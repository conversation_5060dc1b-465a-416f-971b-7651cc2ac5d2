# 🚀 系统自动更新安装指南

## 📋 概述
您的Java项目现在已经配置了完整的网页端自动更新功能！用户可以通过访问 `http://localhost:8080/index` 来进行系统更新。

## 🎯 功能特性
✅ **网页端更新界面** - 美观的现代化UI界面
✅ **实时更新进度** - 显示更新进度和详细日志
✅ **版本检测** - 自动检测当前版本和最新版本
✅ **状态监控** - 实时监控更新状态
✅ **错误处理** - 完善的错误处理和回退机制
✅ **API代理** - 8080端口代理到8078端口的API请求

## 🌐 访问地址

### 主要访问方式
- **更新页面**: http://localhost:8080/index
- **后端API**: http://localhost:8078

### 备用访问方式
- **直接访问后端**: http://localhost:8078/index.html
- **API状态检查**: http://localhost:8078/system/update/status

## 🚀 使用方法

### 1. 启动更新服务
```bash
# 方法1：运行启动脚本
.\start-update-server-simple.bat

# 方法2：直接运行Python服务器
python update_server.py
```

### 2. 确保后端服务运行
```bash
# 启动后端服务（如果没有运行）
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

### 3. 访问更新页面
在浏览器中访问：http://localhost:8080/index

### 4. 执行更新
1. 点击"🔄 刷新状态"检查更新
2. 如果有更新，点击"🚀 开始更新"
3. 等待更新完成，系统会自动重启

## 🔧 技术实现

### 前端部分
- **位置**: `ruoyi-admin/src/main/resources/static/index.html`
- **功能**: 现代化的更新界面，实时进度显示
- **技术**: HTML5 + CSS3 + JavaScript

### 后端API
- **控制器**: `SysUpdateController.java`
- **接口**:
  - `GET /system/update/status` - 获取更新状态
  - `POST /system/update/execute` - 执行更新
  - `GET /system/update/progress` - 获取更新进度

### 代理服务器
- **文件**: `update_server.py`
- **功能**: 8080端口代理到8078端口
- **技术**: Python HTTP Server

## 📁 相关文件

### 更新脚本
- `update-local.bat` - 本地更新脚本（主要）
- `update-api.bat` - API更新脚本（备用）

### 服务启动
- `start-update-server-simple.bat` - 启动8080端口服务
- `update_server.py` - Python代理服务器

### 页面文件
- `ruoyi-admin/src/main/resources/static/index.html` - 更新页面
- `redirect-update.html` - 重定向页面

## 🛠️ 故障排除

### 问题1：8080端口被占用
**解决**: 启动脚本会自动终止占用进程，或手动运行：
```bash
netstat -ano | findstr ":8080"
taskkill /PID [进程ID] /F
```

### 问题2：后端API无法访问
**解决**: 确保后端服务(8078端口)已启动：
```bash
java -jar ruoyi-admin/target/ruoyi-admin.jar
```

### 问题3：更新页面无法显示
**解决**: 检查文件路径，确保 `index.html` 存在：
```bash
# 检查文件是否存在
dir "ruoyi-admin\src\main\resources\static\index.html"
```

### 问题4：Git命令失败
**解决**: 确保Git已安装并在PATH中：
```bash
git --version
```

### 问题5：Maven编译失败
**解决**: 检查Java和Maven环境：
```bash
java -version
mvn -version
```

## 🔒 安全说明

1. **本地访问**: 更新服务只监听本地地址，外部无法直接访问
2. **API代理**: 通过代理方式访问后端API，避免跨域问题
3. **权限检查**: 更新操作需要适当的文件系统权限
4. **备份建议**: 建议在更新前备份重要数据

## 🎉 完成效果

用户现在可以：
1. 访问 http://localhost:8080/index 
2. 看到美观的更新界面
3. 实时查看版本信息
4. 一键执行系统更新
5. 监控更新进度和日志
6. 更新完成后自动重启

## 📞 技术支持

如果在使用过程中遇到问题，请检查：
1. 后端服务是否正常运行(8078端口)
2. Git、Maven、Node.js环境是否配置正确
3. 网络连接是否正常
4. 文件权限是否足够

---
🎊 **恭喜！您的系统自动更新功能已完全配置完成！**
