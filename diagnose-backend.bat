@echo off
chcp 65001 > nul
echo ========================================
echo          后端问题诊断工具
echo ========================================
echo.

echo 🔍 正在诊断后端启动问题...
echo.

REM 检查Java环境
echo 📋 检查Java环境：
java -version > nul 2>&1
if errorlevel 1 (
    echo ❌ Java - 未安装或不在PATH中
    echo 💡 请安装JDK 8+ 并配置环境变量
) else (
    echo ✅ Java - 已安装
    java -version 2>&1 | findstr "version"
)
echo.

REM 检查Maven环境
echo 📋 检查Maven环境：
mvn -version > nul 2>&1
if errorlevel 1 (
    echo ❌ Maven - 未安装或不在PATH中
    echo 💡 请安装Maven并配置环境变量
) else (
    echo ✅ Maven - 已安装
    mvn -version | findstr "Apache Maven"
)
echo.

REM 检查项目结构
echo 📂 检查项目结构：
if exist "pom.xml" (
    echo ✅ 根目录pom.xml - 存在
) else (
    echo ❌ 根目录pom.xml - 缺失
)

if exist "ruoyi-admin\pom.xml" (
    echo ✅ ruoyi-admin\pom.xml - 存在
) else (
    echo ❌ ruoyi-admin\pom.xml - 缺失
)

if exist "ruoyi-system\pom.xml" (
    echo ✅ ruoyi-system\pom.xml - 存在
) else (
    echo ❌ ruoyi-system\pom.xml - 缺失
)

if exist "ruoyi-common\pom.xml" (
    echo ✅ ruoyi-common\pom.xml - 存在
) else (
    echo ❌ ruoyi-common\pom.xml - 缺失
)
echo.

REM 检查关键Java文件
echo 📋 检查关键Java文件：
if exist "ruoyi-system\src\main\java\com\ruoyi\system\domain\SysConfig.java" (
    echo ✅ SysConfig.java - 存在
) else (
    echo ❌ SysConfig.java - 缺失
)

if exist "ruoyi-admin\src\main\java\com\ruoyi\RuoYiApplication.java" (
    echo ✅ RuoYiApplication.java - 存在
) else (
    echo ❌ RuoYiApplication.java - 缺失
)
echo.

REM 检查配置文件
echo 📋 检查配置文件：
if exist "ruoyi-admin\src\main\resources\application.yml" (
    echo ✅ application.yml - 存在
) else (
    echo ❌ application.yml - 缺失
)

if exist "ruoyi-admin\src\main\resources\application-dev.yml" (
    echo ✅ application-dev.yml - 存在
) else (
    echo ❌ application-dev.yml - 缺失
)
echo.

REM 检查编译文件
echo 📋 检查编译状态：
if exist "ruoyi-admin\target\classes" (
    echo ✅ ruoyi-admin编译文件 - 存在
) else (
    echo ❌ ruoyi-admin编译文件 - 缺失，需要编译
)

if exist "ruoyi-system\target\classes" (
    echo ✅ ruoyi-system编译文件 - 存在
) else (
    echo ❌ ruoyi-system编译文件 - 缺失，需要编译
)
echo.

REM 检查端口占用
echo 📋 检查端口占用：
netstat -an | findstr ":8078" > nul
if errorlevel 1 (
    echo ✅ 端口8078 - 空闲
) else (
    echo ⚠️  端口8078 - 被占用
    echo 💡 可能需要关闭其他应用或更改端口
)

netstat -an | findstr ":3306" > nul
if errorlevel 1 (
    echo ❌ 端口3306 - MySQL可能未启动
    echo 💡 请启动MySQL数据库服务
) else (
    echo ✅ 端口3306 - MySQL正在运行
)
echo.

echo 📊 诊断总结：
echo ========================================
echo.
echo 🎯 错误分析：
echo    主要问题：ClassNotFoundException: com.ruoyi.system.domain.SysConfig
echo    可能原因：
echo    1. Maven编译不完整
echo    2. 依赖jar包缺失
echo    3. 类路径配置错误
echo    4. 模块依赖问题
echo.
echo 🔧 建议解决方案：
echo    1. 运行 fix-backend.bat 进行自动修复
echo    2. 手动执行：mvn clean compile install
echo    3. 检查数据库连接配置
echo    4. 确保所有依赖模块正确编译
echo.
echo 🚀 下一步操作：
echo    1. 双击运行：fix-backend.bat
echo    2. 等待编译完成
echo    3. 启动后端服务
echo    4. 检查日志输出
echo.

pause
