@echo off
chcp 65001 >nul
echo ================================================
echo 🚀 启动系统更新服务 - 端口8080
echo ================================================
echo.

echo 🔍 检查8080端口是否可用...
netstat -ano | findstr ":8080" | findstr "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ⚠️  8080端口已被占用，正在终止占用进程...
    for /f "tokens=5" %%i in ('netstat -ano ^| findstr ":8080" ^| findstr "LISTENING"') do (
        echo 终止进程 PID: %%i
        taskkill /PID %%i /F >nul 2>&1
    )
    timeout /t 2 >nul
)

echo 🌐 启动简易HTTP服务器在8080端口...
echo 📱 更新页面地址: http://localhost:8080/index
echo 🔄 API服务地址: http://localhost:8078
echo.
echo ⚠️  请确保后端服务(8078端口)已启动
echo 💡 按Ctrl+C可停止服务
echo ================================================

cd /d "%~dp0"

:: 创建临时Python HTTP服务器脚本
(
echo import http.server
echo import socketserver
echo import webbrowser
echo import os
echo import urllib.request
echo import json
echo from urllib.parse import urlparse, parse_qs
echo.
echo class UpdateHandler^(http.server.SimpleHTTPRequestHandler^):
echo     def do_GET^(self^):
echo         if self.path == '/index' or self.path == '/':
echo             self.send_response^(200^)
echo             self.send_header^('Content-type', 'text/html; charset=utf-8'^)
echo             self.end_headers^(^)
echo             try:
echo                 with open^('ruoyi-admin/src/main/resources/static/index.html', 'r', encoding='utf-8'^) as f:
echo                     content = f.read^(^)
echo                 self.wfile.write^(content.encode^('utf-8'^)^)
echo             except:
echo                 self.wfile.write^(b'Update page not found. Please make sure backend is running.'^)
echo         else:
echo             super^(^).do_GET^(^)
echo.
echo     def do_POST^(self^):
echo         # 代理POST请求到后端API
echo         if self.path.startswith^('/system/update'^):
echo             try:
echo                 content_length = int^(self.headers['Content-Length'] or 0^)
echo                 post_data = self.rfile.read^(content_length^)
echo                 
echo                 # 转发到后端
echo                 backend_url = 'http://localhost:8078' + self.path
echo                 req = urllib.request.Request^(backend_url, data=post_data^)
echo                 req.add_header^('Content-Type', 'application/json'^)
echo                 
echo                 try:
echo                     response = urllib.request.urlopen^(req^)
echo                     response_data = response.read^(^)
echo                     
echo                     self.send_response^(200^)
echo                     self.send_header^('Content-Type', 'application/json'^)
echo                     self.end_headers^(^)
echo                     self.wfile.write^(response_data^)
echo                 except urllib.error.HTTPError as e:
echo                     self.send_response^(e.code^)
echo                     self.send_header^('Content-Type', 'application/json'^)
echo                     self.end_headers^(^)
echo                     error_response = json.dumps^({'code': e.code, 'msg': 'Backend API error: ' + str^(e^)'}^)
echo                     self.wfile.write^(error_response.encode^('utf-8'^)^)
echo             except Exception as e:
echo                 self.send_response^(500^)
echo                 self.send_header^('Content-Type', 'application/json'^)
echo                 self.end_headers^(^)
echo                 error_response = json.dumps^({'code': 500, 'msg': 'Proxy error: ' + str^(e^)'}^)
echo                 self.wfile.write^(error_response.encode^('utf-8'^)^)
echo         else:
echo             self.send_response^(404^)
echo             self.end_headers^(^)
echo.
echo PORT = 8080
echo with socketserver.TCPServer^("", PORT, UpdateHandler^) as httpd:
echo     print^(f"✅ 更新服务已启动: http://localhost:{PORT}/index"^)
echo     print^(f"🔄 后端API代理: http://localhost:8078"^)
echo     print^("🛑 按Ctrl+C停止服务"^)
echo     try:
echo         httpd.serve_forever^(^)
echo     except KeyboardInterrupt:
echo         print^("🛑 服务已停止"^)
) > temp_server.py

echo 🚀 正在启动更新服务...
python temp_server.py

echo.
echo 🧹 清理临时文件...
del temp_server.py >nul 2>&1

echo ================================================
echo 💡 服务已停止
echo ================================================
pause
