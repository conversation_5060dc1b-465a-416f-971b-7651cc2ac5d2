@echo off
echo ===========================================
echo Redis Bean冲突问题修复验证脚本
echo ===========================================
echo.

echo 1. 检查Redis服务状态...
netstat -an | findstr ":6379" >nul
if %errorlevel% == 0 (
    echo    ✓ Redis服务正在运行 (端口6379已监听)
) else (
    echo    ⚠ Redis服务未运行，应用将使用降级配置
)
echo.

echo 2. 修复内容概览:
echo    ✓ 移除了RuoYiApplication中的重复RedisTemplate Bean定义
echo    ✓ 优化了RedisTemplateConfig配置，增加了错误处理
echo    ✓ 添加了RedisFallbackConfig降级配置
echo    ✓ 在application.yml中增加了Bean覆盖允许配置
echo    ✓ 增加了详细的日志输出便于问题排查
echo.

echo 3. 启动应用进行验证...
echo    正在启动RuoYi应用...
echo.

cd /d "%~dp0"
if exist "ruoyi-admin\target\ruoyi-admin.jar" (
    echo    使用JAR包启动...
    java -jar ruoyi-admin\target\ruoyi-admin.jar
) else (
    echo    JAR包不存在，请先编译项目
    echo    运行命令: mvn clean package -DskipTests
    pause
)
