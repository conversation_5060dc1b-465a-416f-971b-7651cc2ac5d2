@echo off
chcp 65001 >nul
echo ================================================
echo 🌐 API自动更新工具
echo ================================================
echo.

echo 📡 正在调用本地更新API...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8078/system/update/local' -Method POST -ContentType 'application/json' -Body '{}' -TimeoutSec 30; Write-Host '✅ API调用成功'; Write-Host $response.Content } catch { Write-Host '❌ API调用失败:' $_.Exception.Message; exit 1 }"

if %errorlevel% neq 0 (
    echo.
    echo ❌ API更新失败，尝试直接执行本地更新...
    echo.
    call update-local.bat
    exit /b %errorlevel%
)

echo.
echo ✅ 更新命令已发送，系统正在后台更新...
echo 💡 请检查服务器日志以确认更新状态
echo ================================================
pause
