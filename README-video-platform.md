# 智能视频制作平台

## 项目概述

基于阿里云IMS（智能媒体服务）的专业视频编辑平台，提供素材管理、AI递进式剪辑、实时渲染等功能。

## 功能特性

### 1. 素材管理系统
- **分类存储**: 按场景类型组织素材库（产品展示、生活方式、企业宣传等）
- **文件夹管理**: 树形结构管理，支持新建、删除、重命名
- **多格式支持**: 视频、图片、音频文件上传和管理
- **标签系统**: 为素材添加标签，便于搜索和分类
- **预览功能**: 快速预览素材内容
- **批量操作**: 支持批量上传、删除、移动

### 2. AI递进式剪辑
- **模板系统**: 预设多种剪辑模板（产品展示、故事叙述、快节奏剪辑等）
- **智能匹配**: AI自动匹配合适的素材和音乐
- **项目管理**: 创建、编辑、复制、删除剪辑项目
- **状态跟踪**: 编辑中、已完成、已发布等状态管理
- **版本控制**: 保存项目历史版本

### 3. 专业编辑器
- **阿里云IMS集成**: 基于阿里云智能媒体服务的Web编辑器
- **时间轴编辑**: 多轨道时间轴，支持精确剪辑
- **实时预览**: 高质量实时预览，支持全屏播放
- **效果处理**: 转场、滤镜、特效等丰富效果
- **文字编辑**: 字幕、标题、动画文字
- **音频处理**: 音频剪辑、混音、降噪

### 4. 渲染任务管理
- **任务队列**: 管理所有渲染任务
- **进度跟踪**: 实时显示渲染进度
- **状态管理**: 等待中、渲染中、已完成、失败等状态
- **结果下载**: 渲染完成后支持下载
- **重试机制**: 失败任务支持重新渲染

## 技术架构

### 前端技术栈
- **Vue.js 2.x**: 主框架
- **Element UI**: UI组件库
- **Vue Router**: 路由管理
- **Axios**: HTTP客户端

### 后端集成
- **阿里云IMS**: 智能媒体服务
- **阿里云OSS**: 对象存储服务
- **阿里云CDN**: 内容分发网络

### 核心组件

#### 1. 视频平台主页面
```
ruoyi-ui/src/views/store/video-platform.vue
```
- 素材管理界面
- AI剪辑项目列表
- 渲染任务管理

#### 2. IMS编辑器集成
```
ruoyi-ui/src/utils/ims-editor-mock.js
```
- 阿里云IMS SDK封装
- 编辑器生命周期管理
- 事件处理和回调

#### 3. 路由配置
```
ruoyi-ui/src/router/index.js
```
- 添加视频平台路由
- 权限控制

## 安装和配置

### 1. 环境要求
- Node.js 14+
- Vue CLI 4+
- 现代浏览器（Chrome、Firefox、Safari）

### 2. 安装依赖
```bash
cd ruoyi-ui
npm install
```

### 3. 阿里云配置
```javascript
// config/ims-config.js
export const IMSConfig = {
  accessKeyId: 'your-access-key-id',
  accessKeySecret: 'your-access-key-secret',
  endpoint: 'https://ims.cn-shanghai.aliyuncs.com',
  region: 'cn-shanghai'
}
```

### 4. 启动开发服务器
```bash
npm run dev
```

## 使用指南

### 1. 素材管理
1. 点击"素材管理"标签页
2. 使用"新建视频库"创建分类文件夹
3. 选择文件夹后点击"上传素材"
4. 为素材添加标签便于管理

### 2. 创建剪辑项目
1. 切换到"AI递进式剪辑"标签页
2. 点击"创建新剪辑模型"
3. 填写项目信息和选择模板类型
4. 点击"创建并打开编辑器"

### 3. 视频编辑
1. 在编辑器中拖拽素材到时间轴
2. 调整素材位置、时长、效果
3. 添加文字、音乐、转场效果
4. 实时预览编辑结果

### 4. 渲染和导出
1. 编辑完成后点击"渲染视频"
2. 配置输出格式和质量
3. 在"渲染任务"标签页查看进度
4. 渲染完成后下载视频文件

## 界面布局

### 素材管理界面
```
┌─────────────────────────────────────────────────────────┐
│ 工具栏: [上传素材] [新建视频库] [管理音乐] [搜索框]        │
├─────────────────┬───────────────────────────────────────┤
│ 文件夹树形结构    │ 素材展示区域                           │
│ ├─产品展示       │ ┌─────┐ ┌─────┐ ┌─────┐              │
│ │ ├─电子产品     │ │素材1│ │素材2│ │素材3│              │
│ │ └─服装配饰     │ └─────┘ └─────┘ └─────┘              │
│ ├─生活方式       │ ┌─────┐ ┌─────┐ ┌─────┐              │
│ └─BGM音乐       │ │素材4│ │素材5│ │素材6│              │
│                 │ └─────┘ └─────┘ └─────┘              │
└─────────────────┴───────────────────────────────────────┘
```

### AI剪辑项目界面
```
┌─────────────────────────────────────────────────────────┐
│ 工具栏: [创建新剪辑模型] [搜索框] [状态筛选]              │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│ │   项目1     │ │   项目2     │ │   项目3     │        │
│ │ [缩略图]    │ │ [缩略图]    │ │ [缩略图]    │        │
│ │ 产品宣传片   │ │ 生活短视频   │ │ 企业介绍    │        │
│ │ [编辑中]    │ │ [已完成]    │ │ [已发布]    │        │
│ │ [打开编辑器] │ │ [打开编辑器] │ │ [打开编辑器] │        │
│ └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

### 阿里云IMS编辑器界面
```
┌─────────────────────────────────────────────────────────┐
│ 编辑器标题栏: [播放] [保存] [导出]                        │
├─────────┬─────────────────────────┬─────────────────────┤
│ 素材库   │      视频预览区域        │     属性面板         │
│ ├─视频   │  ┌─────────────────────┐ │ ┌─基础属性─────────┐ │
│ ├─图片   │  │                     │ │ │ 位置X: [   ]    │ │
│ ├─音频   │  │    [预览画面]       │ │ │ 位置Y: [   ]    │ │
│ └─效果   │  │                     │ │ │ 缩放:  [====]   │ │
│         │  └─────────────────────┘ │ └─────────────────┘ │
│         │  [⏮] [▶️] [⏭] 00:00/03:30 │ ┌─效果─────────────┐ │
│         │                         │ │ [淡入][淡出]     │ │
│         │                         │ │ [缩放][旋转]     │ │
│         │                         │ └─────────────────┘ │
├─────────┴─────────────────────────┴─────────────────────┤
│ 时间轴面板                                               │
│ ┌─视频轨道─────────────────────────────────────────────┐ │
│ │ [片段1] [片段2]     [片段3]                         │ │
│ └─────────────────────────────────────────────────────┘ │
│ ┌─音频轨道─────────────────────────────────────────────┐ │
│ │ [BGM音乐]                                           │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 开发说明

### 扩展功能
1. **自定义模板**: 在`templates/`目录添加新的模板配置
2. **效果插件**: 扩展`effects/`目录添加新的视频效果
3. **素材处理**: 在`utils/`目录添加素材处理工具

### API接口
- `POST /api/materials/upload` - 上传素材
- `GET /api/materials/list` - 获取素材列表
- `POST /api/projects/create` - 创建项目
- `PUT /api/projects/:id` - 更新项目
- `POST /api/render/create` - 创建渲染任务

### 注意事项
1. 确保阿里云账号有足够的IMS服务权限
2. 大文件上传需要配置合适的超时时间
3. 渲染任务可能需要较长时间，建议异步处理
4. 定期清理临时文件和缓存

## 常见问题

**Q: 编辑器加载失败怎么办？**
A: 检查网络连接、阿里云配置、浏览器兼容性

**Q: 素材上传失败？**
A: 检查文件格式、大小限制、OSS配置

**Q: 渲染任务一直等待？**
A: 检查IMS服务状态、账号余额、任务队列

**Q: 如何自定义模板？**
A: 参考`templates/`目录下的示例文件

## 更新日志

### v1.0.0 (2025-01-11)
- 初始版本发布
- 基础素材管理功能
- AI剪辑项目管理
- 阿里云IMS编辑器集成
- 渲染任务管理

## 许可证

MIT License
