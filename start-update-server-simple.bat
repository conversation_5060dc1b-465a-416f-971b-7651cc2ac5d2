@echo off
chcp 65001 >nul
echo ================================================
echo 🚀 启动系统更新服务 - 端口8080
echo ================================================
echo.

echo 🔍 检查8080端口是否可用...
netstat -ano | findstr ":8080" | findstr "LISTENING" >nul
if %errorlevel% equ 0 (
    echo ⚠️  8080端口已被占用，正在终止占用进程...
    for /f "tokens=5" %%i in ('netstat -ano ^| findstr ":8080" ^| findstr "LISTENING"') do (
        echo 终止进程 PID: %%i
        taskkill /PID %%i /F >nul 2>&1
    )
    timeout /t 2 >nul
)

echo 🌐 启动简易HTTP服务器在8080端口...
echo 📱 更新页面地址: http://localhost:8080/index
echo 🔄 API服务地址: http://localhost:8078
echo.
echo ⚠️  请确保后端服务(8078端口)已启动
echo 💡 按Ctrl+C可停止服务
echo.
echo 🛠️  如遇到认证问题，请运行: fix-auth-issues.bat
echo 📋 详细解决方案请查看: UPDATE_FIX_GUIDE.md
echo ================================================

cd /d "%~dp0"
python update_server.py

echo ================================================
echo 💡 服务已停止
echo ================================================
pause
