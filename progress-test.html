<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度条测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            border-radius: 10px;
            transition: width 0.5s ease;
            width: 0%;
            position: relative;
            overflow: hidden;
        }
        
        .progress-fill::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 2s infinite;
        }
        
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #2d3748;
            font-weight: 600;
            font-size: 0.85rem;
            text-shadow: 0 1px 2px rgba(255,255,255,0.8);
            z-index: 10;
        }
        
        .progress-stage {
            text-align: center;
            margin-top: 10px;
            color: #4a5568;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(79, 172, 254, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 进度条测试</h1>
        <p>测试新的进度条效果</p>
        
        <div class="progress-bar" id="progressBar" style="display: block;">
            <div class="progress-fill" id="progressFill"></div>
            <div class="progress-text" id="progressText">0%</div>
        </div>
        <div class="progress-stage" id="progressStage">准备就绪</div>
        
        <button class="btn" onclick="startDemo()">🎬 开始演示</button>
        <button class="btn" onclick="resetDemo()">🔄 重置</button>
    </div>

    <script>
        let demoInterval;
        let currentProgress = 0;
        
        const stages = [
            { progress: 10, text: '🔍 检查系统状态...', color: 'linear-gradient(90deg, #fc8181 0%, #f56565 100%)' },
            { progress: 25, text: '📥 正在拉取最新代码...', color: 'linear-gradient(90deg, #f6e05e 0%, #ecc94b 100%)' },
            { progress: 50, text: '🏗️ 正在编译后端代码...', color: 'linear-gradient(90deg, #63b3ed 0%, #4299e1 100%)' },
            { progress: 75, text: '🎨 正在构建前端资源...', color: 'linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)' },
            { progress: 95, text: '🔧 正在部署更新...', color: 'linear-gradient(90deg, #68d391 0%, #48bb78 100%)' },
            { progress: 100, text: '✅ 更新完成！', color: 'linear-gradient(90deg, #68d391 0%, #48bb78 100%)' }
        ];
        
        function updateProgress(percent, stage = '', color = '') {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const progressStage = document.getElementById('progressStage');
            
            percent = Math.max(0, Math.min(100, percent));
            
            progressFill.style.width = percent + '%';
            progressText.textContent = Math.round(percent) + '%';
            
            if (stage) {
                progressStage.textContent = stage;
            }
            
            if (color) {
                progressFill.style.background = color;
            }
        }
        
        function startDemo() {
            resetDemo();
            currentProgress = 0;
            let stageIndex = 0;
            
            demoInterval = setInterval(() => {
                if (stageIndex < stages.length) {
                    const stage = stages[stageIndex];
                    
                    // 平滑增长到目标进度
                    const targetProgress = stage.progress;
                    if (currentProgress < targetProgress) {
                        currentProgress += 2;
                        updateProgress(
                            Math.min(currentProgress, targetProgress), 
                            stage.text, 
                            stage.color
                        );
                    } else {
                        stageIndex++;
                    }
                } else {
                    clearInterval(demoInterval);
                }
            }, 100);
        }
        
        function resetDemo() {
            if (demoInterval) {
                clearInterval(demoInterval);
            }
            currentProgress = 0;
            updateProgress(0, '准备就绪', 'linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)');
        }
    </script>
</body>
</html>
