@echo off
chcp 65001 >nul
echo ==========================================
echo Node.js 环境检查工具
echo ==========================================
echo.

echo 1. 检查 Node.js 安装位置...
echo.

REM 检查常见的 Node.js 安装路径
set "NODEJS_PATHS=C:\Program Files\nodejs;C:\Program Files (x86)\nodejs;C:\nodejs;%APPDATA%\npm"

for %%p in (%NODEJS_PATHS%) do (
    if exist "%%p\node.exe" (
        echo ✅ 找到 Node.js: %%p\node.exe
        "%%p\node.exe" --version
        echo.
    )
    if exist "%%p\npm.cmd" (
        echo ✅ 找到 npm: %%p\npm.cmd
        "%%p\npm.cmd" --version
        echo.
    )
)

echo 2. 检查环境变量中的 Node.js...
echo.

REM 尝试直接调用
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ node 命令可用
    node --version
) else (
    echo ❌ node 命令不可用
)

npm --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm 命令可用
    npm --version
) else (
    echo ❌ npm 命令不可用
)

echo.
echo 3. 当前 PATH 环境变量:
echo %PATH%
echo.

echo 4. 建议的解决方案:
echo.

REM 检查是否需要修复环境变量
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 需要修复环境变量
    echo.
    echo 解决方案1 - 修复环境变量:
    echo 1. 按 Win+R，输入: sysdm.cpl
    echo 2. 点击"高级"选项卡
    echo 3. 点击"环境变量"按钮
    echo 4. 在"系统变量"中找到"Path"
    echo 5. 点击"编辑"，然后"新建"
    echo 6. 添加: C:\Program Files\nodejs
    echo 7. 点击"确定"保存
    echo 8. 重启命令行窗口
    echo.
    echo 解决方案2 - 使用完整路径:
    echo "C:\Program Files\nodejs\npm.cmd" run dev
    echo.
    echo 解决方案3 - 重新安装 Node.js:
    echo 访问 https://nodejs.org/ 下载最新版本
) else (
    echo ✅ 环境变量配置正确
    echo 可以直接使用: npm run dev
)

echo.
echo 5. 测试前端启动:
echo.

if exist "package.json" (
    echo ✅ 找到 package.json 文件
    echo 可以尝试启动前端服务器
    echo.
    echo 运行以下命令启动:
    if exist "C:\Program Files\nodejs\npm.cmd" (
        echo "C:\Program Files\nodejs\npm.cmd" run dev
    ) else (
        echo npm run dev
    )
) else (
    echo ❌ 未找到 package.json 文件
    echo 请确保在 ruoyi-ui 目录中运行
)

echo.
echo ==========================================
pause
