@echo off
chcp 65001 >nul
echo ==========================================
echo Node.js 环境变量修复工具
echo ==========================================
echo.

echo 请以管理员身份运行此脚本！
echo.

REM 检查是否以管理员身份运行
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 需要管理员权限
    echo.
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo ✅ 管理员权限确认
echo.

echo 1. 查找 Node.js 安装位置...
echo.

set "NODEJS_PATH="
set "FOUND_PATHS="

REM 检查常见路径
for %%p in ("C:\Program Files\nodejs" "C:\Program Files (x86)\nodejs" "C:\nodejs" "%LOCALAPPDATA%\Programs\nodejs") do (
    if exist "%%~p\node.exe" (
        echo ✅ 找到 Node.js: %%~p
        set "NODEJS_PATH=%%~p"
        set "FOUND_PATHS=!FOUND_PATHS!%%~p;"
    )
)

if "%NODEJS_PATH%"=="" (
    echo ❌ 未找到 Node.js 安装
    echo.
    echo 请先安装 Node.js:
    echo 1. 访问 https://nodejs.org/
    echo 2. 下载 LTS 版本
    echo 3. 运行安装程序
    echo 4. 重新运行此脚本
    pause
    exit /b 1
)

echo.
echo 2. 当前系统 PATH:
echo %PATH%
echo.

echo 3. 添加 Node.js 到系统 PATH...
echo.

REM 获取当前系统 PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SYSTEM_PATH=%%b"

REM 检查是否已经在 PATH 中
echo %SYSTEM_PATH% | findstr /i "%NODEJS_PATH%" >nul
if %errorlevel% equ 0 (
    echo ✅ Node.js 路径已在系统 PATH 中
) else (
    echo 添加 %NODEJS_PATH% 到系统 PATH...
    
    REM 添加到系统 PATH
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "%SYSTEM_PATH%;%NODEJS_PATH%" /f
    
    if %errorlevel% equ 0 (
        echo ✅ 成功添加到系统 PATH
    ) else (
        echo ❌ 添加失败
    )
)

echo.
echo 4. 刷新环境变量...
echo.

REM 通知系统环境变量已更改
powershell -Command "[Environment]::SetEnvironmentVariable('Path', [Environment]::GetEnvironmentVariable('Path', 'Machine'), 'Machine')"

echo 5. 测试 Node.js 命令...
echo.

REM 使用完整路径测试
"%NODEJS_PATH%\node.exe" --version
if %errorlevel% equ 0 (
    echo ✅ node.exe 可以运行
) else (
    echo ❌ node.exe 运行失败
)

"%NODEJS_PATH%\npm.cmd" --version
if %errorlevel% equ 0 (
    echo ✅ npm.cmd 可以运行
) else (
    echo ❌ npm.cmd 运行失败
)

echo.
echo ==========================================
echo 修复完成！
echo.
echo 重要提示:
echo 1. 关闭所有命令行窗口
echo 2. 重新打开命令行
echo 3. 测试: npm --version
echo 4. 如果还不行，重启电脑
echo ==========================================
pause
