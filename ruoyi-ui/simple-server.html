<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fly-Cut 服务器启动器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 30px;
        }

        .method {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #3498db;
        }

        .method h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .method p {
            color: #7f8c8d;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .command {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 12px 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            word-break: break-all;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .command:hover {
            background: #34495e;
            transform: translateY(-1px);
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin: 10px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn.success {
            background: #27ae60;
        }

        .btn.success:hover {
            background: #229954;
        }

        .links {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ecf0f1;
        }

        .links a {
            color: #3498db;
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
        }

        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🎬</div>
        <div class="title">Fly-Cut 服务器启动器</div>
        <div class="subtitle">选择一种方式启动 Fly-Cut 本地服务器</div>

        <div id="status" class="status info">
            请选择下面的启动方式之一
        </div>

        <div class="method">
            <h3>方法1: 使用 Live Server (推荐)</h3>
            <p>如果你使用 VS Code，安装 Live Server 扩展，然后右键点击 public/fly-cut/index.html 选择 "Open with Live Server"</p>
            <button class="btn" onclick="openVSCode()">打开 VS Code</button>
        </div>

        <div class="method">
            <h3>方法2: 使用 Python (简单)</h3>
            <p>在命令行中运行以下命令：</p>
            <div class="command" onclick="copyToClipboard(this)">
                cd public/fly-cut && python -m http.server 3001
            </div>
            <button class="btn" onclick="testServer('http://localhost:3001')">测试连接</button>
        </div>

        <div class="method">
            <h3>方法3: 使用 Node.js</h3>
            <p>如果安装了 Node.js，运行：</p>
            <div class="command" onclick="copyToClipboard(this)">
                npx http-server public/fly-cut -p 3001 --cors
            </div>
            <button class="btn" onclick="testServer('http://localhost:3001')">测试连接</button>
        </div>

        <div class="method">
            <h3>方法4: 直接使用在线版本</h3>
            <p>如果本地服务器有问题，可以直接使用在线版本：</p>
            <button class="btn success" onclick="openOnlineVersion()">打开在线版 Fly-Cut</button>
        </div>

        <div class="links">
            <a href="#" onclick="openFlyCutLocal()">测试本地版本</a>
            <a href="#" onclick="checkFiles()">检查文件完整性</a>
            <a href="#" onclick="showHelp()">获取帮助</a>
        </div>
    </div>

    <script>
        function copyToClipboard(element) {
            const text = element.textContent;
            navigator.clipboard.writeText(text).then(() => {
                showStatus('命令已复制到剪贴板！', 'success');
            }).catch(() => {
                showStatus('复制失败，请手动复制命令', 'error');
            });
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function testServer(url) {
            showStatus('正在测试服务器连接...', 'info');
            
            fetch(url)
                .then(response => {
                    if (response.ok) {
                        showStatus('✅ 服务器连接成功！可以使用 Fly-Cut 了', 'success');
                        setTimeout(() => {
                            window.open(url, '_blank');
                        }, 1000);
                    } else {
                        throw new Error('服务器响应错误');
                    }
                })
                .catch(error => {
                    showStatus('❌ 服务器连接失败，请检查服务器是否已启动', 'error');
                });
        }

        function openVSCode() {
            showStatus('请在 VS Code 中安装 Live Server 扩展，然后右键点击 index.html', 'info');
        }

        function openOnlineVersion() {
            const url = 'https://fly-cut.vercel.app/?templateId=demo&templateName=演示模板&source=local';
            window.open(url, '_blank');
            showStatus('已打开在线版 Fly-Cut', 'success');
        }

        function openFlyCutLocal() {
            const url = 'http://localhost:3001/';
            testServer(url);
        }

        function checkFiles() {
            const requiredFiles = [
                'public/fly-cut/index.html',
                'public/fly-cut/assets/index-85e1c866.js',
                'public/fly-cut/assets/index-82bc1b7e.css'
            ];
            
            showStatus('文件检查功能需要服务器支持，请手动检查文件是否存在', 'info');
        }

        function showHelp() {
            const helpText = `
Fly-Cut 启动帮助：

1. 最简单的方法是使用 VS Code 的 Live Server 扩展
2. 如果有 Python，运行: python -m http.server 3001
3. 如果有 Node.js，运行: npx http-server public/fly-cut -p 3001 --cors
4. 如果都不行，使用在线版本

常见问题：
- 页面空白：通常是 MIME 类型问题，需要 HTTP 服务器
- 端口被占用：更改端口号或关闭占用程序
- 网络错误：检查防火墙和网络设置
            `;
            
            alert(helpText);
        }

        // 页面加载时检查本地服务器
        window.addEventListener('load', () => {
            setTimeout(() => {
                testServer('http://localhost:3001');
            }, 1000);
        });
    </script>
</body>
</html>
