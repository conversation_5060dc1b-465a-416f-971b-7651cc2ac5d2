@echo off
chcp 65001 >nul
title 项目一键启动工具
color 0A

echo.
echo     ██████╗ ██╗   ██╗ ██████╗ ██╗   ██╗██╗
echo     ██╔══██╗██║   ██║██╔═══██╗╚██╗ ██╔╝██║
echo     ██████╔╝██║   ██║██║   ██║ ╚████╔╝ ██║
echo     ██╔══██╗██║   ██║██║   ██║  ╚██╔╝  ██║
echo     ██║  ██║╚██████╔╝╚██████╔╝   ██║   ██║
echo     ╚═╝  ╚═╝ ╚═════╝  ╚═════╝    ╚═╝   ╚═╝
echo.
echo ==========================================
echo           项目一键启动工具
echo ==========================================
echo.

:menu
echo 请选择要启动的服务:
echo.
echo [1] 启动前端服务 (Vue)
echo [2] 启动 Fly-Cut 本地服务器
echo [3] 检查 Node.js 环境
echo [4] 启动所有服务
echo [5] 打开项目目录
echo [0] 退出
echo.
set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" goto start_frontend
if "%choice%"=="2" goto start_flycut
if "%choice%"=="3" goto check_env
if "%choice%"=="4" goto start_all
if "%choice%"=="5" goto open_dirs
if "%choice%"=="0" goto exit
echo 无效选择，请重新输入
goto menu

:start_frontend
echo.
echo ==========================================
echo 启动前端服务
echo ==========================================
echo.

if not exist "package.json" (
    echo ❌ 错误: 未找到 package.json 文件
    echo 请确保脚本在 ruoyi-ui 目录中
    pause
    goto menu
)

echo 正在启动前端服务器...
echo.

REM 尝试使用引号包围的路径
"C:\Program Files\nodejs\npm.cmd" run dev
if %errorlevel% neq 0 (
    echo.
    echo 启动失败，尝试其他方法...
    npm run dev
)

pause
goto menu

:start_flycut
echo.
echo ==========================================
echo 启动 Fly-Cut 本地服务器
echo ==========================================
echo.

if not exist "public\fly-cut\index.html" (
    echo ❌ 错误: 未找到 fly-cut 文件
    echo 请确保 public/fly-cut/ 目录存在
    pause
    goto menu
)

echo 选择启动方式:
echo [1] Python HTTP 服务器 (推荐)
echo [2] Node.js http-server
echo [3] 返回主菜单
echo.
set /p flycut_choice=请选择 (1-3): 

if "%flycut_choice%"=="1" goto start_python_server
if "%flycut_choice%"=="2" goto start_node_server
if "%flycut_choice%"=="3" goto menu
echo 无效选择
goto start_flycut

:start_python_server
echo.
echo 使用 Python 启动 Fly-Cut 服务器...
cd public\fly-cut
python -m http.server 3001
cd ..\..
pause
goto menu

:start_node_server
echo.
echo 使用 Node.js 启动 Fly-Cut 服务器...
cd public\fly-cut
"C:\Program Files\nodejs\npx.cmd" http-server -p 3001 --cors
cd ..\..
pause
goto menu

:check_env
echo.
echo ==========================================
echo 检查 Node.js 环境
echo ==========================================
echo.

call 检查环境.bat
goto menu

:start_all
echo.
echo ==========================================
echo 启动所有服务
echo ==========================================
echo.

echo 1. 启动 Fly-Cut 服务器...
start "Fly-Cut Server" cmd /k "cd public\fly-cut && python -m http.server 3001"

timeout /t 3 /nobreak >nul

echo 2. 启动前端服务器...
start "Frontend Server" cmd /k ""C:\Program Files\nodejs\npm.cmd" run dev"

echo.
echo ✅ 所有服务启动完成！
echo.
echo 📱 前端地址: http://localhost:8081
echo 🎬 Fly-Cut: http://localhost:3001
echo.
pause
goto menu

:open_dirs
echo.
echo 打开项目相关目录...
echo.

REM 打开当前目录
start "" "%CD%"

REM 打开 fly-cut 目录
if exist "public\fly-cut" (
    start "" "%CD%\public\fly-cut"
)

echo ✅ 目录已打开
pause
goto menu

:exit
echo.
echo 感谢使用项目启动工具！
echo.
timeout /t 2 /nobreak >nul
exit

:error
echo.
echo ❌ 发生错误，请检查配置
pause
goto menu
