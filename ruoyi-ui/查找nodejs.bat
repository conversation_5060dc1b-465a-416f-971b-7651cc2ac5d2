@echo off
echo ==========================================
echo Finding Node.js Installation
echo ==========================================
echo.

echo 1. Searching common installation paths...
echo.

REM Check common installation paths
set "SEARCH_PATHS=C:\Program Files\nodejs;C:\Program Files (x86)\nodejs;C:\nodejs;%LOCALAPPDATA%\Programs\nodejs;%APPDATA%\npm"

for %%p in (%SEARCH_PATHS%) do (
    if exist "%%p" (
        echo [FOUND] Directory: %%p
        if exist "%%p\node.exe" (
            echo   - node.exe: EXISTS
            "%%p\node.exe" --version 2>nul && echo   - Version: && "%%p\node.exe" --version
        ) else (
            echo   - node.exe: NOT FOUND
        )
        if exist "%%p\npm.cmd" (
            echo   - npm.cmd: EXISTS
        ) else (
            echo   - npm.cmd: NOT FOUND
        )
        echo.
    )
)

echo 2. Searching entire C drive for node.exe...
echo (This may take a few minutes)
echo.

REM Search entire C drive for node.exe
for /f "delims=" %%i in ('dir /s /b C:\node.exe 2^>nul') do (
    echo [FOUND] node.exe: %%i
    "%%i" --version 2>nul && echo   Version: && "%%i" --version
    echo.
)

echo 3. Searching for npm.cmd...
echo.

for /f "delims=" %%i in ('dir /s /b C:\npm.cmd 2^>nul') do (
    echo [FOUND] npm.cmd: %%i
    echo.
)

echo 4. Current PATH environment variable:
echo.
echo PATH content:
echo %PATH%
echo.

echo 5. User environment variables:
echo.
reg query "HKCU\Environment" /v PATH 2>nul
echo.

echo 6. System environment variables:
echo.
reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2>nul
echo.

echo ==========================================
echo Search completed!
echo.
echo If Node.js was found, note the full path
echo Then manually add it to environment variables
echo ==========================================
pause
