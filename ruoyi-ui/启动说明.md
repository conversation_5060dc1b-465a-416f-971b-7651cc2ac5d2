# 🚀 项目启动说明

## 问题解决方案

### 1. 前端启动问题 (npm run dev 失败)

**原因**: Node.js 环境路径问题

**解决方案**:
```bash
# 方法1: 使用完整路径 (推荐)
# 找到你的 Node.js 安装路径，例如：
C:\Program Files\nodejs\npm.cmd run dev

# 方法2: 修复环境变量
# 将 Node.js 路径添加到系统 PATH 环境变量中
# 路径通常是: C:\Program Files\nodejs\

# 方法3: 使用 yarn (如果已安装)
yarn dev

# 方法4: 重新安装 Node.js
# 从 https://nodejs.org/ 下载最新版本重新安装
```

### 2. Fly-Cut 页面空白问题

**原因**: 需要 HTTP 服务器来正确处理 JavaScript 模块的 MIME 类型

**解决方案** (选择其中一种):

#### 方案A: 使用 Python (最简单)
```bash
# 1. 打开命令行，进入项目目录
cd E:\ry-vue-flowable-xg-main\ruoyi-ui\public\fly-cut

# 2. 启动 Python HTTP 服务器
python -m http.server 3001

# 3. 浏览器访问: http://localhost:3001/
```

#### 方案B: 使用 VS Code Live Server (推荐)
```bash
# 1. 在 VS Code 中安装 "Live Server" 扩展
# 2. 右键点击 public/fly-cut/index.html
# 3. 选择 "Open with Live Server"
```

#### 方案C: 使用 Node.js http-server
```bash
# 1. 全局安装 http-server
npm install -g http-server

# 2. 启动服务器
cd ruoyi-ui/public/fly-cut
http-server -p 3001 --cors

# 3. 访问: http://localhost:3001/
```

#### 方案D: 直接使用在线版本
```
直接访问: https://fly-cut.vercel.app/
```

## 🎯 完整启动流程

### 1. 启动后台服务
```bash
cd E:\ry-vue-flowable-xg-main
mvn spring-boot:run -pl ruoyi-admin
```

### 2. 启动前端服务
```bash
cd ruoyi-ui

# 尝试以下方法之一:
npm run dev
# 或
C:\Program Files\nodejs\npm.cmd run dev
# 或
yarn dev
```

### 3. 启动 Fly-Cut 服务器 (可选)
```bash
# 选择上面任一方案启动 fly-cut 服务器
```

### 4. 访问应用
- 前端: http://localhost:8081 (或你指定的端口)
- 后台: http://localhost:8080
- Fly-Cut: http://localhost:3001 (如果启动了本地服务器)

## 🔧 故障排除

### 问题1: 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :8081
netstat -ano | findstr :3001

# 杀死占用进程
taskkill /PID <进程ID> /F
```

### 问题2: Node.js 命令不识别
```bash
# 检查 Node.js 是否安装
node --version
npm --version

# 如果没有安装，从官网下载安装
# https://nodejs.org/
```

### 问题3: Fly-Cut 页面空白
```bash
# 检查浏览器控制台错误 (F12)
# 通常是 MIME 类型错误，需要 HTTP 服务器

# 最简单的解决方案：
cd ruoyi-ui/public/fly-cut
python -m http.server 3001
```

## 📱 使用建议

### 开发环境 (推荐)
1. **前端**: 使用 `npm run dev` 或 `yarn dev`
2. **Fly-Cut**: 使用 VS Code Live Server 或 Python HTTP 服务器
3. **后台**: 使用 `mvn spring-boot:run`

### 生产环境
1. **前端**: 构建后部署到 Web 服务器
2. **Fly-Cut**: 集成到主应用中，或使用在线版本
3. **后台**: 打包为 JAR 文件部署

### 快速测试
1. **前端**: 直接使用在线版本或已部署的版本
2. **Fly-Cut**: 使用在线版本 https://fly-cut.vercel.app/
3. **后台**: 使用已部署的后台服务

## 🎬 Fly-Cut 使用说明

现在项目中的 Fly-Cut 集成支持：

1. **智能选择**: 用户点击编辑时会弹出选择对话框
2. **在线版本**: 稳定可靠，推荐使用
3. **本地版本**: 需要启动本地服务器，性能更好
4. **自动检测**: 自动检测本地服务器状态
5. **友好提示**: 提供详细的启动指导

### 使用流程:
1. 在模板管理页面点击"编辑模板"
2. 选择启动方式（在线版本或本地版本）
3. 系统自动打开对应的编辑器
4. 开始视频编辑

这样设计确保了在任何环境下都能正常使用 Fly-Cut 编辑器！
