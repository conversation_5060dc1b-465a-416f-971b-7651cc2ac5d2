@echo off
echo ===========================================
echo Fly-Cut 构建脚本
echo ===========================================
echo.

echo 1. 切换到 Node.js 18 环境...
call nvm use 18
if %errorlevel% neq 0 (
    echo ❌ 切换到 Node.js 18 失败
    pause
    exit /b 1
)

echo.
echo 2. 进入 fly-cut 源码目录...
cd fly-cut-source
if %errorlevel% neq 0 (
    echo ❌ fly-cut-source 目录不存在
    pause
    exit /b 1
)

echo.
echo 3. 安装 fly-cut 依赖...
npm install --legacy-peer-deps
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 4. 构建 fly-cut...
npm run build-only
if %errorlevel% neq 0 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo 5. 复制构建文件到 public 目录...
if exist "..\public\fly-cut" (
    rmdir /s /q "..\public\fly-cut"
)
mkdir "..\public\fly-cut"
xcopy "dist\*" "..\public\fly-cut\" /s /e /y

echo.
echo 6. 切换回 Node.js 16 环境...
cd ..
call nvm use 16

echo.
echo ✅ Fly-Cut 构建完成！
echo 📁 文件位置: public/fly-cut/
echo 🌐 访问地址: http://localhost:8080/fly-cut-editor.html
echo.
pause
