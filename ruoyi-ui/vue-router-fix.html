<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Vue路由问题修复工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #2c3e50;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .section {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .section h2 {
            color: #34495e;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .status-card {
            display: flex;
            align-items: center;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #219a52;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .btn-warning {
            background: #f39c12;
        }
        
        .btn-warning:hover {
            background: #d68910;
        }
        
        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .nav-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .nav-card:hover::before {
            left: 100%;
        }
        
        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .nav-card h3 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .nav-card p {
            margin: 0;
            opacity: 0.9;
        }
        
        .console-output {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
            border: 2px solid #333;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .problem-solution {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        
        .problem-solution h3 {
            color: #007bff;
            margin-bottom: 10px;
        }
        
        .problem-solution ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .problem-solution li {
            padding: 5px 0;
            position: relative;
            padding-left: 25px;
        }
        
        .problem-solution li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        
        .fix-steps {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        
        .fix-steps h3 {
            color: #28a745;
            margin-bottom: 15px;
        }
        
        .fix-steps ol {
            counter-reset: step-counter;
            list-style: none;
        }
        
        .fix-steps li {
            counter-increment: step-counter;
            padding: 10px 0;
            position: relative;
            padding-left: 40px;
        }
        
        .fix-steps li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 10px;
            background: #28a745;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Vue路由问题修复工具</h1>
        
        <div class="section">
            <h2>🎯 问题诊断</h2>
            <div class="problem-solution">
                <h3>🔍 发现的问题</h3>
                <ul>
                    <li>Vue组件渲染失败：模板或渲染函数未定义</li>
                    <li>路由配置错误：组件导入路径缺少.vue扩展名</li>
                    <li>权限拦截器问题：可能影响开发环境导航</li>
                    <li>TypeScript类型检查问题：影响组件编译</li>
                </ul>
            </div>
            
            <div class="fix-steps">
                <h3>🛠️ 修复步骤</h3>
                <ol>
                    <li>已修复路由配置：为所有组件导入添加.vue扩展名</li>
                    <li>已更新权限拦截器：开发环境跳过权限检查</li>
                    <li>已创建调试工具：帮助诊断剩余问题</li>
                    <li>需要重启开发服务器：让修改生效</li>
                </ol>
            </div>
        </div>
        
        <div class="section">
            <h2>🖥️ 服务器状态</h2>
            <div id="server-status" class="status-card status-info">
                <div class="loading"></div>
                <span style="margin-left: 15px;">正在检查服务器状态...</span>
            </div>
            <button class="btn btn-success" onclick="restartServer()">🔄 重启开发服务器</button>
            <button class="btn" onclick="checkServerStatus()">🔍 检查服务器状态</button>
        </div>
        
        <div class="section">
            <h2>🧭 路由测试</h2>
            <div class="nav-grid">
                <a href="http://localhost:8081/" class="nav-card" target="_blank">
                    <h3>🏠 工作台</h3>
                    <p>主页面</p>
                </a>
                <a href="http://localhost:8081/agent/list" class="nav-card" target="_blank">
                    <h3>👥 代理列表</h3>
                    <p>代理管理</p>
                </a>
                <a href="http://localhost:8081/merchant/list" class="nav-card" target="_blank">
                    <h3>🏪 商家列表</h3>
                    <p>商家管理</p>
                </a>
                <a href="http://localhost:8081/store/index" class="nav-card" target="_blank">
                    <h3>🏪 店铺概览</h3>
                    <p>店铺首页</p>
                </a>
                <a href="http://localhost:8081/store/bangding" class="nav-card" target="_blank">
                    <h3>🔗 绑定平台</h3>
                    <p>平台绑定</p>
                </a>
                <a href="http://localhost:8081/store/shipin" class="nav-card" target="_blank">
                    <h3>🎥 视频管理</h3>
                    <p>视频内容</p>
                </a>
                <a href="http://localhost:8081/store/daka" class="nav-card" target="_blank">
                    <h3>⏰ 打卡管理</h3>
                    <p>打卡系统</p>
                </a>
                <a href="http://localhost:8081/finance/overview" class="nav-card" target="_blank">
                    <h3>💰 财务概览</h3>
                    <p>财务管理</p>
                </a>
            </div>
        </div>
        
        <div class="section">
            <h2>🔄 自动化测试</h2>
            <button class="btn btn-success" onclick="runFullTest()">🚀 运行完整测试</button>
            <button class="btn btn-warning" onclick="testRoutes()">🧪 测试路由</button>
            <button class="btn btn-danger" onclick="clearResults()">🧹 清除结果</button>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            
            <div id="test-results" style="margin-top: 20px;"></div>
        </div>
        
        <div class="section">
            <h2>📊 实时控制台</h2>
            <div id="console-output" class="console-output">
                [系统] 控制台输出将显示在这里...
            </div>
            <button class="btn" onclick="clearConsole()">🧹 清除控制台</button>
        </div>
        
        <div class="section">
            <h2>🔧 快速修复命令</h2>
            <div class="problem-solution">
                <h3>💻 执行以下命令</h3>
                <ul>
                    <li>停止当前服务器：Ctrl+C</li>
                    <li>重新启动：npm run dev</li>
                    <li>或者使用：vue-cli-service serve</li>
                    <li>检查端口：服务器应该在8081端口运行</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        let consoleOutput = document.getElementById('console-output');
        let testResults = document.getElementById('test-results');
        let progressBar = document.getElementById('progress-fill');
        
        // 控制台输出功能
        function addToConsole(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'INFO': '#00ff00',
                'ERROR': '#ff4444',
                'WARN': '#ffaa00',
                'SUCCESS': '#44ff44'
            };
            
            const color = colors[type] || '#00ff00';
            consoleOutput.innerHTML += `<div style="color: ${color}; margin: 2px 0;">[${timestamp}] [${type}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '[系统] 控制台已清除...';
        }
        
        // 服务器状态检查
        function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            statusElement.innerHTML = '<div class="loading"></div><span style="margin-left: 15px;">正在检查服务器状态...</span>';
            statusElement.className = 'status-card status-info';
            
            addToConsole('INFO', '开始检查服务器状态...');
            
            fetch('http://localhost:8081/')
                .then(response => {
                    if (response.ok) {
                        statusElement.innerHTML = '✅ 服务器运行正常 (HTTP ' + response.status + ')';
                        statusElement.className = 'status-card status-success';
                        addToConsole('SUCCESS', '服务器状态正常');
                    } else {
                        statusElement.innerHTML = '⚠️ 服务器响应异常 (HTTP ' + response.status + ')';
                        statusElement.className = 'status-card status-warning';
                        addToConsole('WARN', '服务器响应异常: ' + response.status);
                    }
                })
                .catch(error => {
                    statusElement.innerHTML = '❌ 服务器连接失败: ' + error.message;
                    statusElement.className = 'status-card status-error';
                    addToConsole('ERROR', '服务器连接失败: ' + error.message);
                });
        }
        
        // 路由测试
        function testRoutes() {
            const routes = [
                { path: '/', name: '工作台' },
                { path: '/agent/list', name: '代理列表' },
                { path: '/merchant/list', name: '商家列表' },
                { path: '/store/index', name: '店铺概览' },
                { path: '/store/bangding', name: '绑定平台' },
                { path: '/store/shipin', name: '视频管理' },
                { path: '/store/daka', name: '打卡管理' },
                { path: '/finance/overview', name: '财务概览' }
            ];
            
            addToConsole('INFO', '开始测试路由...');
            testResults.innerHTML = '<h3>🧪 路由测试结果</h3>';
            
            let completed = 0;
            let results = [];
            
            routes.forEach((route, index) => {
                fetch('http://localhost:8081' + route.path)
                    .then(response => {
                        const status = response.ok ? 'SUCCESS' : 'ERROR';
                        const icon = response.ok ? '✅' : '❌';
                        const statusText = response.ok ? '正常' : '错误';
                        
                        results[index] = `
                            <div class="status-card ${response.ok ? 'status-success' : 'status-error'}">
                                ${icon} ${route.name} (${route.path}) - ${statusText} (${response.status})
                            </div>
                        `;
                        
                        addToConsole(status, `${route.name}: ${statusText}`);
                    })
                    .catch(error => {
                        results[index] = `
                            <div class="status-card status-error">
                                ❌ ${route.name} (${route.path}) - 连接失败: ${error.message}
                            </div>
                        `;
                        addToConsole('ERROR', `${route.name}: 连接失败`);
                    })
                    .finally(() => {
                        completed++;
                        const progress = (completed / routes.length) * 100;
                        progressBar.style.width = progress + '%';
                        
                        if (completed === routes.length) {
                            testResults.innerHTML += results.join('');
                            addToConsole('INFO', '路由测试完成');
                        }
                    });
            });
        }
        
        // 完整测试
        function runFullTest() {
            addToConsole('INFO', '开始运行完整测试...');
            progressBar.style.width = '0%';
            
            setTimeout(() => {
                checkServerStatus();
                progressBar.style.width = '25%';
            }, 500);
            
            setTimeout(() => {
                testRoutes();
                progressBar.style.width = '50%';
            }, 1500);
            
            setTimeout(() => {
                addToConsole('INFO', '检查Vue开发者工具...');
                progressBar.style.width = '75%';
            }, 3000);
            
            setTimeout(() => {
                addToConsole('SUCCESS', '完整测试完成！');
                progressBar.style.width = '100%';
            }, 4000);
        }
        
        // 重启服务器
        function restartServer() {
            addToConsole('INFO', '请在终端中执行以下命令重启服务器：');
            addToConsole('INFO', '1. 按 Ctrl+C 停止当前服务器');
            addToConsole('INFO', '2. 运行：npm run dev');
            addToConsole('INFO', '3. 等待编译完成后测试路由');
            
            // 模拟重启过程
            let countdown = 5;
            const interval = setInterval(() => {
                addToConsole('INFO', `建议等待 ${countdown} 秒后重新测试...`);
                countdown--;
                if (countdown === 0) {
                    clearInterval(interval);
                    addToConsole('SUCCESS', '可以开始测试了！');
                }
            }, 1000);
        }
        
        function clearResults() {
            testResults.innerHTML = '';
            progressBar.style.width = '0%';
            addToConsole('INFO', '测试结果已清除');
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', function() {
            addToConsole('INFO', '修复工具已加载');
            addToConsole('INFO', '建议：1. 先重启服务器，2. 然后运行完整测试');
            setTimeout(checkServerStatus, 1000);
        });
        
        // 监听错误
        window.addEventListener('error', function(e) {
            addToConsole('ERROR', `页面错误: ${e.message}`);
        });
    </script>
</body>
</html>
