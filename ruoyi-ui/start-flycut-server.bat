@echo off
echo ==========================================
echo 启动 Fly-Cut 服务器
echo ==========================================
echo.

echo 1. 设置 Node.js 环境...
set PATH=C:\chajian\nvm;C:\nvm4w\nodejs;%PATH%

echo 2. 检查 Node.js 版本...
node --version
if %errorlevel% neq 0 (
    echo ❌ Node.js 未找到，请检查安装
    pause
    exit /b 1
)

echo.
echo 3. 启动 Fly-Cut 服务器...
echo 🚀 正在启动服务器...
echo 📱 访问地址: http://localhost:3001/fly-cut/
echo.

node fly-cut-server.js
