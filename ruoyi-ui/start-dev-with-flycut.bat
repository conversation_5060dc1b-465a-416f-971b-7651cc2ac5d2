@echo off
echo ===========================================
echo 完整开发环境启动脚本
echo ===========================================
echo.

echo 1. 确保使用 Node.js 16 环境...
call nvm use 16
if %errorlevel% neq 0 (
    echo ❌ 切换到 Node.js 16 失败
    pause
    exit /b 1
)

echo.
echo 2. 检查 fly-cut 是否已构建...
if not exist "public\fly-cut\index.html" (
    echo ⚠️  fly-cut 未构建，正在构建...
    call build-fly-cut.bat
    if %errorlevel% neq 0 (
        echo ❌ fly-cut 构建失败
        pause
        exit /b 1
    )
) else (
    echo ✅ fly-cut 已存在
)

echo.
echo 3. 启动 Vue 开发服务器...
echo 🚀 正在启动开发服务器...
echo 📱 主项目: http://localhost:8080
echo 🎬 Fly-Cut编辑器: http://localhost:8080/fly-cut-editor.html
echo.

npm run dev
