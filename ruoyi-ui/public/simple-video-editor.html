<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简易视频编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #fff;
            overflow: hidden;
        }

        .editor-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .header {
            background: #2d2d2d;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #444;
        }

        .back-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .back-btn:hover {
            background: #0056b3;
        }

        .title {
            font-size: 18px;
            font-weight: 600;
        }

        .export-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .export-btn:hover {
            background: #1e7e34;
        }

        .export-btn:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .main-content {
            flex: 1;
            display: flex;
        }

        .preview-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #000;
        }

        .video-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .video-preview {
            max-width: 100%;
            max-height: 100%;
            background: #333;
        }

        .controls {
            background: #2d2d2d;
            padding: 15px;
            border-top: 1px solid #444;
        }

        .control-row {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }

        .control-row:last-child {
            margin-bottom: 0;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .file-input {
            display: none;
        }

        .slider {
            flex: 1;
            margin: 0 10px;
        }

        .text-input {
            background: #444;
            color: white;
            border: 1px solid #666;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 12px;
        }

        .sidebar {
            width: 300px;
            background: #2d2d2d;
            border-left: 1px solid #444;
            padding: 20px;
            overflow-y: auto;
        }

        .section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #ccc;
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-content {
            background: #2d2d2d;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #444;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .canvas-container {
            position: relative;
            display: inline-block;
        }

        .text-overlay {
            position: absolute;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            pointer-events: none;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <!-- 头部 -->
        <div class="header">
            <button class="back-btn" onclick="goBack()">
                ← 返回
            </button>
            <div class="title">简易视频编辑器</div>
            <button class="export-btn" id="exportBtn" onclick="exportVideo()" disabled>
                导出视频
            </button>
        </div>

        <!-- 主内容 -->
        <div class="main-content">
            <!-- 预览区域 -->
            <div class="preview-area">
                <div class="video-container">
                    <div class="canvas-container">
                        <video id="videoPreview" class="video-preview" controls>
                            <source src="" type="video/mp4">
                            您的浏览器不支持视频播放
                        </video>
                        <div id="textOverlay" class="text-overlay hidden"></div>
                    </div>
                </div>

                <!-- 控制面板 -->
                <div class="controls">
                    <div class="control-row">
                        <button class="btn" onclick="document.getElementById('videoInput').click()">
                            选择视频
                        </button>
                        <input type="file" id="videoInput" class="file-input" accept="video/*">
                        
                        <button class="btn" onclick="document.getElementById('audioInput').click()">
                            添加音频
                        </button>
                        <input type="file" id="audioInput" class="file-input" accept="audio/*">
                        
                        <button class="btn" id="playBtn" onclick="togglePlay()" disabled>
                            播放
                        </button>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 文本设置 -->
                <div class="section">
                    <div class="section-title">文本设置</div>
                    <div class="control-row">
                        <input type="text" id="textInput" class="text-input" placeholder="输入文本" style="flex: 1;">
                    </div>
                    <div class="control-row">
                        <label style="min-width: 60px;">字体大小:</label>
                        <input type="range" id="fontSizeSlider" class="slider" min="12" max="72" value="24">
                        <span id="fontSizeValue">24px</span>
                    </div>
                    <div class="control-row">
                        <label style="min-width: 60px;">颜色:</label>
                        <input type="color" id="textColor" value="#ffffff">
                    </div>
                    <div class="control-row">
                        <button class="btn" onclick="addText()">添加文本</button>
                        <button class="btn" onclick="removeText()">移除文本</button>
                    </div>
                </div>

                <!-- 视频设置 -->
                <div class="section">
                    <div class="section-title">视频设置</div>
                    <div class="control-row">
                        <label style="min-width: 60px;">亮度:</label>
                        <input type="range" id="brightnessSlider" class="slider" min="0" max="200" value="100">
                        <span id="brightnessValue">100%</span>
                    </div>
                    <div class="control-row">
                        <label style="min-width: 60px;">对比度:</label>
                        <input type="range" id="contrastSlider" class="slider" min="0" max="200" value="100">
                        <span id="contrastValue">100%</span>
                    </div>
                    <div class="control-row">
                        <label style="min-width: 60px;">饱和度:</label>
                        <input type="range" id="saturationSlider" class="slider" min="0" max="200" value="100">
                        <span id="saturationValue">100%</span>
                    </div>
                </div>

                <!-- 音频设置 -->
                <div class="section">
                    <div class="section-title">音频设置</div>
                    <div class="control-row">
                        <label style="min-width: 60px;">音量:</label>
                        <input type="range" id="volumeSlider" class="slider" min="0" max="100" value="50">
                        <span id="volumeValue">50%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="loading hidden">
        <div class="loading-content">
            <div class="spinner"></div>
            <div>正在处理视频，请稍候...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let videoElement = null;
        let audioElement = null;
        let isPlaying = false;

        // 获取URL参数
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                templateId: params.get('templateId') || '',
                templateName: decodeURIComponent(params.get('templateName') || ''),
                source: params.get('source') || ''
            };
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const params = getUrlParams();
            if (params.templateName) {
                document.querySelector('.title').textContent = `编辑: ${params.templateName}`;
            }

            initializeEditor();
        });

        function initializeEditor() {
            videoElement = document.getElementById('videoPreview');
            
            // 视频输入处理
            document.getElementById('videoInput').addEventListener('change', handleVideoUpload);
            document.getElementById('audioInput').addEventListener('change', handleAudioUpload);
            
            // 滑块事件
            setupSliders();
            
            // 文本输入事件
            document.getElementById('textInput').addEventListener('input', updateTextPreview);
        }

        function setupSliders() {
            // 字体大小滑块
            const fontSizeSlider = document.getElementById('fontSizeSlider');
            const fontSizeValue = document.getElementById('fontSizeValue');
            fontSizeSlider.addEventListener('input', function() {
                fontSizeValue.textContent = this.value + 'px';
                updateTextPreview();
            });

            // 视频滤镜滑块
            const brightnessSlider = document.getElementById('brightnessSlider');
            const brightnessValue = document.getElementById('brightnessValue');
            brightnessSlider.addEventListener('input', function() {
                brightnessValue.textContent = this.value + '%';
                updateVideoFilters();
            });

            const contrastSlider = document.getElementById('contrastSlider');
            const contrastValue = document.getElementById('contrastValue');
            contrastSlider.addEventListener('input', function() {
                contrastValue.textContent = this.value + '%';
                updateVideoFilters();
            });

            const saturationSlider = document.getElementById('saturationSlider');
            const saturationValue = document.getElementById('saturationValue');
            saturationSlider.addEventListener('input', function() {
                saturationValue.textContent = this.value + '%';
                updateVideoFilters();
            });

            // 音量滑块
            const volumeSlider = document.getElementById('volumeSlider');
            const volumeValue = document.getElementById('volumeValue');
            volumeSlider.addEventListener('input', function() {
                volumeValue.textContent = this.value + '%';
                if (videoElement) {
                    videoElement.volume = this.value / 100;
                }
            });
        }

        function handleVideoUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const url = URL.createObjectURL(file);
                videoElement.src = url;
                videoElement.load();
                
                document.getElementById('playBtn').disabled = false;
                document.getElementById('exportBtn').disabled = false;
                
                // 设置初始音量
                videoElement.volume = document.getElementById('volumeSlider').value / 100;
            }
        }

        function handleAudioUpload(event) {
            const file = event.target.files[0];
            if (file) {
                // 创建音频元素（这里简化处理，实际项目中需要音频混合）
                if (audioElement) {
                    audioElement.pause();
                }
                audioElement = new Audio(URL.createObjectURL(file));
                audioElement.loop = true;
                console.log('音频已加载（简化版本，实际需要音频混合功能）');
            }
        }

        function togglePlay() {
            if (isPlaying) {
                videoElement.pause();
                if (audioElement) audioElement.pause();
                document.getElementById('playBtn').textContent = '播放';
                isPlaying = false;
            } else {
                videoElement.play();
                if (audioElement) audioElement.play();
                document.getElementById('playBtn').textContent = '暂停';
                isPlaying = true;
            }
        }

        function addText() {
            const textOverlay = document.getElementById('textOverlay');
            const textInput = document.getElementById('textInput');
            
            if (textInput.value.trim()) {
                textOverlay.textContent = textInput.value;
                textOverlay.classList.remove('hidden');
                updateTextPreview();
            }
        }

        function removeText() {
            const textOverlay = document.getElementById('textOverlay');
            textOverlay.classList.add('hidden');
        }

        function updateTextPreview() {
            const textOverlay = document.getElementById('textOverlay');
            const fontSize = document.getElementById('fontSizeSlider').value;
            const color = document.getElementById('textColor').value;
            const text = document.getElementById('textInput').value;
            
            if (text.trim() && !textOverlay.classList.contains('hidden')) {
                textOverlay.style.fontSize = fontSize + 'px';
                textOverlay.style.color = color;
                textOverlay.textContent = text;
                
                // 居中显示
                textOverlay.style.left = '50%';
                textOverlay.style.top = '50%';
                textOverlay.style.transform = 'translate(-50%, -50%)';
            }
        }

        function updateVideoFilters() {
            const brightness = document.getElementById('brightnessSlider').value;
            const contrast = document.getElementById('contrastSlider').value;
            const saturation = document.getElementById('saturationSlider').value;
            
            videoElement.style.filter = `brightness(${brightness}%) contrast(${contrast}%) saturate(${saturation}%)`;
        }

        function exportVideo() {
            // 显示加载提示
            document.getElementById('loading').classList.remove('hidden');
            
            // 模拟导出过程
            setTimeout(() => {
                document.getElementById('loading').classList.add('hidden');
                
                // 简化版导出：创建一个下载链接
                if (videoElement.src) {
                    const a = document.createElement('a');
                    a.href = videoElement.src;
                    a.download = `edited-video-${Date.now()}.mp4`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    
                    alert('视频导出完成！\n\n注意：这是简化版本，实际的视频编辑需要更复杂的处理。');
                } else {
                    alert('请先选择一个视频文件');
                }
            }, 2000);
        }

        function goBack() {
            if (confirm('确定要返回吗？未保存的更改将丢失。')) {
                // 尝试返回上一页
                if (window.history.length > 1) {
                    window.history.back();
                } else {
                    // 如果没有历史记录，跳转到主页
                    window.location.href = '/';
                }
            }
        }

        // ESC键返回
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                goBack();
            }
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (videoElement && videoElement.src) {
                URL.revokeObjectURL(videoElement.src);
            }
            if (audioElement && audioElement.src) {
                URL.revokeObjectURL(audioElement.src);
            }
        });
    </script>
</body>
</html>
