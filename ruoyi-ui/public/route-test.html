<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>路由测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .menu-item {
            display: block;
            padding: 15px 20px;
            margin: 10px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        .menu-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .menu-item.finance {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .menu-item.system {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }
        .menu-item.store {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .menu-item.changelog {
            background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>菜单路由测试</h1>
        <p>以下是当前系统的菜单项，点击测试跳转：</p>
        
        <h3>主菜单</h3>
        <a href="#/index" class="menu-item">🏠 工作台</a>
        <a href="#/agent/list" class="menu-item">👥 代理列表</a>
        <a href="#/merchant/list" class="menu-item">🏪 商家列表</a>
        
        <h3>财务管理</h3>
        <a href="#/finance/overview" class="menu-item finance">💰 财务概览</a>
        <a href="#/finance/record" class="menu-item finance">📊 交易记录</a>
        
        <h3>系统管理</h3>
        <a href="#/system/menu" class="menu-item system">📋 菜单管理</a>
        <a href="#/system/dict" class="menu-item system">📖 字典管理</a>
        <a href="#/system/config" class="menu-item system">⚙️ 参数设置</a>
        
        <h3>店铺管理</h3>
        <a href="#/store/index" class="menu-item store">🏬 店铺概览</a>
        <a href="#/store/bangding" class="menu-item store">🔗 绑定平台</a>
        <a href="#/store/shipin" class="menu-item store">🎥 视频管理</a>
        <a href="#/store/daka" class="menu-item store">⏰ 打卡管理</a>
        
        <h3>其他</h3>
        <a href="#/changelog/index" class="menu-item changelog">📝 更新日志</a>
        
        <div style="margin-top: 30px; padding: 20px; background: #e8f4f8; border-radius: 6px;">
            <h4>解决方案总结：</h4>
            <p><strong>问题原因：</strong> 路由权限拦截器检查用户token，没有token时跳转到登录页面，而登录页面可能配置了外部域名。</p>
            
            <p><strong>解决方案：</strong></p>
            <ul>
                <li>1. 修改了 <code>src/permission.js</code> 文件，在开发环境中跳过权限验证</li>
                <li>2. 更新了 <code>vue.config.js</code> 代理配置，避免请求重定向</li>
                <li>3. 创建了所有菜单页面的简化版本，只保留banner显示</li>
            </ul>
            
            <p><strong>注意：</strong> 在生产环境中，需要确保后端API服务正常运行，并且用户有正确的登录凭证。</p>
        </div>
    </div>
</body>
</html>
