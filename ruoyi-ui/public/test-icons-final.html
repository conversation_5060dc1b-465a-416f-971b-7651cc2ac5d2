<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终图标验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; }
        .icon-container { 
            display: inline-block; 
            margin: 10px; 
            text-align: center;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .icon { 
            width: 48px; 
            height: 48px; 
            display: block; 
            margin: 0 auto 5px;
        }
        .status { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>图标集成最终验证</h1>
    
    <div class="test-section">
        <h2>1. 静态图标测试（public目录）</h2>
        <div id="public-icons"></div>
    </div>
    
    <div class="test-section">
        <h2>2. webpack编译后的图标测试</h2>
        <div id="webpack-test">
            <p>请在浏览器控制台查看测试结果</p>
        </div>
    </div>

    <div class="test-section">
        <h2>3. 应用访问测试</h2>
        <p><a href="/promotion/config" target="_blank">打开推广页面配置</a></p>
        <p><a href="/promotion/page" target="_blank">打开推广页面</a></p>
    </div>

    <script>
        // 平台图标列表
        const platformIcons = [
            'baidudian', 'dazhongdian', 'douyin', 'douyindian', 
            'gaodedian', 'kuaishou', 'meituandian', 'pengyouquan',
            'qiye', 'qq', 'shipinhao', 'weixin', 'xiaohongshu'
        ];

        // 测试静态图标访问
        function testPublicIcons() {
            const container = document.getElementById('public-icons');
            platformIcons.forEach(iconName => {
                const div = document.createElement('div');
                div.className = 'icon-container';
                
                const img = document.createElement('img');
                img.className = 'icon';
                img.src = `/images/platforms/${iconName}.png`;
                img.alt = iconName;
                
                const label = document.createElement('div');
                label.textContent = iconName;
                
                img.onload = () => {
                    label.innerHTML = `${iconName}<br><span class="status">✓ 加载成功</span>`;
                };
                
                img.onerror = () => {
                    label.innerHTML = `${iconName}<br><span class="error">✗ 加载失败</span>`;
                };
                
                div.appendChild(img);
                div.appendChild(label);
                container.appendChild(div);
            });
        }

        // 测试webpack资源
        function testWebpackAssets() {
            console.log('=== Webpack资源测试开始 ===');
            platformIcons.forEach(iconName => {
                // 模拟require调用测试
                const testPath = `/static/img/${iconName}.png`;
                
                const testImg = new Image();
                testImg.onload = () => {
                    console.log(`✓ Webpack资源可访问: ${iconName} -> ${testPath}`);
                };
                testImg.onerror = () => {
                    console.log(`✗ Webpack资源访问失败: ${iconName} -> ${testPath}`);
                };
                testImg.src = testPath;
            });
        }

        // 检查localStorage配置
        function checkLocalStorage() {
            console.log('=== localStorage配置检查 ===');
            const config = localStorage.getItem('promotionPageConfig');
            if (config) {
                try {
                    const parsed = JSON.parse(config);
                    console.log('当前配置:', parsed);
                } catch (e) {
                    console.log('配置解析错误:', e);
                }
            } else {
                console.log('没有找到推广页面配置');
            }
        }

        // 启动测试
        document.addEventListener('DOMContentLoaded', () => {
            testPublicIcons();
            testWebpackAssets();
            checkLocalStorage();
        });
    </script>
</body>
</html>
