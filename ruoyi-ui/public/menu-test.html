<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>路由跳转测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .menu-item {
            display: block;
            padding: 15px 20px;
            margin: 10px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 16px;
        }
        .menu-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .menu-item.store {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .menu-item.merchant {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>路由跳转测试</h1>
        
        <div class="test-result" id="testResult">
            点击下方链接测试路由跳转...
        </div>
        
        <h3>🏪 商家管理</h3>
        <a href="#/merchant/list" class="menu-item merchant" onclick="testRoute('/merchant/list')">
            商家列表 (门店列表)
        </a>
        
        <h3>🏬 店铺管理</h3>
        <a href="#/store/index" class="menu-item store" onclick="testRoute('/store/index')">
            店铺概览
        </a>
        <a href="#/store/bangding" class="menu-item store" onclick="testRoute('/store/bangding')">
            绑定平台
        </a>
        <a href="#/store/shipin" class="menu-item store" onclick="testRoute('/store/shipin')">
            视频管理
        </a>
        <a href="#/store/daka" class="menu-item store" onclick="testRoute('/store/daka')">
            打卡管理
        </a>
        
        <div style="margin-top: 30px; padding: 20px; background: #e8f4f8; border-radius: 6px;">
            <h4>🔧 调试信息</h4>
            <p>当前页面: <span id="currentPath">-</span></p>
            <p>页面状态: <span id="pageStatus">-</span></p>
        </div>
    </div>

    <script>
        function testRoute(path) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.innerHTML = `正在测试路由: ${path}`;
            resultDiv.className = 'test-result';
            
            // 模拟路由跳转测试
            setTimeout(() => {
                try {
                    // 检查当前URL
                    const currentHash = window.location.hash;
                    if (currentHash.includes(path)) {
                        resultDiv.innerHTML = `✅ 路由跳转成功: ${path}`;
                        resultDiv.className = 'test-result success';
                    } else {
                        resultDiv.innerHTML = `❌ 路由跳转失败: ${path}`;
                        resultDiv.className = 'test-result error';
                    }
                } catch (error) {
                    resultDiv.innerHTML = `❌ 路由测试出错: ${error.message}`;
                    resultDiv.className = 'test-result error';
                }
            }, 100);
        }
        
        // 更新当前路径显示
        function updateCurrentPath() {
            document.getElementById('currentPath').textContent = window.location.hash || '/';
            document.getElementById('pageStatus').textContent = document.readyState;
        }
        
        // 监听hash变化
        window.addEventListener('hashchange', updateCurrentPath);
        window.addEventListener('load', updateCurrentPath);
        
        // 初始化
        updateCurrentPath();
    </script>
</body>
</html>
