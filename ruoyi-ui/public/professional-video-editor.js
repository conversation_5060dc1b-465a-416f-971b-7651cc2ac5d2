// 专业视频编辑器 JavaScript

// 全局状态
const editorState = {
    currentTime: 0,
    duration: 30,
    isPlaying: false,
    selectedItem: null,
    tracks: [],
    zoom: 5,
    canvas: null,
    ctx: null,
    animationId: null,
    dragState: {
        isDragging: false,
        dragType: null, // 'item', 'playhead', 'resize'
        dragItem: null,
        startX: 0,
        startTime: 0,
        originalStart: 0,
        originalDuration: 0
    },
    nextTrackId: 1,
    nextItemId: 1
};

// 初始化编辑器
document.addEventListener('DOMContentLoaded', function() {
    initializeEditor();
    setupEventListeners();
    generateTimelineRuler();
    
    // 获取URL参数并设置标题
    const params = getUrlParams();
    if (params.templateName) {
        document.querySelector('.header-title').textContent = `编辑: ${params.templateName}`;
    }
});

function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    return {
        templateId: params.get('templateId') || '',
        templateName: decodeURIComponent(params.get('templateName') || ''),
        source: params.get('source') || ''
    };
}

function initializeEditor() {
    // 初始化画布
    editorState.canvas = document.getElementById('previewCanvas');
    editorState.ctx = editorState.canvas.getContext('2d');

    // 设置画布大小
    resizeCanvas();

    // 初始化轨道系统
    initializeTracks();

    // 添加示例内容
    addSampleContent();

    // 开始渲染循环
    startRenderLoop();
}

function initializeTracks() {
    // 创建默认轨道
    createTrack('video', '视频 1');
    createTrack('audio', '音频 1');
    createTrack('text', '文字 1');

    // 渲染轨道UI
    renderTracks();
}

function resizeCanvas() {
    const container = document.querySelector('.preview-container');
    const canvas = editorState.canvas;
    
    // 计算合适的显示尺寸
    const containerWidth = container.clientWidth - 40;
    const containerHeight = container.clientHeight - 40;
    const aspectRatio = 16 / 9;
    
    let displayWidth = containerWidth;
    let displayHeight = displayWidth / aspectRatio;
    
    if (displayHeight > containerHeight) {
        displayHeight = containerHeight;
        displayWidth = displayHeight * aspectRatio;
    }
    
    canvas.style.width = displayWidth + 'px';
    canvas.style.height = displayHeight + 'px';
}

// 轨道管理
function createTrack(type, name) {
    const track = {
        id: `track_${editorState.nextTrackId++}`,
        type: type,
        name: name,
        items: [],
        muted: false,
        locked: false
    };

    editorState.tracks.push(track);
    return track;
}

function renderTracks() {
    const trackLabels = document.getElementById('trackLabels');
    const tracksArea = document.getElementById('tracksArea');

    // 清空现有内容
    trackLabels.innerHTML = '';
    tracksArea.innerHTML = '';

    // 渲染每个轨道
    editorState.tracks.forEach(track => {
        // 创建轨道标签
        const label = document.createElement('div');
        label.className = 'track-label';
        label.textContent = track.name;
        trackLabels.appendChild(label);

        // 创建轨道区域
        const trackElement = document.createElement('div');
        trackElement.className = 'track';
        trackElement.id = track.id;
        trackElement.dataset.trackType = track.type;
        tracksArea.appendChild(trackElement);

        // 渲染轨道中的项目
        track.items.forEach(item => {
            renderTrackItem(track.id, item);
        });
    });
}

function addSampleContent() {
    // 找到对应类型的轨道
    const videoTrack = editorState.tracks.find(t => t.type === 'video');
    const audioTrack = editorState.tracks.find(t => t.type === 'audio');
    const textTrack = editorState.tracks.find(t => t.type === 'text');

    // 添加示例视频片段
    if (videoTrack) {
        const videoItem = createTrackItem('video', '示例视频', 0, 10);
        videoTrack.items.push(videoItem);
    }

    // 添加示例音频片段
    if (audioTrack) {
        const audioItem = createTrackItem('audio', '背景音乐', 0, 15);
        audioTrack.items.push(audioItem);
    }

    // 添加示例文字
    if (textTrack) {
        const textItem = createTrackItem('text', '标题文字', 2, 5);
        textItem.content = '欢迎使用专业视频编辑器';
        textTrack.items.push(textItem);
    }

    // 重新渲染轨道
    renderTracks();

    // 设置总时长
    editorState.duration = 30;
    document.getElementById('renderBtn').disabled = false;
}

function createTrackItem(type, name, start, duration) {
    return {
        id: `item_${editorState.nextItemId++}`,
        type: type,
        name: name,
        start: start,
        duration: duration,
        properties: getDefaultProperties(type),
        content: type === 'text' ? 'Sample Text' : null
    };
}

function getDefaultProperties(type) {
    const common = {
        x: 0, y: 0, scale: 100, rotation: 0, opacity: 100
    };

    switch (type) {
        case 'video':
            return { ...common, brightness: 100, contrast: 100, saturation: 100 };
        case 'audio':
            return { volume: 80, fadeIn: 0, fadeOut: 0 };
        case 'text':
            return { ...common, fontSize: 48, color: '#ffffff' };
        default:
            return common;
    }
}

function setupEventListeners() {
    // 窗口大小改变
    window.addEventListener('resize', resizeCanvas);

    // 时间线交互
    const tracksContainer = document.getElementById('tracksContainer');
    tracksContainer.addEventListener('mousedown', handleMouseDown);
    tracksContainer.addEventListener('mousemove', handleMouseMove);
    tracksContainer.addEventListener('mouseup', handleMouseUp);
    tracksContainer.addEventListener('click', handleTimelineClick);

    // 全局鼠标事件（用于拖拽）
    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);

    // 右键菜单
    tracksContainer.addEventListener('contextmenu', handleContextMenu);

    // 文件输入
    document.getElementById('fileInput').addEventListener('change', handleFileImport);

    // 键盘快捷键
    document.addEventListener('keydown', handleKeyboard);

    // 隐藏右键菜单
    document.addEventListener('click', hideContextMenu);

    // 播放头拖拽
    const playhead = document.getElementById('playhead');
    playhead.addEventListener('mousedown', handlePlayheadDrag);
}

function generateTimelineRuler() {
    const rulerMarks = document.getElementById('rulerMarks');
    rulerMarks.innerHTML = '';

    const duration = Math.max(editorState.duration, 30);
    const pixelsPerSecond = editorState.zoom * 10;
    const totalWidth = duration * pixelsPerSecond;

    // 设置标尺容器宽度
    rulerMarks.style.width = totalWidth + 'px';

    // 生成时间刻度
    for (let i = 0; i <= duration; i++) {
        const mark = document.createElement('div');
        mark.className = 'ruler-mark';
        mark.style.left = (i * pixelsPerSecond) + 'px';
        mark.textContent = formatTime(i);
        rulerMarks.appendChild(mark);
    }

    // 更新轨道区域宽度
    const tracksArea = document.getElementById('tracksArea');
    tracksArea.style.minWidth = totalWidth + 'px';
}

function formatTime(seconds) {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

function renderTrackItem(trackId, item) {
    const track = document.getElementById(trackId);
    if (!track) return;

    const pixelsPerSecond = editorState.zoom * 10;

    // 检查是否已存在，如果存在则更新
    let element = document.getElementById(item.id);
    if (!element) {
        element = document.createElement('div');
        element.className = `track-item ${item.type}`;
        element.id = item.id;

        // 添加拖拽手柄
        const leftHandle = document.createElement('div');
        leftHandle.className = 'resize-handle left';
        element.appendChild(leftHandle);

        const rightHandle = document.createElement('div');
        rightHandle.className = 'resize-handle right';
        element.appendChild(rightHandle);

        // 添加事件监听
        element.addEventListener('mousedown', (e) => handleItemMouseDown(e, item));
        leftHandle.addEventListener('mousedown', (e) => handleResizeMouseDown(e, item, 'left'));
        rightHandle.addEventListener('mousedown', (e) => handleResizeMouseDown(e, item, 'right'));

        track.appendChild(element);
    }

    // 更新位置和大小
    element.textContent = item.name;
    element.style.left = (item.start * pixelsPerSecond) + 'px';
    element.style.width = (item.duration * pixelsPerSecond) + 'px';
}

// 鼠标事件处理
function handleMouseDown(e) {
    e.preventDefault();
}

function handleMouseMove(e) {
    // 这里处理时间线内的鼠标移动
}

function handleMouseUp(e) {
    // 这里处理时间线内的鼠标释放
}

function handleGlobalMouseMove(e) {
    if (!editorState.dragState.isDragging) return;

    const rect = document.getElementById('tracksContainer').getBoundingClientRect();
    const x = e.clientX - rect.left;
    const pixelsPerSecond = editorState.zoom * 10;

    switch (editorState.dragState.dragType) {
        case 'item':
            handleItemDrag(x, pixelsPerSecond);
            break;
        case 'playhead':
            handlePlayheadMove(x, pixelsPerSecond);
            break;
        case 'resize':
            handleItemResize(x, pixelsPerSecond);
            break;
    }
}

function handleGlobalMouseUp(e) {
    if (editorState.dragState.isDragging) {
        endDrag();
    }
}

function handleItemMouseDown(e, item) {
    e.stopPropagation();
    e.preventDefault();

    selectTrackItem(item);

    const rect = document.getElementById('tracksContainer').getBoundingClientRect();
    const x = e.clientX - rect.left;

    editorState.dragState = {
        isDragging: true,
        dragType: 'item',
        dragItem: item,
        startX: x,
        startTime: editorState.currentTime,
        originalStart: item.start,
        originalDuration: item.duration
    };

    document.getElementById(item.id).classList.add('dragging');
}

function handleResizeMouseDown(e, item, side) {
    e.stopPropagation();
    e.preventDefault();

    selectTrackItem(item);

    const rect = document.getElementById('tracksContainer').getBoundingClientRect();
    const x = e.clientX - rect.left;

    editorState.dragState = {
        isDragging: true,
        dragType: 'resize',
        dragItem: item,
        resizeSide: side,
        startX: x,
        originalStart: item.start,
        originalDuration: item.duration
    };
}

function handlePlayheadDrag(e) {
    e.stopPropagation();
    e.preventDefault();

    const rect = document.getElementById('tracksContainer').getBoundingClientRect();
    const x = e.clientX - rect.left;

    editorState.dragState = {
        isDragging: true,
        dragType: 'playhead',
        startX: x,
        startTime: editorState.currentTime
    };
}

function handleItemDrag(x, pixelsPerSecond) {
    const deltaX = x - editorState.dragState.startX;
    const deltaTime = deltaX / pixelsPerSecond;
    const newStart = Math.max(0, editorState.dragState.originalStart + deltaTime);

    editorState.dragState.dragItem.start = newStart;

    // 更新UI
    const element = document.getElementById(editorState.dragState.dragItem.id);
    element.style.left = (newStart * pixelsPerSecond) + 'px';
}

function handleItemResize(x, pixelsPerSecond) {
    const deltaX = x - editorState.dragState.startX;
    const deltaTime = deltaX / pixelsPerSecond;
    const item = editorState.dragState.dragItem;

    if (editorState.dragState.resizeSide === 'left') {
        const newStart = Math.max(0, editorState.dragState.originalStart + deltaTime);
        const newDuration = editorState.dragState.originalDuration - (newStart - editorState.dragState.originalStart);

        if (newDuration > 0.1) {
            item.start = newStart;
            item.duration = newDuration;
        }
    } else {
        const newDuration = Math.max(0.1, editorState.dragState.originalDuration + deltaTime);
        item.duration = newDuration;
    }

    // 更新UI
    const element = document.getElementById(item.id);
    element.style.left = (item.start * pixelsPerSecond) + 'px';
    element.style.width = (item.duration * pixelsPerSecond) + 'px';
}

function handlePlayheadMove(x, pixelsPerSecond) {
    const newTime = Math.max(0, Math.min(editorState.duration, x / pixelsPerSecond));
    editorState.currentTime = newTime;
    updatePlayhead();
}

function endDrag() {
    if (editorState.dragState.dragItem) {
        document.getElementById(editorState.dragState.dragItem.id).classList.remove('dragging');
    }

    editorState.dragState = {
        isDragging: false,
        dragType: null,
        dragItem: null,
        startX: 0,
        startTime: 0,
        originalStart: 0,
        originalDuration: 0
    };
}

function selectTrackItem(item) {
    // 清除之前的选择
    document.querySelectorAll('.track-item.selected').forEach(el => {
        el.classList.remove('selected');
    });

    // 选择新项目
    const element = document.getElementById(item.id);
    if (element) {
        element.classList.add('selected');
    }
    editorState.selectedItem = item;

    // 更新属性面板
    updatePropertiesPanel(item);
}

function findTrackItemById(id) {
    for (const track of editorState.tracks) {
        const item = track.items.find(item => item.id === id);
        if (item) return item;
    }
    return null;
}

function updatePropertiesPanel(item) {
    if (!item || !item.properties) return;
    
    const props = item.properties;
    
    // 更新变换属性
    if (props.x !== undefined) {
        document.querySelector('input[onchange*="x"]').value = props.x;
    }
    if (props.y !== undefined) {
        document.querySelector('input[onchange*="y"]').value = props.y;
    }
    if (props.scale !== undefined) {
        document.querySelector('input[onchange*="scale"]').value = props.scale;
        document.getElementById('scaleValue').textContent = props.scale + '%';
    }
    if (props.rotation !== undefined) {
        document.querySelector('input[onchange*="rotation"]').value = props.rotation;
        document.getElementById('rotationValue').textContent = props.rotation + '°';
    }
    
    // 更新视频效果
    if (props.opacity !== undefined) {
        document.querySelector('input[onchange*="opacity"]').value = props.opacity;
        document.getElementById('opacityValue').textContent = props.opacity + '%';
    }
    if (props.brightness !== undefined) {
        document.querySelector('input[onchange*="brightness"]').value = props.brightness;
        document.getElementById('brightnessValue').textContent = props.brightness + '%';
    }
    if (props.contrast !== undefined) {
        document.querySelector('input[onchange*="contrast"]').value = props.contrast;
        document.getElementById('contrastValue').textContent = props.contrast + '%';
    }
    if (props.saturation !== undefined) {
        document.querySelector('input[onchange*="saturation"]').value = props.saturation;
        document.getElementById('saturationValue').textContent = props.saturation + '%';
    }
    
    // 更新音频属性
    if (props.volume !== undefined) {
        document.querySelector('input[onchange*="volume"]').value = props.volume;
        document.getElementById('volumeValue').textContent = props.volume + '%';
    }
}

function updateProperty(property, value) {
    if (!editorState.selectedItem) return;
    
    editorState.selectedItem.properties[property] = parseFloat(value);
    
    // 更新显示值
    const valueElement = document.getElementById(property + 'Value');
    if (valueElement) {
        const unit = property === 'rotation' ? '°' : '%';
        valueElement.textContent = value + unit;
    }
    
    // 重新渲染预览
    renderFrame();
}

function startRenderLoop() {
    function render() {
        renderFrame();
        editorState.animationId = requestAnimationFrame(render);
    }
    render();
}

function renderFrame() {
    const ctx = editorState.ctx;
    const canvas = editorState.canvas;
    
    // 清空画布
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 渲染当前时间的所有元素
    renderVideoElements();
    renderTextElements();
    
    // 更新时间显示
    updateTimeDisplay();
}

function renderVideoElements() {
    const ctx = editorState.ctx;
    const currentTime = editorState.currentTime;
    
    // 渲染视频轨道上的元素
    editorState.tracks.video.forEach(item => {
        if (currentTime >= item.start && currentTime <= item.start + item.duration) {
            renderVideoItem(ctx, item);
        }
    });
}

function renderVideoItem(ctx, item) {
    const props = item.properties;
    const canvas = editorState.canvas;
    
    // 保存当前状态
    ctx.save();
    
    // 应用变换
    ctx.translate(canvas.width / 2 + props.x, canvas.height / 2 + props.y);
    ctx.scale(props.scale / 100, props.scale / 100);
    ctx.rotate(props.rotation * Math.PI / 180);
    ctx.globalAlpha = props.opacity / 100;
    
    // 绘制视频占位符（实际项目中这里会是视频帧）
    ctx.fillStyle = '#333333';
    ctx.fillRect(-200, -112.5, 400, 225);
    
    ctx.fillStyle = '#666666';
    ctx.font = '24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('视频预览', 0, 0);
    
    // 恢复状态
    ctx.restore();
}

function renderTextElements() {
    const ctx = editorState.ctx;
    const currentTime = editorState.currentTime;
    
    // 渲染文字轨道上的元素
    editorState.tracks.text.forEach(item => {
        if (currentTime >= item.start && currentTime <= item.start + item.duration) {
            renderTextItem(ctx, item);
        }
    });
}

function renderTextItem(ctx, item) {
    const props = item.properties;
    const canvas = editorState.canvas;
    
    // 保存当前状态
    ctx.save();
    
    // 应用变换
    ctx.translate(canvas.width / 2 + props.x, canvas.height / 2 + props.y);
    ctx.scale(props.scale / 100, props.scale / 100);
    ctx.rotate(props.rotation * Math.PI / 180);
    ctx.globalAlpha = props.opacity / 100;
    
    // 绘制文字
    ctx.fillStyle = props.color || '#ffffff';
    ctx.font = `${props.fontSize || 48}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // 添加文字描边
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.strokeText(item.content, 0, 0);
    ctx.fillText(item.content, 0, 0);
    
    // 恢复状态
    ctx.restore();
}

function updateTimeDisplay() {
    const timeDisplay = document.getElementById('timeDisplay');
    const currentTime = editorState.currentTime;
    
    const hours = Math.floor(currentTime / 3600);
    const minutes = Math.floor((currentTime % 3600) / 60);
    const seconds = Math.floor(currentTime % 60);
    const frames = Math.floor((currentTime % 1) * 30);
    
    timeDisplay.textContent = 
        `${hours.toString().padStart(2, '0')}:` +
        `${minutes.toString().padStart(2, '0')}:` +
        `${seconds.toString().padStart(2, '0')}:` +
        `${frames.toString().padStart(2, '0')}`;
}

function updatePlayhead() {
    const playhead = document.getElementById('playhead');
    const pixelsPerSecond = editorState.zoom * 10;
    playhead.style.left = (editorState.currentTime * pixelsPerSecond) + 'px';
}

// 播放控制
function togglePlay() {
    const playBtn = document.getElementById('playBtn');
    
    if (editorState.isPlaying) {
        editorState.isPlaying = false;
        playBtn.textContent = '▶️ 播放';
        clearInterval(editorState.playInterval);
    } else {
        editorState.isPlaying = true;
        playBtn.textContent = '⏸️ 暂停';
        
        editorState.playInterval = setInterval(() => {
            editorState.currentTime += 1/30; // 30fps
            
            if (editorState.currentTime >= editorState.duration) {
                editorState.currentTime = editorState.duration;
                togglePlay(); // 停止播放
            }
            
            updatePlayhead();
        }, 1000/30);
    }
}

function goToStart() {
    editorState.currentTime = 0;
    updatePlayhead();
}

function goToEnd() {
    editorState.currentTime = editorState.duration;
    updatePlayhead();
}

// 时间线交互
function handleTimelineClick(e) {
    if (e.target.classList.contains('tracks-container') || e.target.classList.contains('track')) {
        const rect = e.currentTarget.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const pixelsPerSecond = editorState.zoom * 10;
        
        editorState.currentTime = Math.max(0, x / pixelsPerSecond);
        updatePlayhead();
    }
}

function handleContextMenu(e) {
    e.preventDefault();
    
    const contextMenu = document.getElementById('contextMenu');
    contextMenu.style.display = 'block';
    contextMenu.style.left = e.clientX + 'px';
    contextMenu.style.top = e.clientY + 'px';
}

function hideContextMenu() {
    document.getElementById('contextMenu').style.display = 'none';
}

// 轨道管理
function addTrack(type) {
    const trackName = `${type === 'video' ? '视频' : type === 'audio' ? '音频' : '文字'} ${editorState.tracks.filter(t => t.type === type).length + 1}`;
    const newTrack = createTrack(type, trackName);

    // 重新渲染轨道
    renderTracks();

    console.log(`添加了${type}轨道:`, newTrack);
}

function changeTimelineZoom(value) {
    editorState.zoom = parseInt(value);
    generateTimelineRuler();

    // 重新渲染所有轨道项目
    editorState.tracks.forEach(track => {
        track.items.forEach(item => {
            renderTrackItem(track.id, item);
        });
    });

    updatePlayhead();
}

function findTrackItemById(id) {
    for (const trackType in editorState.tracks) {
        const item = editorState.tracks[trackType].find(item => item.id === id);
        if (item) return item;
    }
    return null;
}

// 侧边栏
function switchTab(tabName) {
    // 更新标签状态
    document.querySelectorAll('.sidebar-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 显示对应内容
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    document.getElementById(tabName + 'Tab').classList.remove('hidden');
}

function selectResource(element, type) {
    // 清除之前的选择
    document.querySelectorAll('.resource-item.selected').forEach(item => {
        item.classList.remove('selected');
    });
    
    // 选择当前项目
    element.classList.add('selected');
    
    console.log(`选择了${type}资源:`, element.textContent);
}

// 文件导入
function importMedia() {
    document.getElementById('fileInput').click();
}

function handleFileImport(e) {
    const files = Array.from(e.target.files);
    
    files.forEach(file => {
        console.log('导入文件:', file.name, file.type);
        
        // 这里可以添加文件处理逻辑
        // 根据文件类型添加到相应轨道
    });
}

// 导出功能
function exportProject() {
    const projectData = {
        duration: editorState.duration,
        tracks: editorState.tracks,
        settings: {
            resolution: '1920x1080',
            framerate: 30
        }
    };
    
    const blob = new Blob([JSON.stringify(projectData, null, 2)], {
        type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'video-project.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function renderVideo() {
    showLoading('正在渲染视频，请稍候...');
    
    // 模拟渲染过程
    setTimeout(() => {
        hideLoading();
        alert('视频渲染完成！\n\n这是演示版本，实际渲染需要更复杂的视频处理。');
    }, 3000);
}

// 上下文菜单操作
function cutItem() {
    console.log('剪切项目');
    hideContextMenu();
}

function copyItem() {
    console.log('复制项目');
    hideContextMenu();
}

function deleteItem() {
    if (editorState.selectedItem) {
        const element = document.getElementById(editorState.selectedItem.id);
        if (element) {
            element.remove();
        }
        
        // 从数据中删除
        for (const trackType in editorState.tracks) {
            const index = editorState.tracks[trackType].findIndex(
                item => item.id === editorState.selectedItem.id
            );
            if (index !== -1) {
                editorState.tracks[trackType].splice(index, 1);
                break;
            }
        }
        
        editorState.selectedItem = null;
    }
    hideContextMenu();
}

function splitItem() {
    console.log('分割项目');
    hideContextMenu();
}

// 键盘快捷键
function handleKeyboard(e) {
    switch (e.key) {
        case 'Escape':
            goBack();
            break;
        case ' ':
            e.preventDefault();
            togglePlay();
            break;
        case 'Delete':
            deleteItem();
            break;
        case 'Home':
            goToStart();
            break;
        case 'End':
            goToEnd();
            break;
    }
}

// 工具函数
function showLoading(text) {
    document.getElementById('loadingText').textContent = text;
    document.getElementById('loading').classList.remove('hidden');
}

function hideLoading() {
    document.getElementById('loading').classList.add('hidden');
}

function changeZoom(value) {
    console.log('改变预览缩放:', value);
}

function goBack() {
    if (confirm('确定要返回吗？未保存的更改将丢失。')) {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = '/';
        }
    }
}
