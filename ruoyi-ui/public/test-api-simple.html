<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单API测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        
        .success {
            border-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单API测试</h1>
        
        <div>
            <button class="btn" onclick="testHealth()">健康检查</button>
            <button class="btn" onclick="testDoubaoReal()">测试豆包API</button>
            <button class="btn" onclick="testCreateLibrary()">测试创建文案库</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8078';
        
        function showResult(content, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${isSuccess ? 'success' : 'error'}">${content}</div>`;
        }
        
        async function testHealth() {
            showResult('正在检查API健康状态...');
            
            try {
                const response = await fetch(`${API_BASE}/ai/test/health`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`✅ API健康检查成功\n\n${JSON.stringify(data, null, 2)}`, true);
                } else {
                    showResult(`❌ API健康检查失败\n\n${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                showResult(`❌ 网络连接失败\n\n${error.message}`, false);
            }
        }
        
        async function testDoubaoReal() {
            showResult('正在测试豆包API...');
            
            try {
                const response = await fetch(`${API_BASE}/ai/test/test-shuiyuewan-real`);
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    showResult(`✅ 豆包API测试成功\n\n生成的文案：\n${data.data.generatedContent}\n\n字数：${data.data.wordCount}`, true);
                } else {
                    showResult(`❌ 豆包API测试失败\n\n${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                showResult(`❌ 豆包API请求失败\n\n${error.message}`, false);
            }
        }
        
        async function testCreateLibrary() {
            showResult('正在测试创建文案库...');
            
            const testData = {
                libraryName: '水悦湾SPA测试库',
                shopDetails: '王牌项目（含特色）：壹品・休闲足道（70min、金牌技师、肩背下肢经络、小吃果盘）、壹品・太极理疗（90min、芳疗技师、艾灸红外、养生茶、睡眠 / 腰腹调理）、悦境・养元 SPA（70min、资深 SPA 技师、肩背排毒、定制甜点、商务回血）、云尚・至尊足道（80min、金牌技师、手足反射区、性价比）',
                prompt: '我们是水悦湾，除了上面介绍的，其余都模糊介绍,不要发散思维说一些不一些不存在的东西,每段文案一定要强调正规绿色！熬夜党速来！这口盈江的温泉，是给肝的道歉礼🌿',
                targetCount: 3,
                wordCount: 100,
                useAi: true
            };
            
            try {
                const response = await fetch(`${API_BASE}/ai/copywriting/library/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const data = await response.json();

                if (response.ok && data.code === 200) {
                    showResult(`✅ 文案库创建成功\n\n库ID：${data.data.libraryId || data.data.id}\n库名：${data.data.libraryName}`, true);

                    // 如果创建成功，尝试启动生成
                    const libraryId = data.data.libraryId || data.data.id;
                    if (libraryId) {
                        setTimeout(() => testGenerate(libraryId), 1000);
                    }
                } else {
                    showResult(`❌ 文案库创建失败\n\n${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                showResult(`❌ 创建文案库请求失败\n\n${error.message}`, false);
            }
        }
        
        async function testGenerate(libraryId) {
            showResult('正在启动文案生成...');
            
            const generateData = {
                libraryId: libraryId,
                shopDetails: '王牌项目（含特色）：壹品・休闲足道（70min、金牌技师、肩背下肢经络、小吃果盘）、壹品・太极理疗（90min、芳疗技师、艾灸红外、养生茶、睡眠 / 腰腹调理）、悦境・养元 SPA（70min、资深 SPA 技师、肩背排毒、定制甜点、商务回血）、云尚・至尊足道（80min、金牌技师、手足反射区、性价比）',
                prompt: '现在你是一位优秀的文案策划，你需要根据我提供的内容生成一篇口播文案。\n\n店铺详情：王牌项目（含特色）：壹品・休闲足道（70min、金牌技师、肩背下肢经络、小吃果盘）、壹品・太极理疗（90min、芳疗技师、艾灸红外、养生茶、睡眠 / 腰腹调理）、悦境・养元 SPA（70min、资深 SPA 技师、肩背排毒、定制甜点、商务回血）、云尚・至尊足道（80min、金牌技师、手足反射区、性价比）\n\n特殊要求：我们是水悦湾，除了上面介绍的，其余都模糊介绍,不要发散思维说一些不一些不存在的东西,每段文案一定要强调正规绿色！熬夜党速来！这口盈江的温泉，是给肝的道歉礼🌿\n\n请生成适合口播的文案，语言自然生动，朋友推荐的语气，字数控制在50-100字。',
                count: 3,
                wordCount: 100
            };
            
            try {
                const response = await fetch(`${API_BASE}/ai/copywriting/generate/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(generateData)
                });

                const data = await response.json();

                if (response.ok && data.code === 200) {
                    const taskId = data.data && data.data.taskId ? data.data.taskId : '已启动';
                    showResult(`✅ 文案生成任务启动成功\n\n任务ID：${taskId}\n\n请等待生成完成...`, true);
                } else {
                    showResult(`❌ 文案生成启动失败\n\n${JSON.stringify(data, null, 2)}`, false);
                }
            } catch (error) {
                showResult(`❌ 文案生成请求失败\n\n${error.message}`, false);
            }
        }
        
        // 页面加载时自动进行健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
