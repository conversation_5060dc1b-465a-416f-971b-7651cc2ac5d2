<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fly-Cut视频编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #1a1a1a;
        }
        
        #fly-cut-container {
            width: 100vw;
            height: 100vh;
            border: none;
            display: block;
        }
        
        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ffffff;
            font-size: 18px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            z-index: 9999;
        }
        
        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 10000;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: 1px solid #555;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: all 0.3s;
        }
        
        .back-button:hover {
            background: rgba(0, 0, 0, 0.9);
            border-color: #777;
        }
        
        .back-button:active {
            transform: translateY(1px);
        }
    </style>
</head>
<body>
    <div class="loading" id="loading">正在加载Fly-Cut编辑器</div>
    <button class="back-button" onclick="goBack()">← 返回</button>
    
    <iframe
        id="fly-cut-container"
        src="http://localhost:3001/fly-cut/"
        frameborder="0"
        allowfullscreen
        style="display: none;"
    ></iframe>

    <script>
        // 获取URL参数
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                templateId: params.get('templateId'),
                templateName: params.get('templateName'),
                source: params.get('source')
            };
        }
        
        // 设置页面标题
        function setPageTitle() {
            const params = getUrlParams();
            if (params.templateName) {
                document.title = `编辑模板: ${decodeURIComponent(params.templateName)} - Fly-Cut`;
            }
        }
        
        // 返回功能
        function goBack() {
            if (confirm('确定要离开编辑器吗？未保存的更改将丢失。')) {
                // 尝试返回上一页
                if (window.history.length > 1) {
                    window.history.back();
                } else {
                    // 如果没有历史记录，跳转到主页
                    window.location.href = '/';
                }
            }
        }
        
        // iframe加载完成处理
        function onIframeLoad() {
            const loading = document.getElementById('loading');
            const iframe = document.getElementById('fly-cut-container');
            
            // 隐藏加载提示，显示iframe
            loading.style.display = 'none';
            iframe.style.display = 'block';
            
            // 尝试与iframe通信（如果fly-cut支持的话）
            try {
                const params = getUrlParams();
                if (params.templateId) {
                    // 这里可以向fly-cut传递模板信息
                    console.log('模板信息:', params);
                }
            } catch (error) {
                console.log('无法与fly-cut通信:', error);
            }
        }
        
        // 监听iframe加载
        document.getElementById('fly-cut-container').addEventListener('load', onIframeLoad);
        
        // 监听键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Esc键返回
            if (e.key === 'Escape') {
                goBack();
            }
            
            // Ctrl+S保存（传递给iframe）
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                console.log('保存快捷键触发');
            }
        });
        
        // 防止右键菜单（可选）
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // 初始化
        setPageTitle();
        
        // 如果fly-cut加载失败，显示备用方案
        setTimeout(function() {
            const loading = document.getElementById('loading');
            if (loading.style.display !== 'none') {
                loading.innerHTML = '正在连接Fly-Cut服务器，请稍候...';
            }
        }, 5000);
        
        // 超时处理
        setTimeout(function() {
            const loading = document.getElementById('loading');
            if (loading.style.display !== 'none') {
                loading.innerHTML = `
                    <div style="text-align: center;">
                        <p>无法连接到Fly-Cut服务器</p>
                        <p style="margin-top: 10px; font-size: 14px; color: #ccc;">
                            请检查网络连接或稍后重试
                        </p>
                        <button onclick="location.reload()" style="
                            margin-top: 15px;
                            padding: 8px 16px;
                            background: #409eff;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                        ">重新加载</button>
                    </div>
                `;
            }
        }, 15000);
    </script>
</body>
</html>
