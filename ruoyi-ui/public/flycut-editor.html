<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fly-Cut 视频编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000;
            color: #fff;
            overflow: hidden;
            height: 100vh;
        }

        .editor-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            padding: 0 20px;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .back-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .title {
            flex: 1;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: #fff;
        }

        .info {
            font-size: 12px;
            color: #aaa;
        }

        .flycut-frame {
            position: absolute;
            top: 50px;
            left: 0;
            right: 0;
            bottom: 0;
            border: none;
            width: 100%;
            height: calc(100vh - 50px);
            background: #1a1a1a;
        }

        .loading {
            position: absolute;
            top: 50px;
            left: 0;
            right: 0;
            bottom: 0;
            background: #1a1a1a;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 999;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #333;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 16px;
            color: #ccc;
            margin-bottom: 10px;
        }

        .loading-tip {
            font-size: 12px;
            color: #666;
            text-align: center;
            max-width: 300px;
        }

        .error {
            position: absolute;
            top: 50px;
            left: 0;
            right: 0;
            bottom: 0;
            background: #1a1a1a;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 998;
        }

        .error-icon {
            font-size: 48px;
            color: #dc3545;
            margin-bottom: 20px;
        }

        .error-text {
            font-size: 18px;
            color: #fff;
            margin-bottom: 10px;
        }

        .error-tip {
            font-size: 14px;
            color: #aaa;
            text-align: center;
            max-width: 400px;
            line-height: 1.5;
        }

        .retry-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 20px;
            transition: all 0.2s ease;
        }

        .retry-btn:hover {
            background: #1e7e34;
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <!-- 顶部导航栏 -->
        <div class="header">
            <button class="back-btn" onclick="goBack()">
                ← 返回
            </button>
            <div class="title" id="editorTitle">Fly-Cut 视频编辑器</div>
            <div class="info">专业版</div>
        </div>

        <!-- 加载提示 -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div class="loading-text">正在加载 Fly-Cut 编辑器...</div>
            <div class="loading-tip">首次加载可能需要几秒钟，请耐心等待</div>
        </div>

        <!-- 错误提示 -->
        <div class="error" id="error">
            <div class="error-icon">⚠️</div>
            <div class="error-text">编辑器加载失败</div>
            <div class="error-tip">
                请检查网络连接，或尝试刷新页面。<br>
                如果问题持续存在，请联系技术支持。
            </div>
            <button class="retry-btn" onclick="retryLoad()">重新加载</button>
        </div>

        <!-- Fly-Cut 编辑器 -->
        <iframe
            id="flycutFrame"
            class="flycut-frame"
            src="http://localhost:3001/fly-cut/"
            frameborder="0"
            allowfullscreen
            allow="camera; microphone; fullscreen"
        ></iframe>
    </div>

    <script>
        // 获取URL参数
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                templateId: params.get('templateId') || '',
                templateName: decodeURIComponent(params.get('templateName') || ''),
                source: params.get('source') || ''
            };
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const params = getUrlParams();
            
            // 设置标题
            if (params.templateName) {
                document.getElementById('editorTitle').textContent = `编辑: ${params.templateName}`;
                document.title = `编辑: ${params.templateName} - Fly-Cut`;
            }
            
            // 初始化编辑器
            initializeEditor();
        });

        function initializeEditor() {
            const iframe = document.getElementById('flycutFrame');
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            
            // 设置加载超时
            const loadTimeout = setTimeout(() => {
                showError();
            }, 15000); // 15秒超时
            
            // iframe加载完成
            iframe.onload = function() {
                clearTimeout(loadTimeout);
                
                // 延迟隐藏加载提示，确保fly-cut完全初始化
                setTimeout(() => {
                    loading.classList.add('hidden');
                }, 1000);
                
                console.log('Fly-Cut 编辑器加载完成');
                
                // 尝试与fly-cut通信
                try {
                    setupCommunication();
                } catch (e) {
                    console.warn('无法建立与fly-cut的通信:', e);
                }
            };
            
            // iframe加载错误
            iframe.onerror = function() {
                clearTimeout(loadTimeout);
                showError();
            };
        }

        function setupCommunication() {
            const iframe = document.getElementById('flycutFrame');
            const params = getUrlParams();
            
            // 监听来自fly-cut的消息
            window.addEventListener('message', function(event) {
                // 确保消息来自fly-cut iframe
                if (event.source !== iframe.contentWindow) return;
                
                console.log('收到fly-cut消息:', event.data);
                
                // 处理不同类型的消息
                switch (event.data.type) {
                    case 'READY':
                        handleFlyCutReady();
                        break;
                    case 'SAVE':
                        handleSave(event.data.data);
                        break;
                    case 'EXPORT':
                        handleExport(event.data.data);
                        break;
                    case 'ERROR':
                        console.error('Fly-Cut错误:', event.data.error);
                        break;
                }
            });
            
            // 向fly-cut发送初始化参数
            setTimeout(() => {
                iframe.contentWindow.postMessage({
                    type: 'INIT',
                    data: {
                        templateId: params.templateId,
                        templateName: params.templateName,
                        source: params.source,
                        config: {
                            theme: 'dark',
                            language: 'zh-CN'
                        }
                    }
                }, '*');
            }, 2000);
        }

        function handleFlyCutReady() {
            console.log('Fly-Cut 已准备就绪');
            // 可以在这里执行一些初始化操作
        }

        function handleSave(data) {
            console.log('保存项目:', data);
            
            // 这里可以调用后端API保存项目
            // fetch('/api/save-project', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify(data)
            // });
        }

        function handleExport(data) {
            console.log('导出视频:', data);
            
            // 这里可以调用后端API处理视频导出
            // 或者上传到阿里云等云存储服务
        }

        function showError() {
            document.getElementById('loading').classList.add('hidden');
            document.getElementById('error').style.display = 'flex';
        }

        function retryLoad() {
            document.getElementById('error').style.display = 'none';
            document.getElementById('loading').classList.remove('hidden');
            
            // 重新加载iframe
            const iframe = document.getElementById('flycutFrame');
            iframe.src = iframe.src;
            
            initializeEditor();
        }

        function goBack() {
            if (confirm('确定要返回吗？未保存的更改将丢失。')) {
                // 尝试通知fly-cut保存状态
                try {
                    const iframe = document.getElementById('flycutFrame');
                    iframe.contentWindow.postMessage({
                        type: 'BEFORE_UNLOAD'
                    }, '*');
                } catch (e) {
                    console.warn('无法通知fly-cut:', e);
                }
                
                // 返回上一页
                if (window.history.length > 1) {
                    window.history.back();
                } else {
                    window.location.href = '/';
                }
            }
        }

        // ESC键返回
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                goBack();
            }
        });

        // 页面卸载前清理
        window.addEventListener('beforeunload', function(event) {
            // 可以在这里保存用户的编辑状态
        });
    </script>
</body>
</html>
