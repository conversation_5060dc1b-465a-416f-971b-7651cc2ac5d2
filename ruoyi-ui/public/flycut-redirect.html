<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跳转到 Fly-Cut 编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .redirect-container {
            text-align: center;
            padding: 40px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            max-width: 500px;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #fff;
        }

        .subtitle {
            font-size: 16px;
            color: #ccc;
            margin-bottom: 30px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #333;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status {
            font-size: 14px;
            color: #aaa;
            margin-bottom: 20px;
        }

        .manual-link {
            display: inline-block;
            background: #007bff;
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.2s ease;
            margin-top: 20px;
        }

        .manual-link:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .back-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <button class="back-btn" onclick="goBack()">← 返回</button>
    
    <div class="redirect-container">
        <div class="logo">🎬</div>
        <div class="title" id="pageTitle">正在启动 Fly-Cut 编辑器</div>
        <div class="subtitle" id="templateName">专业视频编辑器</div>
        
        <div class="spinner"></div>
        <div class="status" id="status">正在连接编辑器服务...</div>
        
        <a href="#" class="manual-link" id="manualLink" style="display: none;" target="_blank">
            手动打开编辑器
        </a>
    </div>

    <script>
        // 获取URL参数
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                templateId: params.get('templateId') || '',
                templateName: decodeURIComponent(params.get('templateName') || ''),
                source: params.get('source') || ''
            };
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const params = getUrlParams();
            
            // 设置标题
            if (params.templateName) {
                document.getElementById('templateName').textContent = `模板: ${params.templateName}`;
                document.title = `编辑: ${params.templateName} - Fly-Cut`;
            }
            
            // 构建fly-cut URL
            const flycutUrl = `http://localhost:3001/fly-cut/?templateId=${params.templateId}&templateName=${encodeURIComponent(params.templateName)}&source=${params.source}`;
            
            // 设置手动链接
            document.getElementById('manualLink').href = flycutUrl;
            
            // 开始重定向流程
            startRedirect(flycutUrl);
        });

        function startRedirect(url) {
            let step = 0;
            const steps = [
                '检查编辑器服务...',
                '加载编辑器资源...',
                '初始化编辑器...',
                '准备跳转...'
            ];
            
            const statusElement = document.getElementById('status');
            
            // 显示加载步骤
            const stepInterval = setInterval(() => {
                if (step < steps.length) {
                    statusElement.textContent = steps[step];
                    step++;
                } else {
                    clearInterval(stepInterval);
                    performRedirect(url);
                }
            }, 800);
        }

        function performRedirect(url) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = '正在跳转到编辑器...';
            
            // 尝试检查服务器是否可用
            fetch('http://localhost:3001/fly-cut/')
                .then(response => {
                    if (response.ok) {
                        // 服务器可用，直接跳转
                        setTimeout(() => {
                            window.location.href = url;
                        }, 1000);
                    } else {
                        showError('编辑器服务不可用');
                    }
                })
                .catch(error => {
                    console.error('连接错误:', error);
                    showError('无法连接到编辑器服务');
                });
        }

        function showError(message) {
            const statusElement = document.getElementById('status');
            const manualLink = document.getElementById('manualLink');
            const spinner = document.querySelector('.spinner');
            
            statusElement.textContent = message;
            statusElement.style.color = '#dc3545';
            spinner.style.display = 'none';
            manualLink.style.display = 'inline-block';
            
            // 更新标题
            document.getElementById('pageTitle').textContent = '编辑器启动失败';
        }

        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }

        // ESC键返回
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                goBack();
            }
        });
    </script>
</body>
</html>
