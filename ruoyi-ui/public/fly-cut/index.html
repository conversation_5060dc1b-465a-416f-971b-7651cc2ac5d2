<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="referrer" content="never">
    <title>Fly Cut Web Video Editor</title>
    <meta name="description" content="A web-based video editing tool implemented with WebCodecs, similar to CapCut Web. 使用WebCodecs实现的Web端视频编辑工具，类似剪映Web版。">
    <meta name="keywords" content="web video editing, WebCodecs, CapCut Web, video editor, online video editing, Web视频编辑, WebCodecs, 剪映Web版, 视频编辑器,在线视频编辑">
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-MFLRH5ZJ');</script>
    <!-- End Google Tag Manager -->
    <script type="module" crossorigin src="/assets/index-eaf38e3e.js"></script>
    <link rel="stylesheet" href="/assets/index-39ebeaba.css">
  </head>
  <body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MFLRH5ZJ"
      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <div id="vite-app"></div>
    
    <!-- <script src="https://cdn.jsdelivr.net/npm/opfs-tools-explorer"></script>
    <script>
      OTExplorer.init();
    </script> -->
  </body>
</html>
