<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文案库测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #666;
        }
        .btn {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #66b1ff;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            background: #f0f9ff;
            border-left: 4px solid #409EFF;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background: #fef0f0;
            border-left-color: #f56c6c;
            color: #f56c6c;
        }
        .success {
            background: #f0f9ff;
            border-left-color: #67c23a;
            color: #67c23a;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI文案库测试页面</h1>
            <p>此页面用于测试AI文案库功能，无需登录权限</p>
        </div>

        <!-- 健康检查 -->
        <div class="test-section">
            <h3>🔍 健康检查</h3>
            <button class="btn" onclick="testHealth()">健康检查</button>
            <div id="health-result"></div>
        </div>

        <!-- DeepSeek-V3测试 -->
        <div class="test-section">
            <h3>🚀 DeepSeek-V3 AI测试</h3>
            <button class="btn" onclick="testDeepSeek()">测试DeepSeek-V3</button>
            <div id="deepseek-result"></div>
        </div>

        <!-- 文案库列表 -->
        <div class="test-section">
            <h3>📚 文案库管理</h3>
            <button class="btn" onclick="loadLibraryList()">加载文案库列表</button>
            <button class="btn" onclick="showCreateForm()">创建文案库</button>
            <div id="library-result"></div>
        </div>

        <!-- 创建文案库表单 -->
        <div class="test-section" id="create-form" style="display: none;">
            <h3>✨ 创建文案库</h3>
            <div class="form-group">
                <label>文案库名称:</label>
                <input type="text" id="libraryName" placeholder="请输入文案库名称" value="测试文案库">
            </div>
            <div class="form-group">
                <label>店铺详情:</label>
                <textarea id="shopDetails" placeholder="请描述您的店铺信息">测试商户：一家温馨的咖啡厅，主营手工咖啡和精致甜点，位于市中心繁华地段，装修温馨典雅。</textarea>
            </div>
            <div class="form-group">
                <label>AI提示词:</label>
                <textarea id="prompt" placeholder="请输入AI生成提示词">生成吸引人的美食推广文案，要求语言生动、有食欲感，突出产品特色和店铺氛围</textarea>
            </div>
            <div class="form-group">
                <label>生成条数:</label>
                <input type="number" id="targetCount" min="1" max="50" value="5">
            </div>
            <div class="form-group">
                <label>文案字数:</label>
                <select id="wordCount">
                    <option value="50">50字以内</option>
                    <option value="100" selected>100字左右</option>
                    <option value="200">200字左右</option>
                </select>
            </div>
            <button class="btn" onclick="createLibrary()">创建文案库</button>
            <div id="create-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8078';

        // 通用请求函数
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                const data = await response.json();
                return data;
            } catch (error) {
                throw new Error('网络请求失败: ' + error.message);
            }
        }

        // 显示结果
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        // 健康检查
        async function testHealth() {
            try {
                const result = await apiRequest('/ai/test/health');
                showResult('health-result', result);
            } catch (error) {
                showResult('health-result', { error: error.message }, true);
            }
        }

        // 测试DeepSeek-V3
        async function testDeepSeek() {
            try {
                const result = await apiRequest('/ai/test/test-deepseek-direct');
                showResult('deepseek-result', result);
            } catch (error) {
                showResult('deepseek-result', { error: error.message }, true);
            }
        }

        // 加载文案库列表
        async function loadLibraryList() {
            try {
                const result = await apiRequest('/ai/copywriting/library/list/test');
                showResult('library-result', result);
            } catch (error) {
                showResult('library-result', { error: error.message }, true);
            }
        }

        // 显示创建表单
        function showCreateForm() {
            const form = document.getElementById('create-form');
            form.style.display = form.style.display === 'none' ? 'block' : 'none';
        }

        // 创建文案库
        async function createLibrary() {
            try {
                const libraryData = {
                    libraryName: document.getElementById('libraryName').value,
                    useAi: true,
                    shopDetails: document.getElementById('shopDetails').value,
                    prompt: document.getElementById('prompt').value,
                    targetCount: parseInt(document.getElementById('targetCount').value),
                    wordCount: parseInt(document.getElementById('wordCount').value)
                };

                const result = await apiRequest('/ai/copywriting/library/test', {
                    method: 'POST',
                    body: JSON.stringify(libraryData)
                });

                showResult('create-result', result);
                
                if (result.code === 200) {
                    // 创建成功后重新加载列表
                    setTimeout(() => {
                        loadLibraryList();
                    }, 1000);
                }
            } catch (error) {
                showResult('create-result', { error: error.message }, true);
            }
        }

        // 页面加载时自动执行健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
