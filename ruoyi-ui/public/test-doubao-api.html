<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>豆包API测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 32px;
            margin-bottom: 10px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .test-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success {
            border-left: 4px solid #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
        }
        
        .loading {
            text-align: center;
            color: #6c757d;
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 豆包API测试</h1>
            <p>测试火山引擎Doubao API是否正常工作</p>
        </div>
        
        <div class="test-section">
            <h3>🧪 API连接测试</h3>
            <button class="btn" onclick="testHealth()">健康检查</button>
            <button class="btn" onclick="testBasicGenerate()">基础文案生成</button>
            <button class="btn" onclick="testShuiYueWan()">水悦湾SPA文案测试</button>
            <div id="healthResult"></div>
        </div>
        
        <div class="test-section">
            <h3>📝 水悦湾真实数据测试</h3>
            <p><strong>店铺详情：</strong>王牌项目（含特色）：壹品・休闲足道（70min、金牌技师、肩背下肢经络、小吃果盘）、壹品・太极理疗（90min、芳疗技师、艾灸红外、养生茶、睡眠 / 腰腹调理）、悦境・养元 SPA（70min、资深 SPA 技师、肩背排毒、定制甜点、商务回血）、云尚・至尊足道（80min、金牌技师、手足反射区、性价比）</p>
            <p><strong>AI提示词：</strong>我们是水悦湾，除了上面介绍的，其余都模糊介绍,不要发散思维说一些不一些不存在的东西,每段文案一定要强调正规绿色！熬夜党速来！这口盈江的温泉，是给肝的道歉礼🌿</p>
            <button class="btn" onclick="testRealShuiYueWan()">生成真实文案</button>
            <div id="realResult"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8078';
        
        async function testHealth() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.innerHTML = '<div class="result loading">正在检查API健康状态...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/ai/test/health`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <strong>✅ API健康检查成功</strong>
                            <span class="status success">正常</span>
                            <br><br>
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <strong>❌ API健康检查失败</strong>
                            <span class="status error">异常</span>
                            <br><br>
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>❌ 网络连接失败</strong>
                        <span class="status error">连接错误</span>
                        <br><br>
                        ${error.message}
                    </div>
                `;
            }
        }
        
        async function testBasicGenerate() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.innerHTML = '<div class="result loading">正在测试基础文案生成...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/ai/test/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        prompt: '生成一条吸引人的美食推广文案',
                        shopDetails: '测试店铺：一家温馨的咖啡厅，主营手工咖啡和精致甜点'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <strong>✅ 基础文案生成成功</strong>
                            <span class="status success">正常</span>
                            <br><br>
                            <strong>生成的文案：</strong><br>
                            ${data.data.generatedContent}
                            <br><br>
                            <strong>字数：</strong>${data.data.wordCount}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <strong>❌ 基础文案生成失败</strong>
                            <span class="status error">失败</span>
                            <br><br>
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>❌ 请求失败</strong>
                        <span class="status error">网络错误</span>
                        <br><br>
                        ${error.message}
                    </div>
                `;
            }
        }
        
        async function testShuiYueWan() {
            const resultDiv = document.getElementById('healthResult');
            resultDiv.innerHTML = '<div class="result loading">正在测试水悦湾SPA文案生成...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/ai/test/test-shuiyuewan`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <strong>✅ 水悦湾SPA文案生成成功</strong>
                            <span class="status success">正常</span>
                            <br><br>
                            <strong>生成的文案：</strong><br>
                            ${data.data.result}
                            <br><br>
                            <strong>字数：</strong>${data.data.length}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <strong>❌ 水悦湾SPA文案生成失败</strong>
                            <span class="status error">失败</span>
                            <br><br>
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>❌ 请求失败</strong>
                        <span class="status error">网络错误</span>
                        <br><br>
                        ${error.message}
                    </div>
                `;
            }
        }
        
        async function testRealShuiYueWan() {
            const resultDiv = document.getElementById('realResult');
            resultDiv.innerHTML = '<div class="result loading">正在使用真实数据生成水悦湾文案...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/ai/test/test-shuiyuewan-real`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <strong>✅ 水悦湾真实文案生成成功</strong>
                            <span class="status success">豆包API</span>
                            <br><br>
                            <strong>生成的文案：</strong><br>
                            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0; font-family: 'Microsoft YaHei';">
                                ${data.data.generatedContent}
                            </div>
                            <strong>字数：</strong>${data.data.wordCount}<br>
                            <strong>完整提示词：</strong><br>
                            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-size: 12px;">
                                ${data.data.fullPrompt}
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <strong>❌ 水悦湾真实文案生成失败</strong>
                            <span class="status error">失败</span>
                            <br><br>
                            ${JSON.stringify(data, null, 2)}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>❌ 请求失败</strong>
                        <span class="status error">网络错误</span>
                        <br><br>
                        ${error.message}
                    </div>
                `;
            }
        }
        
        // 页面加载时自动进行健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
