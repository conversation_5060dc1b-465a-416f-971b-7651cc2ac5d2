<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业视频编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #fff;
            overflow: hidden;
            height: 100vh;
        }

        .editor-layout {
            display: grid;
            grid-template-areas:
                "header header header"
                "sidebar preview tools"
                "timeline timeline timeline";
            grid-template-rows: 50px 1fr 250px;
            grid-template-columns: 250px 1fr 300px;
            height: 100vh;
        }

        /* 头部工具栏 */
        .header {
            grid-area: header;
            background: #2d2d2d;
            border-bottom: 1px solid #444;
            display: flex;
            align-items: center;
            padding: 0 20px;
            gap: 15px;
        }

        .back-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .header-title {
            font-size: 16px;
            font-weight: 600;
            flex: 1;
        }

        .header-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .header-btn:disabled {
            background: #666;
            cursor: not-allowed;
        }

        /* 左侧资源面板 */
        .sidebar {
            grid-area: sidebar;
            background: #2d2d2d;
            border-right: 1px solid #444;
            display: flex;
            flex-direction: column;
        }

        .sidebar-tabs {
            display: flex;
            border-bottom: 1px solid #444;
        }

        .sidebar-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            background: #333;
            border: none;
            color: #ccc;
            cursor: pointer;
            font-size: 12px;
        }

        .sidebar-tab.active {
            background: #2d2d2d;
            color: #fff;
        }

        .sidebar-content {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
        }

        .resource-item {
            background: #333;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .resource-item:hover {
            border-color: #007bff;
        }

        .resource-item.selected {
            border-color: #28a745;
        }

        /* 中央预览区 */
        .preview {
            grid-area: preview;
            background: #000;
            display: flex;
            flex-direction: column;
        }

        .preview-header {
            background: #2d2d2d;
            padding: 10px 15px;
            border-bottom: 1px solid #444;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preview-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .preview-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }

        .preview-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        .preview-canvas {
            max-width: 100%;
            max-height: 100%;
            background: #333;
            border-radius: 4px;
        }

        .time-display {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            font-family: monospace;
        }

        /* 右侧属性面板 */
        .tools {
            grid-area: tools;
            background: #2d2d2d;
            border-left: 1px solid #444;
            display: flex;
            flex-direction: column;
        }

        .tools-header {
            padding: 15px;
            border-bottom: 1px solid #444;
            font-size: 14px;
            font-weight: 600;
        }

        .tools-content {
            flex: 1;
            padding: 15px;
            overflow-y: auto;
        }

        .property-group {
            margin-bottom: 20px;
        }

        .property-title {
            font-size: 12px;
            color: #ccc;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .property-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            gap: 8px;
        }

        .property-label {
            min-width: 60px;
            font-size: 11px;
            color: #aaa;
        }

        .property-input {
            flex: 1;
            background: #444;
            border: 1px solid #666;
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 11px;
        }

        .property-slider {
            flex: 1;
        }

        .property-value {
            min-width: 40px;
            font-size: 11px;
            color: #ccc;
        }

        /* 底部时间线 */
        .timeline {
            grid-area: timeline;
            background: #2d2d2d;
            border-top: 1px solid #444;
            display: flex;
            flex-direction: column;
        }

        .timeline-header {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid #444;
            gap: 15px;
        }

        .timeline-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }

        .timeline-content {
            flex: 1;
            display: flex;
        }

        .track-labels {
            width: 120px;
            background: #333;
            border-right: 1px solid #444;
        }

        .track-label {
            height: 40px;
            display: flex;
            align-items: center;
            padding: 0 10px;
            border-bottom: 1px solid #444;
            font-size: 11px;
            color: #ccc;
        }

        .tracks-container {
            flex: 1;
            position: relative;
            overflow-x: auto;
            overflow-y: hidden;
            background: #2a2a2a;
        }

        .timeline-ruler {
            height: 30px;
            background: #333;
            border-bottom: 1px solid #444;
            position: sticky;
            top: 0;
            z-index: 10;
            overflow: hidden;
        }

        .ruler-marks {
            position: relative;
            height: 100%;
            min-width: 2000px;
        }

        .ruler-mark {
            position: absolute;
            top: 0;
            bottom: 0;
            border-left: 1px solid #666;
            font-size: 10px;
            color: #aaa;
            padding-left: 3px;
            line-height: 30px;
        }

        .tracks {
            min-height: 180px;
            position: relative;
        }

        .track {
            height: 40px;
            border-bottom: 1px solid #444;
            position: relative;
            background: #2a2a2a;
        }

        .track-item {
            position: absolute;
            height: 30px;
            top: 5px;
            background: #007bff;
            border-radius: 3px;
            cursor: move;
            display: flex;
            align-items: center;
            padding: 0 8px;
            font-size: 10px;
            color: white;
            border: 2px solid transparent;
            user-select: none;
            min-width: 50px;
        }

        .track-item:hover {
            border-color: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        .track-item.selected {
            border-color: #28a745;
            box-shadow: 0 0 0 1px #28a745;
        }

        .track-item.video {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .track-item.audio {
            background: linear-gradient(135deg, #28a745, #1e7e34);
        }

        .track-item.text {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #000;
        }

        .track-item.dragging {
            opacity: 0.8;
            z-index: 100;
        }

        .track-item .resize-handle {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 8px;
            cursor: ew-resize;
            background: transparent;
        }

        .track-item .resize-handle.left {
            left: 0;
        }

        .track-item .resize-handle.right {
            right: 0;
        }

        .playhead {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #ff0000;
            z-index: 200;
            cursor: ew-resize;
        }

        .playhead::before {
            content: '';
            position: absolute;
            top: -8px;
            left: -8px;
            width: 18px;
            height: 18px;
            background: #ff0000;
            border-radius: 50%;
            border: 2px solid #fff;
            cursor: ew-resize;
        }

        .playhead:hover::before {
            transform: scale(1.2);
        }

        /* 上下文菜单 */
        .context-menu {
            position: fixed;
            background: #333;
            border: 1px solid #666;
            border-radius: 4px;
            padding: 5px 0;
            z-index: 1000;
            display: none;
        }

        .context-menu-item {
            padding: 8px 15px;
            cursor: pointer;
            font-size: 12px;
        }

        .context-menu-item:hover {
            background: #444;
        }

        /* 加载提示 */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        .loading-content {
            background: #2d2d2d;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #444;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        /* 文件输入 */
        .file-input {
            display: none;
        }

        /* 响应式 */
        @media (max-width: 1200px) {
            .editor-layout {
                grid-template-columns: 200px 1fr 250px;
            }
        }

        @media (max-width: 900px) {
            .editor-layout {
                grid-template-areas: 
                    "header header"
                    "preview preview"
                    "timeline timeline";
                grid-template-columns: 1fr 1fr;
                grid-template-rows: 50px 1fr 200px;
            }
            
            .sidebar, .tools {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="editor-layout">
        <!-- 头部工具栏 -->
        <div class="header">
            <button class="back-btn" onclick="goBack()">← 返回</button>
            <div class="header-title">专业视频编辑器</div>
            <button class="header-btn" onclick="importMedia()">导入媒体</button>
            <button class="header-btn" onclick="exportProject()">导出项目</button>
            <button class="header-btn" id="renderBtn" onclick="renderVideo()" disabled>渲染视频</button>
        </div>

        <!-- 左侧资源面板 -->
        <div class="sidebar">
            <div class="sidebar-tabs">
                <button class="sidebar-tab active" onclick="switchTab('media')">媒体</button>
                <button class="sidebar-tab" onclick="switchTab('effects')">特效</button>
                <button class="sidebar-tab" onclick="switchTab('text')">文字</button>
            </div>
            <div class="sidebar-content">
                <div id="mediaTab" class="tab-content">
                    <div class="resource-item" onclick="selectResource(this, 'video')" data-type="video">
                        <div>📹 示例视频</div>
                        <div style="font-size: 10px; color: #aaa;">MP4 • 1920x1080</div>
                    </div>
                    <div class="resource-item" onclick="selectResource(this, 'audio')" data-type="audio">
                        <div>🎵 背景音乐</div>
                        <div style="font-size: 10px; color: #aaa;">MP3 • 44.1kHz</div>
                    </div>
                    <div class="resource-item" onclick="selectResource(this, 'image')" data-type="image">
                        <div>🖼️ 图片素材</div>
                        <div style="font-size: 10px; color: #aaa;">PNG • 1920x1080</div>
                    </div>
                </div>
                <div id="effectsTab" class="tab-content hidden">
                    <div class="resource-item" onclick="selectResource(this, 'effect')" data-effect="blur">
                        <div>🌫️ 模糊效果</div>
                    </div>
                    <div class="resource-item" onclick="selectResource(this, 'effect')" data-effect="brightness">
                        <div>☀️ 亮度调节</div>
                    </div>
                    <div class="resource-item" onclick="selectResource(this, 'effect')" data-effect="contrast">
                        <div>🔆 对比度</div>
                    </div>
                </div>
                <div id="textTab" class="tab-content hidden">
                    <div class="resource-item" onclick="selectResource(this, 'text')" data-text="title">
                        <div>📝 标题文字</div>
                    </div>
                    <div class="resource-item" onclick="selectResource(this, 'text')" data-text="subtitle">
                        <div>📄 字幕文字</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中央预览区 -->
        <div class="preview">
            <div class="preview-header">
                <div class="preview-controls">
                    <button class="preview-btn" id="playBtn" onclick="togglePlay()">▶️ 播放</button>
                    <button class="preview-btn" onclick="goToStart()">⏮️</button>
                    <button class="preview-btn" onclick="goToEnd()">⏭️</button>
                    <span style="font-size: 11px; color: #ccc;">缩放:</span>
                    <select class="preview-btn" style="background: #444;" onchange="changeZoom(this.value)">
                        <option value="fit">适合</option>
                        <option value="50">50%</option>
                        <option value="100" selected>100%</option>
                        <option value="200">200%</option>
                    </select>
                </div>
                <div style="font-size: 11px; color: #ccc;">
                    分辨率: <span id="resolution">1920x1080</span> • 帧率: <span id="framerate">30fps</span>
                </div>
            </div>
            <div class="preview-container">
                <canvas id="previewCanvas" class="preview-canvas" width="1920" height="1080"></canvas>
                <div class="time-display" id="timeDisplay">00:00:00:00</div>
            </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="tools">
            <div class="tools-header">属性面板</div>
            <div class="tools-content">
                <div class="property-group">
                    <div class="property-title">变换</div>
                    <div class="property-item">
                        <span class="property-label">X位置:</span>
                        <input type="number" class="property-input" value="0" onchange="updateProperty('x', this.value)">
                    </div>
                    <div class="property-item">
                        <span class="property-label">Y位置:</span>
                        <input type="number" class="property-input" value="0" onchange="updateProperty('y', this.value)">
                    </div>
                    <div class="property-item">
                        <span class="property-label">缩放:</span>
                        <input type="range" class="property-slider" min="0" max="200" value="100" onchange="updateProperty('scale', this.value)">
                        <span class="property-value" id="scaleValue">100%</span>
                    </div>
                    <div class="property-item">
                        <span class="property-label">旋转:</span>
                        <input type="range" class="property-slider" min="0" max="360" value="0" onchange="updateProperty('rotation', this.value)">
                        <span class="property-value" id="rotationValue">0°</span>
                    </div>
                </div>

                <div class="property-group">
                    <div class="property-title">视频效果</div>
                    <div class="property-item">
                        <span class="property-label">不透明度:</span>
                        <input type="range" class="property-slider" min="0" max="100" value="100" onchange="updateProperty('opacity', this.value)">
                        <span class="property-value" id="opacityValue">100%</span>
                    </div>
                    <div class="property-item">
                        <span class="property-label">亮度:</span>
                        <input type="range" class="property-slider" min="0" max="200" value="100" onchange="updateProperty('brightness', this.value)">
                        <span class="property-value" id="brightnessValue">100%</span>
                    </div>
                    <div class="property-item">
                        <span class="property-label">对比度:</span>
                        <input type="range" class="property-slider" min="0" max="200" value="100" onchange="updateProperty('contrast', this.value)">
                        <span class="property-value" id="contrastValue">100%</span>
                    </div>
                    <div class="property-item">
                        <span class="property-label">饱和度:</span>
                        <input type="range" class="property-slider" min="0" max="200" value="100" onchange="updateProperty('saturation', this.value)">
                        <span class="property-value" id="saturationValue">100%</span>
                    </div>
                </div>

                <div class="property-group">
                    <div class="property-title">音频</div>
                    <div class="property-item">
                        <span class="property-label">音量:</span>
                        <input type="range" class="property-slider" min="0" max="100" value="100" onchange="updateProperty('volume', this.value)">
                        <span class="property-value" id="volumeValue">100%</span>
                    </div>
                    <div class="property-item">
                        <span class="property-label">淡入:</span>
                        <input type="number" class="property-input" value="0" onchange="updateProperty('fadeIn', this.value)" placeholder="秒">
                    </div>
                    <div class="property-item">
                        <span class="property-label">淡出:</span>
                        <input type="number" class="property-input" value="0" onchange="updateProperty('fadeOut', this.value)" placeholder="秒">
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部时间线 -->
        <div class="timeline">
            <div class="timeline-header">
                <button class="timeline-btn" onclick="addTrack('video')">+ 视频轨道</button>
                <button class="timeline-btn" onclick="addTrack('audio')">+ 音频轨道</button>
                <button class="timeline-btn" onclick="addTrack('text')">+ 文字轨道</button>
                <div style="flex: 1;"></div>
                <span style="font-size: 11px; color: #ccc;">时间线缩放:</span>
                <input type="range" min="1" max="10" value="5" onchange="changeTimelineZoom(this.value)" style="width: 100px;">
            </div>
            <div class="timeline-content">
                <div class="track-labels" id="trackLabels">
                    <!-- 轨道标签将通过JavaScript生成 -->
                </div>
                <div class="tracks-container" id="tracksContainer">
                    <div class="timeline-ruler" id="timelineRuler">
                        <div class="ruler-marks" id="rulerMarks">
                            <!-- 时间刻度将通过JavaScript生成 -->
                        </div>
                    </div>
                    <div class="tracks" id="tracksArea">
                        <!-- 轨道将通过JavaScript生成 -->
                    </div>
                    <div class="playhead" id="playhead" style="left: 0px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 上下文菜单 -->
    <div class="context-menu" id="contextMenu">
        <div class="context-menu-item" onclick="cutItem()">剪切</div>
        <div class="context-menu-item" onclick="copyItem()">复制</div>
        <div class="context-menu-item" onclick="deleteItem()">删除</div>
        <div class="context-menu-item" onclick="splitItem()">分割</div>
    </div>

    <!-- 加载提示 -->
    <div id="loading" class="loading hidden">
        <div class="loading-content">
            <div class="spinner"></div>
            <div id="loadingText">正在处理，请稍候...</div>
        </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input type="file" id="fileInput" class="file-input" multiple accept="video/*,audio/*,image/*">

    <script src="professional-video-editor.js"></script>
</body>
</html>
