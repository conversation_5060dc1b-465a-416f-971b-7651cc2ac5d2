<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI文案库功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .link-section {
            margin: 20px 0;
            padding: 20px;
            background: #e9ecef;
            border-radius: 6px;
        }
        .link-section h3 {
            margin-top: 0;
            color: #495057;
        }
        .link-section a {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .link-section a:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI文案库功能测试</h1>
            <p>快速验证系统功能状态</p>
        </div>

        <div class="status success">
            ✅ 系统状态：正常运行（演示模式）
        </div>

        <div class="status info">
            ℹ️ 当前模式：演示模式 - 使用模拟数据，无需登录
        </div>

        <div class="status warning">
            ⚠️ 完整功能：需要重启后端服务以启用真实AI生成
        </div>

        <div class="link-section">
            <h3>🔗 快速访问链接</h3>
            <a href="http://localhost:8080/storer/shipin" target="_blank">📚 文案库管理</a>
            <a href="http://localhost:8080/storer/ai-test" target="_blank">🤖 AI测试页面</a>
            <a href="http://localhost:8080/ai-demo.html" target="_blank">🌟 演示页面</a>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="btn" onclick="testHealth()">🔍 健康检查</button>
            <button class="btn" onclick="testAI()">🤖 测试AI功能</button>
            <button class="btn" onclick="showInstructions()">📋 使用说明</button>
        </div>

        <div id="result" class="result" style="display: none;"></div>

        <div id="instructions" style="display: none; margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
            <h3>📋 使用说明</h3>
            <ol style="line-height: 1.8;">
                <li><strong>文案库管理：</strong>访问 <code>http://localhost:8080/storer/shipin</code>
                    <ul>
                        <li>查看3个演示文案库</li>
                        <li>点击"创建文案库"体验创建流程</li>
                        <li>点击文案库查看详细内容</li>
                    </ul>
                </li>
                <li><strong>AI功能测试：</strong>访问 <code>http://localhost:8080/storer/ai-test</code>
                    <ul>
                        <li>测试DeepSeek-V3 AI模型</li>
                        <li>生成测试文案</li>
                        <li>验证AI服务状态</li>
                    </ul>
                </li>
                <li><strong>演示页面：</strong>访问 <code>http://localhost:8080/ai-demo.html</code>
                    <ul>
                        <li>查看系统功能介绍</li>
                        <li>浏览演示文案库</li>
                        <li>了解使用方法</li>
                    </ul>
                </li>
            </ol>
            
            <h4>🔧 启用完整功能</h4>
            <p>要启用真实的AI文案生成功能，请：</p>
            <ol>
                <li>停止当前的后端服务</li>
                <li>重新启动后端服务</li>
                <li>访问文案库管理页面</li>
                <li>创建文案库并体验真实的AI生成</li>
            </ol>
        </div>
    </div>

    <script>
        function showResult(content, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.style.borderLeftColor = isError ? '#dc3545' : '#007bff';
            resultDiv.style.background = isError ? '#f8d7da' : '#f8f9fa';
            resultDiv.textContent = content;
        }

        async function testHealth() {
            try {
                const response = await fetch('http://localhost:8078/ai/test/health');
                const data = await response.json();
                showResult('健康检查成功！\n\n' + JSON.stringify(data, null, 2));
            } catch (error) {
                showResult('健康检查失败：' + error.message, true);
            }
        }

        async function testAI() {
            try {
                const response = await fetch('http://localhost:8078/ai/test/test-deepseek-direct');
                const data = await response.json();
                showResult('AI功能测试成功！\n\n' + JSON.stringify(data, null, 2));
            } catch (error) {
                showResult('AI功能测试失败：' + error.message, true);
            }
        }

        function showInstructions() {
            const instructions = document.getElementById('instructions');
            instructions.style.display = instructions.style.display === 'none' ? 'block' : 'none';
        }

        // 页面加载时自动执行健康检查
        window.onload = function() {
            console.log('页面加载完成，系统状态：正常');
        };
    </script>
</body>
</html>
