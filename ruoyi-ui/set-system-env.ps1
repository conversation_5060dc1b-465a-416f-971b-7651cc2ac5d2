# PowerShell script to permanently set Node.js environment variables
# Run as Administrator

Write-Host "===========================================" -ForegroundColor Green
Write-Host "Setting Node.js System Environment Variables" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "[ERROR] Administrator privileges required" -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[OK] Administrator privileges confirmed" -ForegroundColor Green
Write-Host ""

# Define paths
$nodejsPath = "C:\chajian\nvm\v18.19.0"
$nvmPath = "C:\chajian\nvm"

Write-Host "1. Checking Node.js installation..." -ForegroundColor Cyan
if (Test-Path "$nodejsPath\node.exe") {
    Write-Host "[OK] Node.js found at: $nodejsPath" -ForegroundColor Green
} else {
    Write-Host "[ERROR] Node.js not found at: $nodejsPath" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "2. Getting current system PATH..." -ForegroundColor Cyan
$currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
Write-Host "Current PATH length: $($currentPath.Length) characters" -ForegroundColor Yellow

Write-Host ""
Write-Host "3. Adding Node.js paths to system PATH..." -ForegroundColor Cyan

# Check if paths are already in PATH
$pathsToAdd = @()

if ($currentPath -notlike "*$nodejsPath*") {
    $pathsToAdd += $nodejsPath
    Write-Host "Will add: $nodejsPath" -ForegroundColor Yellow
} else {
    Write-Host "Already in PATH: $nodejsPath" -ForegroundColor Green
}

if ($currentPath -notlike "*$nvmPath*") {
    $pathsToAdd += $nvmPath
    Write-Host "Will add: $nvmPath" -ForegroundColor Yellow
} else {
    Write-Host "Already in PATH: $nvmPath" -ForegroundColor Green
}

# Add paths if needed
if ($pathsToAdd.Count -gt 0) {
    $newPath = $currentPath + ";" + ($pathsToAdd -join ";")
    
    try {
        [Environment]::SetEnvironmentVariable("Path", $newPath, "Machine")
        Write-Host "[OK] System PATH updated successfully" -ForegroundColor Green
    } catch {
        Write-Host "[ERROR] Failed to update system PATH: $($_.Exception.Message)" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} else {
    Write-Host "[OK] All paths already in system PATH" -ForegroundColor Green
}

Write-Host ""
Write-Host "4. Setting NVM environment variables..." -ForegroundColor Cyan

try {
    [Environment]::SetEnvironmentVariable("NVM_HOME", $nvmPath, "Machine")
    [Environment]::SetEnvironmentVariable("NVM_SYMLINK", "C:\chajian\nodejs", "Machine")
    Write-Host "[OK] NVM environment variables set" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Failed to set NVM variables: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "5. Testing Node.js commands..." -ForegroundColor Cyan

# Test node
try {
    $nodeVersion = & "$nodejsPath\node.exe" --version
    Write-Host "[OK] Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] Node.js test failed" -ForegroundColor Red
}

# Test npm
try {
    $npmVersion = & "$nodejsPath\npm.cmd" --version
    Write-Host "[OK] npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "[ERROR] npm test failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "===========================================" -ForegroundColor Green
Write-Host "Environment setup completed!" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green
Write-Host ""
Write-Host "IMPORTANT NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Close ALL command prompt and PowerShell windows" -ForegroundColor White
Write-Host "2. Open a NEW command prompt" -ForegroundColor White
Write-Host "3. Test: node --version" -ForegroundColor White
Write-Host "4. Test: npm --version" -ForegroundColor White
Write-Host "5. If still not working, restart your computer" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to finish"
