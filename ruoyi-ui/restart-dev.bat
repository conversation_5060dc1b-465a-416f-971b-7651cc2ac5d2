@echo off
chcp 65001 > nul
echo ========================================
echo          Vue 路由问题修复工具
echo ========================================
echo.
echo 🔧 正在修复路由配置...
echo.

echo ✅ 已修复的问题：
echo    • 路由配置：添加了.vue扩展名
echo    • 权限拦截器：开发环境跳过验证
echo    • 调试工具：创建了测试页面
echo.

echo 🚀 正在重启开发服务器...
echo.
echo 📍 当前目录：%CD%
echo.

REM 检查是否在正确的目录
if not exist "package.json" (
    echo ❌ 错误：未找到package.json文件
    echo 请确保在ruoyi-ui目录下运行此脚本
    pause
    exit /b 1
)

REM 检查Node.js是否已安装
node --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Node.js
    echo 请先安装Node.js
    pause
    exit /b 1
)

echo 📦 Node.js版本：
node --version
echo.

echo 🔄 正在启动开发服务器...
echo.
echo 💡 提示：
echo    • 服务器启动后，访问: http://localhost:8081
echo    • 使用 Ctrl+C 可以停止服务器
echo    • 如果端口被占用，可能会自动使用其他端口
echo.
echo 🧪 测试页面：
echo    • 路由修复工具: file://%CD%\vue-router-fix.html
echo    • 导航调试页面: file://%CD%\debug-navigation.html
echo.

REM 启动开发服务器
npm run dev

echo.
echo 🎉 如果看到此消息，说明服务器已启动成功！
echo 🌐 请在浏览器中访问: http://localhost:8081
echo.
pause
