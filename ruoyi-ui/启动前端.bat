@echo off
chcp 65001 >nul
echo ==========================================
echo 启动前端开发服务器
echo ==========================================
echo.

echo 当前目录: %CD%
echo.

REM 检查是否在正确的目录
if not exist "package.json" (
    echo ❌ 错误: 未找到 package.json 文件
    echo 请确保在 ruoyi-ui 目录中运行此脚本
    pause
    exit /b 1
)

echo ✅ 找到 package.json 文件
echo.

REM 尝试不同的 Node.js 路径
echo 正在尝试启动前端服务器...
echo.

REM 方法1: 使用引号包围的完整路径
echo 尝试方法1: 使用完整路径...
"C:\Program Files\nodejs\npm.cmd" run dev
if %errorlevel% equ 0 goto success

REM 方法2: 尝试直接使用 npm（如果环境变量正确）
echo.
echo 尝试方法2: 使用环境变量...
npm run dev
if %errorlevel% equ 0 goto success

REM 方法3: 尝试 yarn
echo.
echo 尝试方法3: 使用 yarn...
yarn dev
if %errorlevel% equ 0 goto success

REM 方法4: 尝试其他常见路径
echo.
echo 尝试方法4: 其他常见路径...
"C:\nodejs\npm.cmd" run dev
if %errorlevel% equ 0 goto success

"C:\Program Files (x86)\nodejs\npm.cmd" run dev
if %errorlevel% equ 0 goto success

REM 所有方法都失败
echo.
echo ❌ 所有启动方法都失败了
echo.
echo 请尝试以下解决方案：
echo.
echo 1. 修复环境变量：
echo    - 按 Win+R，输入 sysdm.cpl
echo    - 点击"高级" → "环境变量"
echo    - 在系统变量的 Path 中添加: C:\Program Files\nodejs
echo.
echo 2. 重新安装 Node.js：
echo    - 访问 https://nodejs.org/
echo    - 下载最新版本重新安装
echo.
echo 3. 使用完整路径：
echo    "C:\Program Files\nodejs\npm.cmd" run dev
echo.
echo 4. 检查 Node.js 安装位置：
echo    where node
echo.
pause
exit /b 1

:success
echo.
echo ✅ 前端服务器启动成功！
echo 📱 访问地址: http://localhost:8081
echo.
echo 按 Ctrl+C 停止服务器
pause
