@echo off
echo ===========================================
echo Vue 开发服务器启动脚本
echo ===========================================
echo.

echo 1. 检查当前目录...
cd /d %~dp0
echo 当前目录: %CD%

echo.
echo 2. 检查 package.json...
if exist package.json (
    echo ✅ package.json 文件存在
) else (
    echo ❌ package.json 文件不存在
    pause
    exit /b 1
)

echo.
echo 3. 检查 node_modules...
if exist node_modules (
    echo ✅ node_modules 文件夹存在
) else (
    echo ❌ node_modules 文件夹不存在，正在安装依赖...
    npm install
)

echo.
echo 4. 启动开发服务器...
echo 正在启动 Vue 开发服务器...
echo 请等待服务器启动完成后访问: http://localhost:8080
echo.
echo 测试链接:
echo - 商家列表: http://localhost:8080/#/merchant/list
echo - 店铺概览: http://localhost:8080/#/store/index
echo - 绑定平台: http://localhost:8080/#/store/bangding
echo - 视频管理: http://localhost:8080/#/store/shipin
echo - 打卡管理: http://localhost:8080/#/store/daka
echo.
echo ===========================================

npm run dev

pause
