@echo off
chcp 65001 >nul
echo ==========================================
echo Node.js 安装助手
echo ==========================================
echo.

echo 检查 Node.js 安装状态...
echo.

REM 检查是否已安装
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js 已安装
    node --version
    npm --version
    echo.
    echo Node.js 已正确安装并配置！
    pause
    exit /b 0
)

echo ❌ Node.js 未安装或未正确配置
echo.

echo 请选择安装方式:
echo.
echo [1] 自动下载并安装 Node.js (推荐)
echo [2] 手动下载安装
echo [3] 使用已有安装但修复环境变量
echo [0] 退出
echo.
set /p choice=请选择 (0-3): 

if "%choice%"=="1" goto auto_install
if "%choice%"=="2" goto manual_install
if "%choice%"=="3" goto fix_existing
if "%choice%"=="0" goto exit
echo 无效选择
goto start

:auto_install
echo.
echo ==========================================
echo 自动安装 Node.js
echo ==========================================
echo.

echo 正在下载 Node.js LTS 版本...
echo.

REM 创建临时目录
if not exist "%TEMP%\nodejs_install" mkdir "%TEMP%\nodejs_install"
cd /d "%TEMP%\nodejs_install"

echo 使用 PowerShell 下载 Node.js...
echo.

powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://nodejs.org/dist/v18.17.0/node-v18.17.0-x64.msi' -OutFile 'nodejs.msi'}"

if exist "nodejs.msi" (
    echo ✅ 下载完成
    echo.
    echo 正在安装 Node.js...
    echo 请在安装向导中点击"下一步"完成安装
    echo.
    
    REM 静默安装
    msiexec /i nodejs.msi /quiet /norestart
    
    echo 安装完成！
    echo.
    echo 请重新打开命令行窗口测试: npm --version
) else (
    echo ❌ 下载失败
    echo 请检查网络连接或手动下载安装
)

pause
goto exit

:manual_install
echo.
echo ==========================================
echo 手动安装指南
echo ==========================================
echo.

echo 请按以下步骤手动安装 Node.js:
echo.
echo 1. 打开浏览器访问: https://nodejs.org/
echo 2. 点击 "LTS" 版本下载 (推荐)
echo 3. 下载完成后运行安装程序
echo 4. 安装过程中确保勾选 "Add to PATH" 选项
echo 5. 完成安装后重启命令行
echo 6. 测试: npm --version
echo.

echo 正在打开 Node.js 官网...
start https://nodejs.org/

pause
goto exit

:fix_existing
echo.
echo ==========================================
echo 修复现有安装
echo ==========================================
echo.

echo 正在查找现有的 Node.js 安装...
echo.

call 查找nodejs.bat

echo.
echo 如果找到了 Node.js 安装，请运行:
echo 修复环境变量.bat (需要管理员权限)
echo.

pause
goto exit

:exit
echo.
echo 安装助手结束
echo.
echo 如果安装成功，请重新打开命令行窗口
echo 然后测试: npm --version
echo.
pause
exit
