<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平台图标测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .icon-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .icon-section h3 {
            color: #409eff;
            border-bottom: 2px solid #409eff;
            padding-bottom: 10px;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .icon-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .icon-item img {
            width: 48px;
            height: 48px;
            object-fit: cover;
            border-radius: 6px;
            margin-bottom: 8px;
        }
        
        .icon-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 4px;
        }
        
        .icon-path {
            font-size: 12px;
            color: #666;
            word-break: break-all;
        }
        
        .highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            margin: 20px 0;
        }
        
        .test-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 平台图标集成测试</h1>
        <p>测试您的自定义平台图标是否正确集成到系统中</p>
        <div class="highlight">
            <h3>✅ 图标集成完成</h3>
            <p>所有平台图标已成功集成，现在可以在配置页面中使用</p>
        </div>
    </div>

    <!-- 图标展示 -->
    <div class="icon-section">
        <h3>📱 平台图标预览</h3>
        <div class="icon-grid" id="iconGrid">
            <!-- 图标将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 测试链接 -->
    <div class="icon-section">
        <h3>🔗 测试链接</h3>
        <div style="text-align: center;">
            <a href="http://localhost:8081/#/promotion/config/index?storeId=1&storeName=测试店铺" class="test-btn" target="_blank">
                打开配置页面测试
            </a>
            <a href="http://localhost:8081/#/promotion/1" class="test-btn" target="_blank">
                查看推广页面效果
            </a>
        </div>
    </div>

    <!-- 使用说明 -->
    <div class="icon-section">
        <h3>📋 使用说明</h3>
        <ol>
            <li><strong>图标选择</strong>：在配置页面的图标选择器中，现在可以看到预设的平台图标网格</li>
            <li><strong>快速选择</strong>：点击任意预设图标即可快速应用</li>
            <li><strong>自定义上传</strong>：仍然支持上传自定义图标</li>
            <li><strong>实时预览</strong>：选择图标后可在右侧预览面板实时查看效果</li>
            <li><strong>保存配置</strong>：点击保存按钮将配置应用到推广页面</li>
        </ol>
    </div>

    <script>
        // 图标数据
        const icons = [
            { name: '抖音', file: 'douyin.png', color: '#000000' },
            { name: '快手', file: 'kuaishou.png', color: '#ff6600' },
            { name: '小红书', file: 'xiaohongshu.png', color: '#ff2442' },
            { name: '朋友圈', file: 'pengyouquan.png', color: '#33cc33' },
            { name: '视频号', file: 'shipinhao.png', color: '#07c160' },
            { name: '微信', file: 'weixin.png', color: '#07c160' },
            { name: 'QQ', file: 'qq.png', color: '#1296db' },
            { name: '企微', file: 'qiye.png', color: '#1296db' },
            { name: '抖音点评', file: 'douyindian.png', color: '#000000' },
            { name: '高德点评', file: 'gaodedian.png', color: '#00a6f7' },
            { name: '百度点评', file: 'baidudian.png', color: '#2196f3' },
            { name: '美团点评', file: 'meituandian.png', color: '#ffc107' },
            { name: '大众点评', file: 'dazhongdian.png', color: '#ff9800' }
        ];

        // 动态生成图标展示
        const iconGrid = document.getElementById('iconGrid');
        
        icons.forEach(icon => {
            const iconItem = document.createElement('div');
            iconItem.className = 'icon-item';
            iconItem.style.borderLeft = `4px solid ${icon.color}`;
            
            iconItem.innerHTML = `
                <img src="/images/platforms/${icon.file}" alt="${icon.name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjZjVmNWY1Ii8+Cjx0ZXh0IHg9IjI0IiB5PSIyOCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7lm77niYc8L3RleHQ+Cjwvc3ZnPgo='">
                <div class="icon-name">${icon.name}</div>
                <div class="icon-path">${icon.file}</div>
            `;
            
            iconGrid.appendChild(iconItem);
        });

        console.log('🎉 平台图标测试页面已加载！');
        console.log('📱 配置页面: http://localhost:8081/#/promotion/config/index?storeId=1&storeName=测试店铺');
        console.log('🌟 推广页面: http://localhost:8081/#/promotion/1');
        
        // 测试图标加载状态
        setTimeout(() => {
            const images = document.querySelectorAll('#iconGrid img');
            let loadedCount = 0;
            let errorCount = 0;
            
            images.forEach(img => {
                if (img.complete) {
                    if (img.naturalWidth > 0) {
                        loadedCount++;
                    } else {
                        errorCount++;
                    }
                }
            });
            
            console.log(`图标加载统计: ${loadedCount} 个成功, ${errorCount} 个失败`);
            
            if (loadedCount === images.length) {
                document.querySelector('.highlight h3').innerHTML = '✅ 图标集成完成 - 所有图标加载成功';
            } else if (errorCount > 0) {
                document.querySelector('.highlight h3').innerHTML = `⚠️ 图标集成完成 - ${errorCount} 个图标加载失败`;
                document.querySelector('.highlight').style.background = '#f39c12';
            }
        }, 2000);
    </script>
</body>
</html>
