@echo off
chcp 65001 > nul
echo ========================================
echo          Vue 项目诊断工具
echo ========================================
echo.

echo 🔍 正在检查项目结构...
echo.

REM 检查关键文件是否存在
echo 📂 检查关键文件：
if exist "package.json" (
    echo ✅ package.json - 存在
) else (
    echo ❌ package.json - 缺失
)

if exist "vue.config.js" (
    echo ✅ vue.config.js - 存在
) else (
    echo ❌ vue.config.js - 缺失
)

if exist "src\router\index.js" (
    echo ✅ src\router\index.js - 存在
) else (
    echo ❌ src\router\index.js - 缺失
)

if exist "src\permission.js" (
    echo ✅ src\permission.js - 存在
) else (
    echo ❌ src\permission.js - 缺失
)

echo.
echo 🧭 检查路由组件文件：
set "missing_files="

if exist "src\views\agent\list.vue" (
    echo ✅ src\views\agent\list.vue - 存在
) else (
    echo ❌ src\views\agent\list.vue - 缺失
    set "missing_files=1"
)

if exist "src\views\merchant\list.vue" (
    echo ✅ src\views\merchant\list.vue - 存在
) else (
    echo ❌ src\views\merchant\list.vue - 缺失
    set "missing_files=1"
)

if exist "src\views\store\index.vue" (
    echo ✅ src\views\store\index.vue - 存在
) else (
    echo ❌ src\views\store\index.vue - 缺失
    set "missing_files=1"
)

if exist "src\views\store\bangding.vue" (
    echo ✅ src\views\store\bangding.vue - 存在
) else (
    echo ❌ src\views\store\bangding.vue - 缺失
    set "missing_files=1"
)

if exist "src\views\store\shipin.vue" (
    echo ✅ src\views\store\shipin.vue - 存在
) else (
    echo ❌ src\views\store\shipin.vue - 缺失
    set "missing_files=1"
)

if exist "src\views\store\daka.vue" (
    echo ✅ src\views\store\daka.vue - 存在
) else (
    echo ❌ src\views\store\daka.vue - 缺失
    set "missing_files=1"
)

if exist "src\views\finance\overview.vue" (
    echo ✅ src\views\finance\overview.vue - 存在
) else (
    echo ❌ src\views\finance\overview.vue - 缺失
    set "missing_files=1"
)

if exist "src\views\finance\record.vue" (
    echo ✅ src\views\finance\record.vue - 存在
) else (
    echo ❌ src\views\finance\record.vue - 缺失
    set "missing_files=1"
)

echo.
echo 🛠️ 检查Node.js和npm：
node --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js - 未安装
) else (
    echo ✅ Node.js - 已安装
    node --version | echo     版本：%input%
)

npm --version > nul 2>&1
if errorlevel 1 (
    echo ❌ npm - 未安装
) else (
    echo ✅ npm - 已安装
    npm --version | echo     版本：%input%
)

echo.
echo 🔧 检查依赖：
if exist "node_modules" (
    echo ✅ node_modules - 存在
) else (
    echo ❌ node_modules - 缺失，需要运行 npm install
)

echo.
echo 📋 诊断总结：
echo ========================================

if defined missing_files (
    echo ⚠️  发现缺失的组件文件
    echo 🔧 建议：检查是否所有Vue组件都已正确创建
) else (
    echo ✅ 所有必需的组件文件都存在
)

echo.
echo 🎯 下一步操作建议：
echo 1. 运行 restart-dev.bat 重启开发服务器
echo 2. 打开浏览器访问 http://localhost:8081
echo 3. 使用 vue-router-fix.html 测试路由
echo 4. 检查浏览器控制台是否有错误信息
echo.

echo 🔗 测试工具：
echo • 路由修复工具: vue-router-fix.html
echo • 导航调试页面: debug-navigation.html
echo • 项目诊断工具: diagnose.bat (当前脚本)
echo.

pause
