<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        .section h2 {
            color: #495057;
            margin-top: 0;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .nav-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .nav-link {
            display: block;
            padding: 15px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            text-align: center;
            transition: background-color 0.3s;
        }
        .nav-link:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-result {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 6px;
        }
        .route-info {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #28a745;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background: #218838;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .console-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
        }
        .debug-info h3 {
            margin-top: 0;
            color: #495057;
        }
        .debug-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .debug-info li {
            margin: 5px 0;
        }
        .iframe-container {
            margin-top: 20px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            overflow: hidden;
        }
        .iframe-container iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 导航调试页面</h1>
        
        <div class="section">
            <h2>🌐 服务器状态检查</h2>
            <div id="server-status" class="status info">正在检查服务器状态...</div>
            <button class="btn" onclick="checkServerStatus()">重新检查服务器</button>
        </div>

        <div class="section">
            <h2>🧭 路由导航测试</h2>
            <div class="nav-links">
                <a href="http://localhost:8081/" class="nav-link" target="_blank">🏠 工作台</a>
                <a href="http://localhost:8081/agent/list" class="nav-link" target="_blank">👥 代理列表</a>
                <a href="http://localhost:8081/merchant/list" class="nav-link" target="_blank">🏪 商家列表</a>
                <a href="http://localhost:8081/finance/overview" class="nav-link" target="_blank">💰 财务概览</a>
                <a href="http://localhost:8081/system/menu" class="nav-link" target="_blank">⚙️ 系统管理</a>
                <a href="http://localhost:8081/store/index" class="nav-link" target="_blank">🏪 店铺概览</a>
                <a href="http://localhost:8081/changelog/index" class="nav-link" target="_blank">📋 更新日志</a>
            </div>
        </div>

        <div class="section">
            <h2>🏪 店铺管理子菜单测试</h2>
            <div class="nav-links">
                <a href="http://localhost:8081/store/index" class="nav-link" target="_blank">🏪 店铺概览</a>
                <a href="http://localhost:8081/store/bangding" class="nav-link" target="_blank">🔗 绑定平台</a>
                <a href="http://localhost:8081/store/shipin" class="nav-link" target="_blank">🎥 视频管理</a>
                <a href="http://localhost:8081/store/daka" class="nav-link" target="_blank">⏰ 打卡管理</a>
            </div>
        </div>

        <div class="section">
            <h2>🔍 路由调试工具</h2>
            <button class="btn" onclick="testAllRoutes()">测试所有路由</button>
            <button class="btn btn-secondary" onclick="clearResults()">清除结果</button>
            <button class="btn" onclick="checkConsoleErrors()">检查控制台错误</button>
            <div id="test-results" class="test-result" style="display: none;">
                <h3>测试结果:</h3>
                <div id="results-content"></div>
            </div>
        </div>

        <div class="section">
            <h2>🖥️ 实时控制台输出</h2>
            <div id="console-output" class="console-output">
                控制台输出将显示在这里...
            </div>
            <button class="btn" onclick="clearConsole()">清除控制台</button>
        </div>

        <div class="section">
            <h2>🔧 调试信息</h2>
            <div class="debug-info">
                <h3>当前状态:</h3>
                <ul>
                    <li>服务器地址: <code>http://localhost:8081</code></li>
                    <li>开发环境: <code>NODE_ENV=development</code></li>
                    <li>权限检查: <code>已禁用（开发环境）</code></li>
                    <li>路由模式: <code>history</code></li>
                    <li>构建状态: <code id="build-status">未知</code></li>
                </ul>
                
                <h3>可能的问题:</h3>
                <ul>
                    <li>组件渲染问题：模板或渲染函数未定义</li>
                    <li>路由配置错误：导入路径不正确</li>
                    <li>权限拦截器问题：开发环境应该跳过验证</li>
                    <li>Vue组件语法错误：检查.vue文件格式</li>
                </ul>
                
                <h3>解决方案:</h3>
                <ul>
                    <li>确保所有.vue文件都有正确的&lt;template&gt;标签</li>
                    <li>检查路由配置中的组件导入路径</li>
                    <li>确认权限拦截器在开发环境下正确跳过</li>
                    <li>检查浏览器控制台的详细错误信息</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎯 嵌入式测试</h2>
            <p>下面将尝试在iframe中加载应用程序:</p>
            <div class="iframe-container">
                <iframe src="http://localhost:8081/" onload="onIframeLoad()" onerror="onIframeError()"></iframe>
            </div>
        </div>
    </div>

    <script>
        let consoleOutput = document.getElementById('console-output');
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;

        // 重写console方法以显示在页面上
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            addToConsole('LOG', args.join(' '));
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            addToConsole('ERROR', args.join(' '));
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            addToConsole('WARN', args.join(' '));
        };

        function addToConsole(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'ERROR' ? '#ff6b6b' : type === 'WARN' ? '#ffa502' : '#4ecdc4';
            consoleOutput.innerHTML += `<div style="color: ${color}; margin: 2px 0;"><span style="color: #718096;">[${timestamp}]</span> <span style="color: ${color};">[${type}]</span> ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function clearConsole() {
            consoleOutput.innerHTML = '控制台输出将显示在这里...';
        }

        function checkServerStatus() {
            const statusElement = document.getElementById('server-status');
            statusElement.innerHTML = '正在检查服务器状态...';
            statusElement.className = 'status info';

            fetch('http://localhost:8081/')
                .then(response => {
                    if (response.ok) {
                        statusElement.innerHTML = '✅ 服务器运行正常 (HTTP ' + response.status + ')';
                        statusElement.className = 'status success';
                        document.getElementById('build-status').textContent = '正常';
                    } else {
                        statusElement.innerHTML = '⚠️ 服务器响应异常 (HTTP ' + response.status + ')';
                        statusElement.className = 'status warning';
                    }
                })
                .catch(error => {
                    statusElement.innerHTML = '❌ 服务器连接失败: ' + error.message;
                    statusElement.className = 'status error';
                    document.getElementById('build-status').textContent = '连接失败';
                });
        }

        function testAllRoutes() {
            const routes = [
                { path: '/', name: '工作台' },
                { path: '/agent/list', name: '代理列表' },
                { path: '/merchant/list', name: '商家列表' },
                { path: '/finance/overview', name: '财务概览' },
                { path: '/system/menu', name: '系统管理' },
                { path: '/store/index', name: '店铺概览' },
                { path: '/store/bangding', name: '绑定平台' },
                { path: '/store/shipin', name: '视频管理' },
                { path: '/store/daka', name: '打卡管理' },
                { path: '/changelog/index', name: '更新日志' }
            ];

            const resultsElement = document.getElementById('test-results');
            const contentElement = document.getElementById('results-content');
            
            resultsElement.style.display = 'block';
            contentElement.innerHTML = '<p>正在测试路由...</p>';

            let results = [];
            let completed = 0;

            routes.forEach((route, index) => {
                fetch('http://localhost:8081' + route.path)
                    .then(response => {
                        const status = response.ok ? '✅' : '❌';
                        const statusText = response.ok ? '正常' : '错误';
                        results[index] = `<div class="route-info">${status} ${route.name} (${route.path}) - ${statusText} (${response.status})</div>`;
                    })
                    .catch(error => {
                        results[index] = `<div class="route-info">❌ ${route.name} (${route.path}) - 连接失败: ${error.message}</div>`;
                    })
                    .finally(() => {
                        completed++;
                        if (completed === routes.length) {
                            contentElement.innerHTML = results.join('');
                        }
                    });
            });
        }

        function clearResults() {
            document.getElementById('test-results').style.display = 'none';
        }

        function checkConsoleErrors() {
            addToConsole('INFO', '正在检查控制台错误...');
            addToConsole('INFO', '请查看浏览器开发者工具控制台以获取详细错误信息');
            
            // 检查常见的错误模式
            setTimeout(() => {
                addToConsole('INFO', '常见错误检查:');
                addToConsole('INFO', '1. 检查是否有Vue组件渲染错误');
                addToConsole('INFO', '2. 检查路由配置是否正确');
                addToConsole('INFO', '3. 检查权限拦截器是否正常工作');
                addToConsole('INFO', '4. 检查网络请求是否成功');
            }, 1000);
        }

        function onIframeLoad() {
            addToConsole('INFO', 'iframe加载成功');
        }

        function onIframeError() {
            addToConsole('ERROR', 'iframe加载失败');
        }

        // 页面加载时自动检查服务器状态
        window.addEventListener('load', function() {
            checkServerStatus();
            addToConsole('INFO', '调试页面加载完成');
        });

        // 监听窗口错误
        window.addEventListener('error', function(e) {
            addToConsole('ERROR', `全局错误: ${e.message} at ${e.filename}:${e.lineno}`);
        });
    </script>
</body>
</html>
