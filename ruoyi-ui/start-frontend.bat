@echo off
echo ==========================================
echo Start Frontend Development Server
echo ==========================================
echo.

echo Current directory: %CD%
echo.

if not exist "package.json" (
    echo [ERROR] package.json not found
    echo Please make sure you are in the ruoyi-ui directory
    pause
    exit /b 1
)

echo [OK] Found package.json
echo.

echo Searching for Node.js installation...
echo.

REM Define possible Node.js paths
set "NODEJS_PATHS="C:\Program Files\nodejs" "C:\Program Files (x86)\nodejs" "C:\nodejs" "%LOCALAPPDATA%\Programs\nodejs""

set "FOUND_NODEJS="
set "FOUND_NPM="

REM Search for Node.js
for %%p in (%NODEJS_PATHS%) do (
    if exist "%%~p\node.exe" (
        echo [FOUND] node.exe: %%~p
        set "FOUND_NODEJS=%%~p\node.exe"
    )
    if exist "%%~p\npm.cmd" (
        echo [FOUND] npm.cmd: %%~p
        set "FOUND_NPM=%%~p\npm.cmd"
    )
)

if "%FOUND_NPM%"=="" (
    echo.
    echo [ERROR] npm.cmd not found
    echo.
    echo Please choose a solution:
    echo [1] Install Node.js
    echo [2] Manually specify Node.js path
    echo [3] Use online version (skip frontend)
    echo.
    set /p solution=Please choose (1-3): 
    
    if "!solution!"=="1" (
        call install-nodejs.bat
        exit /b
    )
    if "!solution!"=="2" (
        goto manual_path
    )
    if "!solution!"=="3" (
        goto online_only
    )
    
    echo Invalid choice
    pause
    exit /b 1
)

echo.
echo Using npm: %FOUND_NPM%
echo.

echo Starting frontend development server...
echo.

REM Set temporary environment variable
set "PATH=%PATH%;%FOUND_NPM:~0,-8%"

REM Start frontend
"%FOUND_NPM%" run dev

if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Failed to start
    echo.
    echo Possible reasons:
    echo 1. Dependencies not installed, please run: "%FOUND_NPM%" install
    echo 2. Port is occupied
    echo 3. Configuration file error
    echo.
    
    echo Do you want to install dependencies? (y/n)
    set /p install_deps=
    if /i "!install_deps!"=="y" (
        echo Installing dependencies...
        "%FOUND_NPM%" install
        echo.
        echo Restarting frontend...
        "%FOUND_NPM%" run dev
    )
)

pause
exit /b

:manual_path
echo.
echo Please enter Node.js installation path (e.g., C:\Program Files\nodejs):
set /p manual_nodejs_path=

if exist "%manual_nodejs_path%\npm.cmd" (
    echo [OK] Found npm: %manual_nodejs_path%\npm.cmd
    "%manual_nodejs_path%\npm.cmd" run dev
) else (
    echo [ERROR] Invalid path or npm.cmd not found
)

pause
exit /b

:online_only
echo.
echo ==========================================
echo Online Version Solution
echo ==========================================
echo.

echo Due to Node.js environment issues, we recommend:
echo.
echo 1. Backend: Use IDEA or Eclipse to start Spring Boot
echo 2. Frontend: Use built version or online version
echo 3. Fly-Cut: Use online version https://fly-cut.vercel.app/
echo.

echo Opening relevant links...
start https://fly-cut.vercel.app/

echo.
echo If you need local frontend, please fix Node.js environment first:
echo 1. Run: install-nodejs.bat
echo 2. Or manually download from https://nodejs.org/
echo.

pause
exit /b
