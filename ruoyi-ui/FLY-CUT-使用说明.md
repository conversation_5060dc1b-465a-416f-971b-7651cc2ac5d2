# Fly-Cut 本地版使用说明

## 🎯 概述

本项目已集成了完整的 Fly-Cut 视频编辑器，支持多种启动方式，确保在任何环境下都能正常使用。

## 🚀 启动方式

系统会按以下优先级自动尝试不同的启动方式：

### 1. 本地服务器模式 (推荐)
- **地址**: `http://localhost:3001/`
- **优势**: 最佳性能，完整功能
- **要求**: 需要启动本地服务器

### 2. 本地文件模式
- **地址**: `file:///` 协议
- **优势**: 无需服务器，直接访问
- **限制**: 某些功能可能受限

### 3. 在线版本模式 (备用)
- **地址**: `https://fly-cut.vercel.app/`
- **优势**: 始终可用，无需本地配置
- **要求**: 需要网络连接

## 🔧 启动本地服务器

### 方法1: PowerShell (推荐)
```powershell
cd ruoyi-ui
powershell -ExecutionPolicy Bypass -File start-flycut-powershell.ps1
```

### 方法2: Python
```bash
cd ruoyi-ui
python start-flycut-server.py
```

### 方法3: Node.js
```bash
cd ruoyi-ui
node start-flycut.js
```

### 方法4: 批处理文件
```cmd
cd ruoyi-ui
start-flycut-simple.bat
```

## 📱 使用方法

1. **启动后台服务** (如果还没启动)
   ```bash
   cd ry-vue-flowable-xg-main
   mvn spring-boot:run -pl ruoyi-admin
   ```

2. **启动前端服务**
   ```bash
   cd ruoyi-ui
   npm run dev
   ```

3. **启动 Fly-Cut 服务器** (可选，用于本地模式)
   ```bash
   # 选择上面任一方法启动
   ```

4. **访问应用**
   - 打开浏览器访问: `http://localhost:8081`
   - 进入"模板管理"页面
   - 点击任意模板的"编辑模板"按钮
   - 系统会自动选择最佳的启动方式

## 🎬 Fly-Cut 功能特性

### 核心功能
- ✅ **多轨道时间线**: 视频、音频、文字轨道
- ✅ **专业编辑工具**: 剪切、分割、合并、复制
- ✅ **视频特效**: 滤镜、调色、变换、动画
- ✅ **音频处理**: 音量调节、淡入淡出、音频同步
- ✅ **文字编辑**: 字体、颜色、动画、位置
- ✅ **转场效果**: 各种专业转场
- ✅ **导出功能**: 多种格式和质量选项

### 技术特性
- ✅ **WebCodecs**: 原生视频处理技术
- ✅ **实时预览**: 30fps 流畅预览
- ✅ **专业界面**: 类似 CapCut 的专业界面
- ✅ **跨平台**: 支持 Windows、Mac、Linux

## 🔍 故障排除

### 问题1: 本地服务器启动失败
**解决方案**:
1. 检查端口 3001 是否被占用
2. 尝试使用其他启动方法
3. 系统会自动回退到在线版本

### 问题2: 编辑器加载空白
**解决方案**:
1. 检查浏览器控制台错误信息
2. 确保 `public/fly-cut/` 目录中有完整文件
3. 尝试刷新页面或清除浏览器缓存

### 问题3: 无法访问在线版本
**解决方案**:
1. 检查网络连接
2. 确认防火墙设置
3. 尝试使用本地文件模式

## 📊 性能优化建议

### 本地服务器模式
- 最佳性能和功能完整性
- 推荐用于日常开发和使用

### 在线版本模式
- 适合快速测试和演示
- 网络延迟可能影响体验

## 🎯 开发扩展

### 添加自定义功能
可以在 `dijin.vue` 中的 `editTemplate` 方法中添加自定义逻辑：

```javascript
editTemplate(template) {
  // 自定义预处理
  this.preprocessTemplate(template)
  
  // 启动编辑器
  // ... 现有代码
}
```

### 集成阿里云功能
可以监听 Fly-Cut 的导出事件，自动上传到阿里云：

```javascript
// 监听来自 Fly-Cut 的消息
window.addEventListener('message', function(event) {
  if (event.data.type === 'EXPORT_COMPLETE') {
    uploadToAliyun(event.data.videoBlob)
  }
})
```

## 📞 技术支持

如果遇到问题，请检查：
1. 浏览器控制台的错误信息
2. 网络连接状态
3. 本地服务器启动状态
4. 文件完整性

系统设计了多重备用方案，确保在任何情况下都能使用 Fly-Cut 编辑器。
