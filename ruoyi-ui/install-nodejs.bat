@echo off
echo ==========================================
echo Node.js Installation Helper
echo ==========================================
echo.

echo Checking Node.js installation status...
echo.

REM Check if already installed
node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [OK] Node.js is already installed
    node --version
    npm --version
    echo.
    echo Node.js is correctly installed and configured!
    pause
    exit /b 0
)

echo [ERROR] Node.js not installed or not configured correctly
echo.

echo Please choose installation method:
echo.
echo [1] Download and install Node.js automatically
echo [2] Manual download and install
echo [3] Fix existing installation
echo [0] Exit
echo.
set /p choice=Please choose (0-3): 

if "%choice%"=="1" goto auto_install
if "%choice%"=="2" goto manual_install
if "%choice%"=="3" goto fix_existing
if "%choice%"=="0" goto exit
echo Invalid choice
pause
exit /b 1

:auto_install
echo.
echo ==========================================
echo Automatic Node.js Installation
echo ==========================================
echo.

echo Downloading Node.js LTS version...
echo.

REM Create temp directory
if not exist "%TEMP%\nodejs_install" mkdir "%TEMP%\nodejs_install"
cd /d "%TEMP%\nodejs_install"

echo Using PowerShell to download Node.js...
echo.

powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://nodejs.org/dist/v18.17.0/node-v18.17.0-x64.msi' -OutFile 'nodejs.msi'}"

if exist "nodejs.msi" (
    echo [OK] Download completed
    echo.
    echo Installing Node.js...
    echo Please follow the installation wizard
    echo.
    
    REM Silent installation
    msiexec /i nodejs.msi /quiet /norestart
    
    echo Installation completed!
    echo.
    echo Please reopen command prompt and test: npm --version
) else (
    echo [ERROR] Download failed
    echo Please check network connection or install manually
)

pause
goto exit

:manual_install
echo.
echo ==========================================
echo Manual Installation Guide
echo ==========================================
echo.

echo Please follow these steps to install Node.js manually:
echo.
echo 1. Open browser and visit: https://nodejs.org/
echo 2. Click "LTS" version to download (recommended)
echo 3. Run the downloaded installer
echo 4. During installation, make sure "Add to PATH" is checked
echo 5. Complete installation and restart command prompt
echo 6. Test: npm --version
echo.

echo Opening Node.js official website...
start https://nodejs.org/

pause
goto exit

:fix_existing
echo.
echo ==========================================
echo Fix Existing Installation
echo ==========================================
echo.

echo Searching for existing Node.js installation...
echo.

call 查找nodejs.bat

echo.
echo If Node.js was found, please run:
echo fix-environment.bat (requires administrator privileges)
echo.

pause
goto exit

:exit
echo.
echo Installation helper finished
echo.
echo If installation was successful, please reopen command prompt
echo Then test: npm --version
echo.
pause
exit
