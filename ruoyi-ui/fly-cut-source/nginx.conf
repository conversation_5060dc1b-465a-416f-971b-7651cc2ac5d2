
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
    '$status $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 650;
    types_hash_max_size 4096;

    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Load modular configuration files from the /etc/nginx/conf.d directory.
    # See http://nginx.org/en/docs/ngx_core_module.html#include
    # for more information.
    ignore_invalid_headers off;

    server {
        listen 80;
        listen [::]:80;
        server_name _;
        root /usr/share/nginx/html;

        index index.html;
        client_max_body_size 100m;

        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Credentials' 'true';
        add_header 'Access-Control-Allow-Methods' '*';
        add_header Cache-Control 'no-cache, no-store';
        add_header Cross-Origin-Opener-Policy "same-origin";
        add_header Cross-Origin-Embedder-Policy "require-corp";

        # Load configuration files for the default server block.

        location /dianshi {
            try_files $uri $uri/ /index.html;
            # root /usr/share/nginx/html/dianshi/;
        }

        location /editor {
            try_files $uri $uri/ /index.html;
            # root /usr/share/nginx/html/editor/;
        }

        # location / {
        #     # root /usr/share/nginx/html/dianshi/;
        #     root /usr/share/nginx/html/editor/;
        # }

        error_page 404 /404.html;
        location = /404.html {
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
        }
    }
}
