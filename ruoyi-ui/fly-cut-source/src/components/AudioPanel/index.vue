<template>
  <div class="p-4 flex-1 overflow-hidden flex flex-col">
    <div class="bg-zinc-200 h-10 flex items-center justify-center rounded text-sm text-gray-900 cursor-pointer" @click="onUpload">
      <i class="iconfont icon-shangchuan_line mr-2" />
      本地音频
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { usePlayerState } from '@/stores/playerState';
  import { useTrackState } from '@/stores/trackState';
  import { AudioClip } from '@webav/av-cliper';
  import { ElMessage } from 'element-plus';
  import { selectFile } from '@/utils/file';
  import { getMD5 } from '@/class/Base';
  import { AudioTrack } from '@/class/AudioTrack';
  import { audioDecoder } from '@/utils/webcodecs';

  const selectedMenu = ref('recommend');
  function onSelect(selected: { value: string }) {
    selectedMenu.value = selected.value;
  }

  const trackStore = useTrackState();
  const playStore = usePlayerState();

  async function onUpload() {
    console.log('🎵 AudioPanel onUpload started');

    try {
      /**
       * TODO: 待优化，有些任务可以并发
       */
      const files = await selectFile({ accept: 'audio/*', multiple: false });
      console.log('🎵 Selected files:', files);

      const id = await getMD5(await files[0].arrayBuffer());
      console.log('🎵 Generated ID:', id);

      const clip = await audioDecoder.decode({ id, stream: files[0].stream(), type: files[0].type });
      console.log('🎵 Decoded clip:', clip);

      if (!clip) {
        // 提示解析视频失败
        ElMessage.error('解析音频失败');
        return;
      }

      const audioTrack = new AudioTrack({
        id,
        url: URL.createObjectURL(files[0]),
        name: files[0].name,
        format: files[0].type,
        duration: Math.round(clip.meta.duration / 1e6)
      }, playStore.playStartFrame);

      console.log('🎵 Created AudioTrack:', audioTrack);
      console.log('🎵 AudioTrack properties:', {
        id: audioTrack.id,
        type: audioTrack.type,
        name: audioTrack.name,
        silent: audioTrack.silent,
        volume: audioTrack.volume
      });

      trackStore.addTrack(audioTrack);
      console.log('🎵 Added track to store, current trackList:', trackStore.trackList);

    } catch (error) {
      console.error('🎵 Error in onUpload:', error);
      ElMessage.error('上传音频失败: ' + error.message);
    }
  }
</script>

<style scoped>

</style>