import type { AudioTrack } from './AudioTrack';
import type { VideoTrack } from './VideoTrack';
import type { ImageTrack } from './ImageTrack';
import type { TextTrack } from './TextTrack';
import type { TransitionTrack } from './TransitionTrack';
import type { EffectTrack } from './EffectTrack';
import type { FilterTrack } from './FilterTrack';

export type Track = AudioTrack | ImageTrack | TextTrack | VideoTrack | TransitionTrack | EffectTrack | FilterTrack;

export interface TrackLineItem {
  type: Track['type'],
  main?: boolean,
  list: Track[]
}