import { uniqueId } from 'lodash-es';
import type { BaseTractItem, TrackType } from './Base';
import { UnitFrame2μs } from '@/data/trackConfig';

export interface EffectSource {
  id: string;
  name: string;
  type: 'blur' | 'brightness' | 'contrast' | 'saturation' | 'hue' | 'sepia' | 'grayscale';
  intensity: number;
  keyframes?: Array<{
    time: number;
    value: number;
  }>;
}

export class EffectTrack implements BaseTractItem {
  id: string;
  type: TrackType = 'effect';
  source: EffectSource;
  name: string;
  frameCount: number;
  start: number;
  end: number;
  effectType: 'blur' | 'brightness' | 'contrast' | 'saturation' | 'hue' | 'sepia' | 'grayscale';
  intensity: number;
  keyframes?: Array<{
    time: number;
    value: number;
  }>;

  constructor(source: EffectSource, curFrame: number, duration: number = 5) {
    // 设置ID
    this.id = uniqueId();
    // 设置效果信息
    this.source = source;
    // 获取效果名称
    this.name = source.name;
    // 获取效果类型
    this.effectType = source.type;
    // 获取效果强度
    this.intensity = source.intensity;
    // 获取关键帧
    this.keyframes = source.keyframes;

    // 获取效果时长，转换为frameCount
    this.frameCount = duration * 30; // 假设30fps
    this.start = curFrame;
    this.end = this.start + this.frameCount;
  }

  // 应用效果
  apply(element: HTMLElement | HTMLCanvasElement, progress: number) {
    const currentIntensity = this.getCurrentIntensity(progress);
    
    switch (this.effectType) {
      case 'blur':
        this.applyBlur(element, currentIntensity);
        break;
      case 'brightness':
        this.applyBrightness(element, currentIntensity);
        break;
      case 'contrast':
        this.applyContrast(element, currentIntensity);
        break;
      case 'saturation':
        this.applySaturation(element, currentIntensity);
        break;
      case 'hue':
        this.applyHue(element, currentIntensity);
        break;
      case 'sepia':
        this.applySepia(element, currentIntensity);
        break;
      case 'grayscale':
        this.applyGrayscale(element, currentIntensity);
        break;
    }
  }

  private getCurrentIntensity(progress: number): number {
    if (!this.keyframes || this.keyframes.length === 0) {
      return this.intensity;
    }

    // 根据关键帧计算当前强度
    const currentTime = progress;
    
    // 找到当前时间点前后的关键帧
    let beforeFrame = this.keyframes[0];
    let afterFrame = this.keyframes[this.keyframes.length - 1];
    
    for (let i = 0; i < this.keyframes.length - 1; i++) {
      if (currentTime >= this.keyframes[i].time && currentTime <= this.keyframes[i + 1].time) {
        beforeFrame = this.keyframes[i];
        afterFrame = this.keyframes[i + 1];
        break;
      }
    }

    // 线性插值
    if (beforeFrame === afterFrame) {
      return beforeFrame.value;
    }

    const t = (currentTime - beforeFrame.time) / (afterFrame.time - beforeFrame.time);
    return beforeFrame.value + (afterFrame.value - beforeFrame.value) * t;
  }

  private applyBlur(element: HTMLElement | HTMLCanvasElement, intensity: number) {
    if (element instanceof HTMLElement) {
      element.style.filter = `blur(${intensity}px)`;
    }
  }

  private applyBrightness(element: HTMLElement | HTMLCanvasElement, intensity: number) {
    if (element instanceof HTMLElement) {
      element.style.filter = `brightness(${intensity}%)`;
    }
  }

  private applyContrast(element: HTMLElement | HTMLCanvasElement, intensity: number) {
    if (element instanceof HTMLElement) {
      element.style.filter = `contrast(${intensity}%)`;
    }
  }

  private applySaturation(element: HTMLElement | HTMLCanvasElement, intensity: number) {
    if (element instanceof HTMLElement) {
      element.style.filter = `saturate(${intensity}%)`;
    }
  }

  private applyHue(element: HTMLElement | HTMLCanvasElement, intensity: number) {
    if (element instanceof HTMLElement) {
      element.style.filter = `hue-rotate(${intensity}deg)`;
    }
  }

  private applySepia(element: HTMLElement | HTMLCanvasElement, intensity: number) {
    if (element instanceof HTMLElement) {
      element.style.filter = `sepia(${intensity}%)`;
    }
  }

  private applyGrayscale(element: HTMLElement | HTMLCanvasElement, intensity: number) {
    if (element instanceof HTMLElement) {
      element.style.filter = `grayscale(${intensity}%)`;
    }
  }

  // 组合多个效果
  applyCombined(element: HTMLElement | HTMLCanvasElement, effects: EffectTrack[], progress: number) {
    const filters: string[] = [];
    
    effects.forEach(effect => {
      const intensity = effect.getCurrentIntensity(progress);
      switch (effect.effectType) {
        case 'blur':
          filters.push(`blur(${intensity}px)`);
          break;
        case 'brightness':
          filters.push(`brightness(${intensity}%)`);
          break;
        case 'contrast':
          filters.push(`contrast(${intensity}%)`);
          break;
        case 'saturation':
          filters.push(`saturate(${intensity}%)`);
          break;
        case 'hue':
          filters.push(`hue-rotate(${intensity}deg)`);
          break;
        case 'sepia':
          filters.push(`sepia(${intensity}%)`);
          break;
        case 'grayscale':
          filters.push(`grayscale(${intensity}%)`);
          break;
      }
    });

    if (element instanceof HTMLElement) {
      element.style.filter = filters.join(' ');
    }
  }

  // 生成合成对象
  async combine() {
    // 效果的合成逻辑
    // 这里需要根据具体的视频处理库来实现
    return null;
  }
}
