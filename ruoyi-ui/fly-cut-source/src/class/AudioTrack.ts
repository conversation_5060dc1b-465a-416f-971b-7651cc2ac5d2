import { uniqueId } from 'lodash-es';
import type { BaseTractItem, TrackType } from './Base';
import { UnitFrame2μs } from '@/data/trackConfig';
import { audioDecoder, splitClip } from '@/utils/webcodecs';
import { OffscreenSprite } from '@webav/av-cliper';

export interface AudioSource {
  id: string,
  url: string,
  name: string,
  format: string,
  duration: number
}

export class AudioTrack implements BaseTractItem {
  id: string;
  type: TrackType = 'audio';
  source: AudioSource;
  name: string;
  format: string;
  frameCount: number;
  start: number;
  end: number;
  offsetL: number;
  offsetR: number;
  private _silent: boolean = false; // 静音属性
  private _volume: number = 1.0; // 音量属性 (0.0 - 1.0)

  // 静音属性的 getter 和 setter
  get silent(): boolean {
    console.log('🎵 AudioTrack get silent:', this._silent);
    return this._silent;
  }

  set silent(value: boolean) {
    console.log('🎵 AudioTrack set silent:', {
      oldValue: this._silent,
      newValue: value,
      trackId: this.id
    });
    this._silent = value;
    this.applyAudioSettings();
  }

  // 音量属性的 getter 和 setter
  get volume(): number {
    return this._volume;
  }

  set volume(value: number) {
    this._volume = Math.max(0, Math.min(1, value)); // 确保音量在0-1范围内
    this.applyAudioSettings();
  }

  // 应用音频设置到 HTML Audio 元素
  private applyAudioSettings() {
    console.log('🎵 AudioTrack applyAudioSettings:', {
      hasAudio: !!this.audio,
      silent: this._silent,
      volume: this._volume,
      trackId: this.id,
      sourceUrl: this.source?.url
    });

    // 如果音频元素不存在但有音频源，则创建音频元素
    if (!this.audio && this.source?.url) {
      console.log('🎵 Creating audio element for settings application');
      this.audio = new Audio(this.source.url);
    }

    if (this.audio) {
      // 使用 muted 属性控制静音，不要同时修改 volume
      this.audio.muted = this._silent;
      // 只有在非静音状态下才设置音量
      if (!this._silent) {
        this.audio.volume = this._volume;
      }
      console.log('🎵 Applied audio settings:', {
        audioMuted: this.audio.muted,
        audioVolume: this.audio.volume,
        silent: this._silent
      });
    } else {
      console.log('🎵 No audio element available to apply settings');
    }
  }
  constructor(source: AudioSource, curFrame: number) {
    // 设置ID
    this.id = uniqueId();
    // 设置音频信息
    this.source = source;
    // 获取文件名称
    this.name = source.name;
    // 获取文件类型
    this.format = source.format;

    // 获取音频时长，转换为frameCount
    this.frameCount = source.duration * 30;
    this.start = curFrame;
    this.end = this.start + this.frameCount;

    // 设置裁剪信息
    this.offsetL = 0;
    this.offsetR = 0;
  }
  audio: HTMLAudioElement | null = null;
  play(currentFrame: number) {
    if (!this.audio) {
      this.audio = new Audio(this.source.url);
      this.applyAudioSettings(); // 初始化时应用音频设置
    }
    if (this.audio?.paused) {
      this.audio.currentTime = (currentFrame - this.start - this.offsetL) / 30;
      this.applyAudioSettings(); // 播放前确保应用最新的音频设置
      this.audio.play();
    }
  }
  pause() {
    if (this.audio && !this.audio.paused) {
      this.audio.pause();
    }
  }

  // 切换静音状态
  toggleMute() {
    this.silent = !this.silent; // 使用 setter，会自动应用设置
  }

  // 设置音量
  setVolume(volume: number) {
    this.volume = volume; // 使用 setter，会自动应用设置
  }


  // 生成合成对象
  async combine() {
    const audio = await audioDecoder.decode({ id: this.source.id });
    const clip = await splitClip(audio, { offsetL: this.offsetL, offsetR: this.offsetR, frameCount: this.frameCount });
    if (!clip) {
      throw new Error('clip is not ready');
    }
    const spr = new OffscreenSprite(clip);
    // TODO：需要支持裁剪
    spr.time = { offset: this.start * UnitFrame2μs, duration: (this.end - this.start) * UnitFrame2μs };

    return spr;
  }
}