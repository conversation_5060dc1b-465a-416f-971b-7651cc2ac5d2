import { uniqueId } from 'lodash-es';
import type { BaseTractItem, TrackType } from './Base';
import { UnitFrame2μs } from '@/data/trackConfig';

export interface FilterSource {
  id: string;
  name: string;
  type: 'vintage' | 'black-white' | 'warm' | 'cool' | 'dramatic';
  strength: number;
}

export class FilterTrack implements BaseTractItem {
  id: string;
  type: TrackType = 'filter';
  source: FilterSource;
  name: string;
  frameCount: number;
  start: number;
  end: number;
  filterType: 'vintage' | 'black-white' | 'warm' | 'cool' | 'dramatic';
  strength: number;

  constructor(source: FilterSource, curFrame: number, duration: number = 5) {
    // 设置ID
    this.id = uniqueId();
    // 设置滤镜信息
    this.source = source;
    // 获取滤镜名称
    this.name = source.name;
    // 获取滤镜类型
    this.filterType = source.type;
    // 获取滤镜强度
    this.strength = source.strength;

    // 获取滤镜时长，转换为frameCount
    this.frameCount = duration * 30; // 假设30fps
    this.start = curFrame;
    this.end = this.start + this.frameCount;
  }

  // 应用滤镜效果
  apply(element: HTMLElement | HTMLCanvasElement, progress: number = 1) {
    const currentStrength = this.strength * progress;
    
    switch (this.filterType) {
      case 'vintage':
        this.applyVintage(element, currentStrength);
        break;
      case 'black-white':
        this.applyBlackWhite(element, currentStrength);
        break;
      case 'warm':
        this.applyWarm(element, currentStrength);
        break;
      case 'cool':
        this.applyCool(element, currentStrength);
        break;
      case 'dramatic':
        this.applyDramatic(element, currentStrength);
        break;
    }
  }

  private applyVintage(element: HTMLElement | HTMLCanvasElement, strength: number) {
    if (element instanceof HTMLElement) {
      const filters = [
        `sepia(${strength * 0.8})`,
        `contrast(${100 + strength * 0.2})`,
        `brightness(${100 + strength * 0.1})`,
        `saturate(${100 - strength * 0.3})`
      ];
      element.style.filter = filters.join(' ');
    }
  }

  private applyBlackWhite(element: HTMLElement | HTMLCanvasElement, strength: number) {
    if (element instanceof HTMLElement) {
      const filters = [
        `grayscale(${strength})`,
        `contrast(${100 + strength * 0.2})`
      ];
      element.style.filter = filters.join(' ');
    }
  }

  private applyWarm(element: HTMLElement | HTMLCanvasElement, strength: number) {
    if (element instanceof HTMLElement) {
      const filters = [
        `hue-rotate(${-strength * 0.1}deg)`,
        `saturate(${100 + strength * 0.2})`,
        `brightness(${100 + strength * 0.05})`
      ];
      element.style.filter = filters.join(' ');
      
      // 添加暖色调叠加
      const overlay = `linear-gradient(rgba(255, 200, 150, ${strength * 0.001}), rgba(255, 200, 150, ${strength * 0.001}))`;
      element.style.background = overlay;
    }
  }

  private applyCool(element: HTMLElement | HTMLCanvasElement, strength: number) {
    if (element instanceof HTMLElement) {
      const filters = [
        `hue-rotate(${strength * 0.1}deg)`,
        `saturate(${100 + strength * 0.1})`,
        `brightness(${100 - strength * 0.02})`
      ];
      element.style.filter = filters.join(' ');
      
      // 添加冷色调叠加
      const overlay = `linear-gradient(rgba(150, 200, 255, ${strength * 0.001}), rgba(150, 200, 255, ${strength * 0.001}))`;
      element.style.background = overlay;
    }
  }

  private applyDramatic(element: HTMLElement | HTMLCanvasElement, strength: number) {
    if (element instanceof HTMLElement) {
      const filters = [
        `contrast(${100 + strength * 0.5})`,
        `saturate(${100 + strength * 0.3})`,
        `brightness(${100 - strength * 0.1})`,
        `blur(${strength * 0.01}px)`
      ];
      element.style.filter = filters.join(' ');
    }
  }

  // 获取滤镜预设
  static getPresets(): FilterSource[] {
    return [
      {
        id: 'vintage-1',
        name: '复古',
        type: 'vintage',
        strength: 80
      },
      {
        id: 'bw-1',
        name: '黑白',
        type: 'black-white',
        strength: 100
      },
      {
        id: 'warm-1',
        name: '暖色调',
        type: 'warm',
        strength: 60
      },
      {
        id: 'cool-1',
        name: '冷色调',
        type: 'cool',
        strength: 60
      },
      {
        id: 'dramatic-1',
        name: '戏剧化',
        type: 'dramatic',
        strength: 70
      }
    ];
  }

  // 生成合成对象
  async combine() {
    // 滤镜的合成逻辑
    // 这里需要根据具体的视频处理库来实现
    return null;
  }
}
