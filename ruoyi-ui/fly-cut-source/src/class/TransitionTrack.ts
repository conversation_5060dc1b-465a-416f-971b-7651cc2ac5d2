import { uniqueId } from 'lodash-es';
import type { BaseTractItem, TrackType } from './Base';
import { UnitFrame2μs } from '@/data/trackConfig';

export interface TransitionSource {
  id: string;
  name: string;
  type: 'fade' | 'wipe' | 'slide' | 'zoom' | 'dissolve';
  duration: number;
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out';
}

export class TransitionTrack implements BaseTractItem {
  id: string;
  type: TrackType = 'transition';
  source: TransitionSource;
  name: string;
  frameCount: number;
  start: number;
  end: number;
  transitionType: 'fade' | 'wipe' | 'slide' | 'zoom' | 'dissolve';
  duration: number;
  easing: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out';

  constructor(source: TransitionSource, curFrame: number) {
    // 设置ID
    this.id = uniqueId();
    // 设置转场信息
    this.source = source;
    // 获取转场名称
    this.name = source.name;
    // 获取转场类型
    this.transitionType = source.type;
    // 获取转场时长
    this.duration = source.duration;
    // 获取缓动类型
    this.easing = source.easing;

    // 获取转场时长，转换为frameCount
    this.frameCount = source.duration * 30; // 假设30fps
    this.start = curFrame;
    this.end = this.start + this.frameCount;
  }

  // 应用转场效果
  apply(fromElement: HTMLElement, toElement: HTMLElement, progress: number) {
    const easedProgress = this.applyEasing(progress);
    
    switch (this.transitionType) {
      case 'fade':
        this.applyFade(fromElement, toElement, easedProgress);
        break;
      case 'slide':
        this.applySlide(fromElement, toElement, easedProgress);
        break;
      case 'zoom':
        this.applyZoom(fromElement, toElement, easedProgress);
        break;
      case 'wipe':
        this.applyWipe(fromElement, toElement, easedProgress);
        break;
      case 'dissolve':
        this.applyDissolve(fromElement, toElement, easedProgress);
        break;
    }
  }

  private applyEasing(progress: number): number {
    switch (this.easing) {
      case 'ease-in':
        return progress * progress;
      case 'ease-out':
        return 1 - (1 - progress) * (1 - progress);
      case 'ease-in-out':
        return progress < 0.5 
          ? 2 * progress * progress 
          : 1 - Math.pow(-2 * progress + 2, 2) / 2;
      default:
        return progress;
    }
  }

  private applyFade(fromElement: HTMLElement, toElement: HTMLElement, progress: number) {
    fromElement.style.opacity = (1 - progress).toString();
    toElement.style.opacity = progress.toString();
  }

  private applySlide(fromElement: HTMLElement, toElement: HTMLElement, progress: number) {
    const slideDistance = fromElement.offsetWidth;
    fromElement.style.transform = `translateX(${-slideDistance * progress}px)`;
    toElement.style.transform = `translateX(${slideDistance * (1 - progress)}px)`;
  }

  private applyZoom(fromElement: HTMLElement, toElement: HTMLElement, progress: number) {
    const fromScale = 1 - progress * 0.5;
    const toScale = 0.5 + progress * 0.5;
    fromElement.style.transform = `scale(${fromScale})`;
    fromElement.style.opacity = (1 - progress).toString();
    toElement.style.transform = `scale(${toScale})`;
    toElement.style.opacity = progress.toString();
  }

  private applyWipe(fromElement: HTMLElement, toElement: HTMLElement, progress: number) {
    const clipPath = `inset(0 ${(1 - progress) * 100}% 0 0)`;
    toElement.style.clipPath = clipPath;
    fromElement.style.opacity = '1';
    toElement.style.opacity = '1';
  }

  private applyDissolve(fromElement: HTMLElement, toElement: HTMLElement, progress: number) {
    // 溶解效果：随机像素点逐渐替换
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      // 这里可以实现更复杂的溶解算法
      this.applyFade(fromElement, toElement, progress);
    }
  }

  // 生成合成对象
  async combine() {
    // 转场效果的合成逻辑
    // 这里需要根据具体的视频处理库来实现
    return null;
  }
}
