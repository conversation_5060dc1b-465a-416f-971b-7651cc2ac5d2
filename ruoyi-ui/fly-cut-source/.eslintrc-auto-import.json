{"globals": {"EffectScope": "readonly", "computed": "readonly", "createApp": "readonly", "customRef": "readonly", "defineAsyncComponent": "readonly", "defineComponent": "readonly", "effectScope": "readonly", "getCurrentInstance": "readonly", "getCurrentScope": "readonly", "h": "readonly", "inject": "readonly", "isProxy": "readonly", "isReactive": "readonly", "isReadonly": "readonly", "isRef": "readonly", "markRaw": "readonly", "nextTick": "readonly", "onActivated": "readonly", "onBeforeMount": "readonly", "onBeforeUnmount": "readonly", "onBeforeUpdate": "readonly", "onDeactivated": "readonly", "onErrorCaptured": "readonly", "onMounted": "readonly", "onRenderTracked": "readonly", "onRenderTriggered": "readonly", "onScopeDispose": "readonly", "onServerPrefetch": "readonly", "onUnmounted": "readonly", "onUpdated": "readonly", "provide": "readonly", "reactive": "readonly", "readonly": "readonly", "ref": "readonly", "resolveComponent": "readonly", "resolveDirective": "readonly", "shallowReactive": "readonly", "shallowReadonly": "readonly", "shallowRef": "readonly", "toRaw": "readonly", "toRef": "readonly", "toRefs": "readonly", "triggerRef": "readonly", "unref": "readonly", "useAttrs": "readonly", "useCssModule": "readonly", "useCssVars": "readonly", "useSlots": "readonly", "watch": "readonly", "watchEffect": "readonly", "watchPostEffect": "readonly", "watchSyncEffect": "readonly"}}