# fly-cut

## Introduction
A web-based editing tool implemented with Webcodecs + Vue3 <br/>
If you find it useful, please 🌟 Star 🌟 to support it 🫣 <br/>

## Features
![](coverImage/map.png)

## Preview
![](coverImage/preview.gif)

## References
- https://github.com/hughfenghen/WebAV Processes audio and video data in the browser based on WebCodecs; used for processing audio and video files
- https://github.com/Cc-Edit/CcClip A web-based editing tool implemented with Vue3 + ffmpeg, the main functionality of this project is based on this project