{"name": "fly-cut", "version": "0.0.0", "license": "Apache 2.0", "private": true, "homepage": "https://github.com/x007xyz/fly-cut.git", "description": "About A web-based video editing tool implemented with WebCodecs, similar to CapCut Web.使用webcodecs实现的Web端视频编辑工具，类似剪映Web版。", "keywords": ["WebCodecs", "video-editor", "video-processing", "video-cut", "video-clip"], "scripts": {"dev": "vite", "dev-ssl": "vite", "preview": "vite preview", "test:unit": "vitest --environment jsdom --root src/", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "lint-fix": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "prepare": "echo 'skipping husky install'"}, "dependencies": {"@ckpack/vue-color": "^1.4.1", "@element-plus/icons-vue": "^2.0.10", "@leafer-in/editor": "1.0.0-rc.23", "@leafer-in/view": "1.0.0-rc.23", "@vueuse/core": "^10.9.0", "@webav/av-canvas": "0.9.0-beta.21", "@webav/av-cliper": "0.9.0-beta.21", "axios": "^1.3.2", "element-plus": "^2.2.29", "leafer-ui": "1.0.0-rc.23", "lodash-es": "^4.17.21", "opfs-tools": "^0.4.1", "pinia": "^2.0.28", "spark-md5": "^3.0.2", "vue": "^3.2.45", "vue-hooks-plus": "1.6.0-alpha.2", "vue-router": "^4.1.6", "vue3-moveable": "^0.18.1", "wavesurfer.js": "^7.7.11"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/typography": "^0.5.9", "@types/jsdom": "^20.0.1", "@types/lodash-es": "^4.17.6", "@types/node": "^18.11.18", "@types/spark-md5": "^3.0.4", "@types/webpack-env": "^1.18.0", "@vitejs/plugin-basic-ssl": "^1.0.1", "@vitejs/plugin-vue": "^4.0.0", "@vue-hooks-plus/resolvers": "^1.2.1", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.2.6", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.13", "cssnano": "^5.1.14", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "husky": "^8.0.3", "jsdom": "^20.0.3", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "postcss-import": "^15.1.0", "postcss-nesting": "^11.1.0", "sass": "^1.75.0", "tailwindcss": "^3.2.4", "typescript": "~4.7.4", "unplugin-auto-import": "^0.13.0", "unplugin-icons": "^0.15.2", "unplugin-vue-components": "^0.23.0", "vite": "^4.0.0", "vite-plugin-vue-devtools": "^7.0.25", "vitest": "^0.25.8", "vue-tsc": "^1.0.12"}}