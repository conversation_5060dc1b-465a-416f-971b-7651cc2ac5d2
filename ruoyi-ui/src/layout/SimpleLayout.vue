<template>
  <div class="app-wrapper" :class="{ 'fullscreen-editor': isEditorFull }">
    <!-- 侧边栏：全屏编辑器时隐藏 -->
    <div class="sidebar-wrapper" v-if="!isEditorFull" :class="{ 'collapsed': isCollapsed }">
      <!-- 原侧边栏内容保留 -->
      <div class="sidebar-header">
        <div class="header-content">
          <span class="company-icon">🏢</span>
          <span v-if="!isCollapsed" class="company-name">谷菱碰一碰</span>
        </div>
        <button class="collapse-btn" @click="toggleCollapse">
          <i :class="isCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
        </button>
      </div>
      <div class="sidebar-menu">
        <!-- 此处省略，保留原菜单 -->
        <ul>
          <li>
            <router-link to="/storer/index" class="menu-item">
              <span class="menu-icon">🏠</span>
              <span v-if="!isCollapsed" class="menu-text">工作台</span>
            </router-link>
          </li>
          <!-- 其余菜单照旧 -->
        </ul>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-wrapper">
      <!-- 顶部导航栏：全屏编辑器时隐藏 -->
      <div class="top-navbar" v-if="!isEditorFull">
        <div class="navbar-left">
          <h3>谷菱碰一碰客户端</h3>
        </div>
        <div class="navbar-right">
          <div class="user-info">
            <i class="el-icon-user" style="margin-right: 8px;"></i>
            <span class="user-name">👤 {{ getCurrentUserName() }}</span>
            <span class="user-role" v-if="getCurrentUserRole()">（{{ getCurrentUserRole() }}）</span>
          </div>
          <el-dropdown class="user-dropdown" trigger="click" @command="handleCommand">
            <span class="dropdown-link">
              <i class="el-icon-setting" style="margin-right: 5px;"></i>
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="profile">
                <i class="el-icon-user"></i> 个人中心
              </el-dropdown-item>
              <el-dropdown-item command="logout" divided>
                <i class="el-icon-switch-button"></i> 退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>

      <!-- 页面内容：全屏编辑器填满 -->
      <div class="content-wrapper" :class="{ 'no-padding': isEditorFull }">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getUserProfile } from '@/api/system/user'

export default {
  name: 'SimpleLayout',
  data() {
    return {
      isCollapsed: false,
      currentUser: {},
      openGroups: {
        copywriting: false,
        video: false,
        media: false,
        marketing: false
      }
    }
  },
  computed: {
    ...mapGetters([
      'userInfo',
      'userName',
      'nickName',
      'roles'
    ]),
    // 处于全屏编辑器模式：当进入 /storer/editor 路由时隐藏侧边栏和顶栏
    isEditorFull() {
      return this.$route && this.$route.path === '/storer/editor'
    }
  },

  created() {
    this.loadUserInfo()
  },
  mounted() {
    // 添加调试信息
    console.log('SimpleLayout mounted')
    console.log('当前用户信息:', this.currentUser)
    console.log('Store中的用户信息:', this.userInfo)
    console.log('Store中的userName:', this.userName)
    console.log('Store中的nickName:', this.nickName)

    // 监听store中用户信息变化
    this.$watch('userInfo', (newVal) => {
      console.log('Store用户信息变化:', newVal)
      if (newVal && Object.keys(newVal).length > 0) {
        this.currentUser = newVal
      }
    }, { immediate: true, deep: true })

    this.$watch('userName', (newVal) => {
      console.log('Store userName变化:', newVal)
      if (newVal && !this.currentUser.userName) {
        this.currentUser = {
          ...this.currentUser,
          userName: newVal,
          nickName: this.nickName || newVal
        }
      }
    }, { immediate: true })
  },
  methods: {
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
      // 收起侧边栏时关闭所有菜单组
      if (this.isCollapsed) {
        Object.keys(this.openGroups).forEach(key => {
          this.openGroups[key] = false
        })
      }
    },

    toggleGroup(groupName) {
      if (this.isCollapsed) return
      this.openGroups[groupName] = !this.openGroups[groupName]
    },

    getCurrentUserName() {
      // 优先级：currentUser.nickName > currentUser.userName > store中的nickName > store中的userName > 默认值
      if (this.currentUser && this.currentUser.nickName) {
        return this.currentUser.nickName
      }
      if (this.currentUser && this.currentUser.userName) {
        return this.currentUser.userName
      }
      if (this.nickName) {
        return this.nickName
      }
      if (this.userName) {
        return this.userName
      }
      return 'admin' // 默认显示admin
    },

    getCurrentUserRole() {
      if (this.currentUser && this.currentUser.roleGroup) {
        return this.currentUser.roleGroup
      }
      if (this.roles && this.roles.length > 0) {
        return this.roles[0]
      }
      return '管理员'
    },

    async loadUserInfo() {
      try {
        console.log('开始获取用户信息...')
        console.log('Store中的userInfo:', this.userInfo)
        console.log('Store中的userName:', this.userName)
        console.log('Store中的nickName:', this.nickName)

        // 首先尝试从 store 获取用户信息
        if (this.userInfo && Object.keys(this.userInfo).length > 0) {
          console.log('从Store获取到用户信息:', this.userInfo)
          this.currentUser = this.userInfo
          return
        }

        // 如果 store 中没有完整信息，但有基本信息，先使用基本信息
        if (this.userName || this.nickName) {
          console.log('使用Store中的基本用户信息')
          this.currentUser = {
            userName: this.userName,
            nickName: this.nickName || this.userName,
            roleGroup: this.roles && this.roles.length > 0 ? this.roles[0] : ''
          }
        }

        // 检查是否有token，如果没有则跳过API调用
        const token = this.$store.getters.token
        if (!token) {
          console.log('未找到token，跳过API调用，使用基本信息')
          this.currentUser = {
            userName: this.userName || 'admin',
            nickName: this.nickName || this.userName || 'admin',
            roleGroup: this.roles && this.roles.length > 0 ? this.roles[0] : '管理员'
          }
          return
        }

        // 然后调用 API 获取详细信息
        console.log('调用API获取详细用户信息...')
        const response = await getUserProfile()
        console.log('API响应:', response)

        if (response && response.data) {
          const userData = {
            ...response.data.user,
            roleGroup: response.data.roleGroup
          }
          console.log('设置用户信息:', userData)
          this.currentUser = userData

          // 更新 store 中的用户信息
          this.$store.commit('SET_USER_INFO', userData)
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)

        // 如果是401错误，说明token无效，清除token
        if (error.response && error.response.status === 401) {
          console.log('Token无效，清除登录状态')
          this.$store.dispatch('LogOut')
        }

        // 使用基本信息作为备用
        this.currentUser = {
          userName: this.userName || 'admin',
          nickName: this.nickName || this.userName || 'admin',
          roleGroup: this.roles && this.roles.length > 0 ? this.roles[0] : '管理员'
        }
        console.log('使用备用用户信息:', this.currentUser)
      }
    },

    handleCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push('/user/profile')
          break
        case 'logout':
          this.logout()
          break
      }
    },

    logout() {
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$router.push('/login')
        })
      }).catch(() => {
        // 用户取消退出
      })
    }
  }
}
</script>

<style scoped>
.app-wrapper {
  display: flex;
  height: 100vh;
}

.sidebar-wrapper {
  width: 200px;
  background-color: #304156;
  color: white;
  transition: width 0.3s ease;
  position: relative;
}

.sidebar-wrapper.collapsed {
  width: 64px;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid #434a5c;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  overflow: hidden;
}

.collapse-btn {
  background: none;
  border: none;
  color: #bfcbd9;
  font-size: 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: 3px;
  transition: all 0.3s;
  flex-shrink: 0;
}

.collapse-btn:hover {
  background-color: #409eff;
  color: white;
}

.sidebar-wrapper.collapsed .collapse-btn {
  position: absolute;
  top: 15px;
  right: 15px;
}

.header-content {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.company-icon {
  font-size: 20px;
  margin-right: 8px;
  flex-shrink: 0;
}

.sidebar-wrapper.collapsed .company-icon {
  margin-right: 0;
  display: none;
}

.company-name {
  color: white;
  font-size: 16px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-header h3 {
  color: white;
  margin: 0;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar-menu {
  padding: 10px 0;
}

.sidebar-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  margin: 0;
}

.menu-item {
  color: #bfcbd9;
  text-decoration: none;
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 0;
  transition: all 0.3s;
  position: relative;
}

.menu-item:hover,
.menu-item.router-link-active {
  background-color: #409eff;
  color: white;
}

.menu-icon {
  font-size: 18px;
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.menu-text {
  white-space: nowrap;
  overflow: hidden;
}

.collapsed .menu-text {
  display: none;
}

.collapsed .menu-icon {
  margin-right: 0;
}

/* 菜单组样式 */
.menu-group {
  margin: 0;
}

.group-header {
  color: #bfcbd9;
  text-decoration: none;
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 0;
  transition: all 0.3s;
  position: relative;
  cursor: pointer;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
}

.group-header:hover {
  background-color: #434a5c;
  color: white;
}

.group-header.active {
  background-color: #409eff;
  color: white;
}

.group-header .menu-icon {
  font-size: 18px;
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.group-header .menu-text {
  white-space: nowrap;
  overflow: hidden;
  flex: 1;
}

.expand-icon {
  margin-left: auto;
  font-size: 12px;
  transition: transform 0.3s;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

/* 子菜单样式 */
.submenu {
  background-color: #2b3644;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.submenu.collapsed-submenu {
  max-height: 0;
}

.submenu.expanded-submenu {
  max-height: 500px;
}

.submenu-item {
  color: #a6b3c7;
  text-decoration: none;
  display: flex;
  align-items: center;
  padding: 10px 15px 10px 45px;
  border-radius: 0;
  transition: all 0.3s;
  position: relative;
  font-size: 14px;
}

.submenu-item:hover {
  background-color: #434a5c;
  color: white;
}

.submenu-item.router-link-active {
  background-color: #409eff;
  color: white;
}

.submenu-item:before {
  content: '•';
  margin-right: 8px;
  font-size: 12px;
}

/* 折叠状态下的子菜单隐藏 */
.collapsed .submenu {
  display: none;
}

.collapsed .expand-icon {
  display: none;
}

.main-wrapper {
  flex: 1;
  background-color: #f0f2f5;
  overflow-y: auto;
  transition: margin-left 0.3s ease;
  display: flex;
  flex-direction: column;
}

.top-navbar {
  background: white;
  height: 60px;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  border-bottom: 1px solid #e8e8e8;
}

.navbar-left h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 14px;
  background: #f5f7fa;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #dcdfe6;
}

.user-name {
  font-weight: 600;
  color: #303133;
  margin-right: 4px;
}

.user-role {
  color: #909399;
  font-size: 12px;
}

.user-dropdown {
  cursor: pointer;
}

.dropdown-link {
  display: flex;
  align-items: center;
  color: #606266;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s;
}

.dropdown-link:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.content-wrapper {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-wrapper {
    position: fixed;
    z-index: 1000;
    height: 100vh;
  }

  .main-wrapper {
    margin-left: 0;
  }
}
</style>
