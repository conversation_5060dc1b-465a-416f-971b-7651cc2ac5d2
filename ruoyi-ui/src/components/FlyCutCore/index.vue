<template>
  <div class="fly-cut-core">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-section">
        <el-button-group size="small">
          <el-button @click="undo" :disabled="!canUndo" icon="el-icon-refresh-left">撤销</el-button>
          <el-button @click="redo" :disabled="!canRedo" icon="el-icon-refresh-right">重做</el-button>
        </el-button-group>
      </div>

      <div class="toolbar-section">
        <el-button-group size="small">
          <el-button @click="addVideoTrack" icon="el-icon-video-camera">视频轨道</el-button>
          <el-button @click="addAudioTrack" icon="el-icon-headset">音频轨道</el-button>
          <el-button @click="addTextTrack" icon="el-icon-edit">文字轨道</el-button>
        </el-button-group>
      </div>

      <div class="toolbar-section">
        <el-button @click="exportTemplateData" type="primary" size="small">导出模板</el-button>
      </div>
    </div>

    <!-- 主要工作区 -->
    <div class="workspace">
      <!-- 左侧资源面板（按需显示，默认隐藏） -->
      <div class="resource-panel" v-if="showResourcePanel">
        <el-tabs v-model="activeResourceTab" tab-position="top" size="small">
          <el-tab-pane label="媒体库" name="media">
            <div class="media-grid">
              <div
                class="media-item"
                v-for="item in mediaLibrary"
                :key="item.id"
                @dragstart="onDragStart($event, item)"
                draggable="true"
              >
                <div class="media-thumbnail">
                  <img v-if="item.type === 'image'" :src="item.thumbnail" :alt="item.name" />
                  <video v-if="item.type === 'video'" :src="item.url" muted preload="metadata"></video>
                  <i v-if="item.type === 'audio'" class="el-icon-headset"></i>
                </div>
                <div class="media-info">
                  <span class="media-name">{{ item.name }}</span>
                  <span class="media-duration">{{ formatDuration(item.duration) }}</span>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="文字样式" name="text">
            <div class="text-styles">
              <div
                class="text-style-item"
                v-for="style in textStyles"
                :key="style.id"
                @click="addTextWithStyle(style)"
              >
                <div class="style-preview" :style="style.css">{{ style.name }}</div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="转场效果" name="transitions">
            <div class="transitions-grid">
              <div
                class="transition-item"
                v-for="transition in transitions"
                :key="transition.id"
                @click="addTransition(transition)"
              >
                <div class="transition-preview">{{ transition.name }}</div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 中央预览区 -->
      <div class="preview-area">
        <div class="canvas-wrapper">
          <canvas
            ref="previewCanvas"
            :width="canvasWidth"
            :height="canvasHeight"
            @click="onCanvasClick"
          ></canvas>

          <!-- 播放控制 -->
          <div class="playback-controls">
            <el-button-group size="small">
              <el-button @click="seekToStart" icon="el-icon-d-arrow-left"></el-button>
              <el-button @click="togglePlayback" :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'">
                {{ isPlaying ? '暂停' : '播放' }}
              </el-button>
              <el-button @click="seekToEnd" icon="el-icon-d-arrow-right"></el-button>
            </el-button-group>

            <div class="time-display">
              <span>{{ formatTime(currentTime) }} / {{ formatTime(totalDuration) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="properties-panel">
        <div class="panel-header">
          <h4>属性</h4>
        </div>

        <div class="panel-content" v-if="selectedElement">
          <el-form :model="selectedElement" label-width="70px" size="small">
            <el-form-item label="名称">
              <el-input v-model="selectedElement.name" size="small"></el-input>
            </el-form-item>

            <el-form-item label="开始时间">
              <el-input-number
                v-model="selectedElement.startTime"
                :min="0"
                :step="0.1"
                size="small"
                style="width: 100%"
              ></el-input-number>
            </el-form-item>

            <el-form-item label="持续时间">
              <el-input-number
                v-model="selectedElement.duration"
                :min="0.1"
                :step="0.1"
                size="small"
                style="width: 100%"
              ></el-input-number>
            </el-form-item>

            <el-form-item label="透明度" v-if="selectedElement.type !== 'audio'">
              <el-slider v-model="selectedElement.opacity" :min="0" :max="100"></el-slider>
            </el-form-item>

            <el-form-item label="音量" v-if="selectedElement.type === 'audio' || selectedElement.type === 'video'">
              <div style="display: flex; align-items: center; gap: 10px;">
                <el-button
                  :type="selectedElement.muted ? 'danger' : 'default'"
                  :icon="selectedElement.muted ? 'el-icon-mute' : 'el-icon-microphone'"
                  size="mini"
                  @click="toggleMute"
                >
                  {{ selectedElement.muted ? '取消静音' : '静音' }}
                </el-button>
                <el-button
                  size="mini"
                  @click="testAudio"
                  style="margin-left: 5px;"
                >
                  测试音频
                </el-button>
                <el-slider
                  v-model="selectedElement.volume"
                  :min="0"
                  :max="100"
                  :disabled="selectedElement.muted"
                  style="flex: 1;"
                  @change="onVolumeChange"
                ></el-slider>
              </div>
            </el-form-item>

            <!-- 文字特有属性 -->
            <template v-if="selectedElement.type === 'text'">
              <el-form-item label="文字内容">
                <el-input v-model="selectedElement.content" type="textarea" :rows="3" size="small"></el-input>
              </el-form-item>

              <el-form-item label="字体大小">
                <el-input-number v-model="selectedElement.fontSize" :min="12" :max="72" size="small" style="width: 100%"></el-input-number>
              </el-form-item>

              <el-form-item label="颜色">
                <el-color-picker v-model="selectedElement.color"></el-color-picker>
              </el-form-item>

              <el-divider></el-divider>
              <div style="display:flex; gap:8px;">
                <el-button size="mini" @click="openCopywritingAndMap">打开文案库</el-button>
                <el-popover placement="top" trigger="click">
                  <div style="width:300px;">
                    <el-input
                      v-model="tempCopyLongText"
                      type="textarea"
                      :rows="6"
                      placeholder="在这里粘贴从文案库选择的长文案"
                    ></el-input>
                    <div style="text-align:right;margin-top:8px;">
                      <el-button size="mini" type="primary" @click="mapCopyToVideoSegments(tempCopyLongText)">按标点拆分并映射</el-button>
                    </div>
                  </div>
                  <el-button slot="reference" size="mini">粘贴文案并拆分</el-button>
                </el-popover>
              </div>
            </template>
          </el-form>
        </div>

        <div v-else class="no-selection">
          <p>选择一个元素来编辑属性</p>
        </div>
      </div>
    </div>

    <!-- 底部时间轴 -->
    <div class="timeline-area">
      <div class="timeline-header">
        <div class="timeline-controls">
          <el-button-group size="mini">
            <el-button @click="zoomTimelineIn" icon="el-icon-zoom-in"></el-button>
            <el-button @click="zoomTimelineOut" icon="el-icon-zoom-out"></el-button>
          </el-button-group>

          <div class="timeline-info">
            <span>缩放: {{ Math.round(timelineZoom * 100) }}%</span>
          </div>
        </div>
      </div>

      <div class="timeline-content">
        <div class="tracks-container">
          <div
            class="track"
            v-for="track in tracks"
            :key="track.id"
            :class="{ active: track.id === activeTrackId }"
          >
            <div class="track-header">
              <span class="track-name">{{ track.name }}</span>
              <div class="track-controls">
                <el-button v-if="track.type === 'video'" @click="addFiveSecondClip(track)" icon="el-icon-plus" size="mini" type="text" title="添加5s片段"></el-button>
                <el-button @click="toggleTrackVisibility(track)" :icon="track.visible ? 'el-icon-view' : 'el-icon-hide'" size="mini" type="text"></el-button>
                <el-button @click="deleteTrack(track)" icon="el-icon-delete" size="mini" type="text"></el-button>
              </div>
            </div>

            <div
              class="track-timeline"
              @drop="onTimelineDrop($event, track)"
              @dragover.prevent
              @click="setActiveTrack(track.id)"
            >
              <div
                class="timeline-element"
                v-for="element in track.elements"
                :key="element.id"
                :style="getElementTimelineStyle(element)"
                :class="{ selected: selectedElement && selectedElement.id === element.id }"
                @dblclick.stop="onElementDblClick(element, track)"
                @click.stop="selectElement(element)"
                @mousedown="startElementDrag($event, element)"
              >
                <span class="element-label">{{ element.name }}</span>

                <!-- 调整手柄 -->
                <div class="resize-handle left" @mousedown.stop="startResize($event, element, 'left')"></div>
                <div class="resize-handle right" @mousedown.stop="startResize($event, element, 'right')"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 时间标尺 -->
        <div class="timeline-ruler">
          <div class="ruler-ticks">
            <div
              v-for="tick in rulerTicks"
              :key="tick.time"
              class="ruler-tick"
              :style="{ left: tick.position + 'px' }"
            >
              <span class="tick-label">{{ formatTime(tick.time) }}</span>
            </div>
          </div>

          <!-- 播放头 -->
          <div
            class="playhead"
            :style="{ left: (currentTime * timelineZoom * 100) + 'px' }"
            @mousedown="startPlayheadDrag"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FlyCutCore',
  props: {
    templateData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      // 画布设置
      canvasWidth: 1920,
      canvasHeight: 1080,

      // 播放状态
      isPlaying: false,
      currentTime: 0,
      totalDuration: 15, // 默认三段视频各5秒

      // 时间轴设置
      timelineZoom: 1,
      activeTrackId: null,

      // 选中元素
      selectedElement: null,
      tempCopyLongText: '', // 文案库粘贴区临时内容

      // 历史记录
      history: [],
      historyIndex: -1,

      // UI状态
      activeResourceTab: 'media',
      // 控制是否显示左侧资源面板（默认隐藏）
      showResourcePanel: false,

      // 轨道数据
      tracks: [
        { id: 1, name: '视频轨道 1', type: 'video', visible: true, elements: [] },
        { id: 2, name: '音频轨道 1', type: 'audio', visible: true, elements: [] },
        { id: 3, name: '文字轨道 1', type: 'text', visible: true, elements: [] }
      ],

      // 媒体库（不再用于拖拽，仅保留占位）
      mediaLibrary: [
        { id: 1, name: '示例视频.mp4', type: 'video', url: '/demo/video1.mp4', thumbnail: '/demo/video1-thumb.jpg', duration: 30 },
        { id: 2, name: '示例图片.jpg', type: 'image', url: '/demo/image1.jpg', thumbnail: '/demo/image1.jpg', duration: 5 },
        { id: 3, name: '背景音乐.mp3', type: 'audio', url: '/demo/audio1.mp3', thumbnail: '', duration: 120 },
        { id: 4, name: '测试音频.mp3', type: 'audio', url: 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT', thumbnail: '', duration: 10 }
      ],

      // 文字样式（仍可添加默认样式文本）
      textStyles: [
        { id: 1, name: '标题', css: { fontSize: '32px', fontWeight: 'bold', color: '#333333' } },
        { id: 2, name: '副标题', css: { fontSize: '24px', color: '#666666' } },
        { id: 3, name: '正文', css: { fontSize: '16px', color: '#333333' } }
      ],

      // 转场效果（保留占位）
      transitions: [
        { id: 1, name: '淡入淡出', type: 'fade' },
        { id: 2, name: '滑动', type: 'slide' },
        { id: 3, name: '缩放', type: 'zoom' }
      ]
    }
  },

  computed: {
    canUndo() {
      return this.historyIndex > 0
    },

    canRedo() {
      return this.historyIndex < this.history.length - 1
    },

    rulerTicks() {
      const ticks = []
      const interval = 5 // 5秒间隔
      for (let i = 0; i <= this.totalDuration; i += interval) {
        ticks.push({
          time: i,
          position: i * this.timelineZoom * 100
        })
      }
      return ticks
    }
  },

  mounted() {
    this.initCanvas()
    this.loadTemplateData()
  },

  methods: {
    // 初始化画布
    initCanvas() {
      const canvas = this.$refs.previewCanvas
      const ctx = canvas.getContext('2d')

      // 设置画布背景
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
    },

    // 加载模板数据
    loadTemplateData() {
      if (this.templateData && this.templateData.tracks && this.templateData.tracks.length) {
        this.tracks = this.templateData.tracks
      } else {
        // 初始化：显示三个5s视频片段的“视频轨道 1”，一个5s文本片段的“文字轨道 1”，一个5s音频片段的“音频轨道 1”
        const initVideoTrack = { id: 1, name: '视频轨道 1', type: 'video', visible: true, elements: [] }
        const initAudioTrack = { id: 2, name: '音频轨道 1', type: 'audio', visible: true, elements: [] }
        const initTextTrack  = { id: 3, name: '文字轨道 1', type: 'text',  visible: true, elements: [] }
        // 三个视频片段，各5s，依次相连
        for (let i = 0; i < 3; i++) {
          initVideoTrack.elements.push({
            id: Date.now() + i,
            name: `视频片段 ${i+1}`,
            type: 'video',
            url: '',
            startTime: i * 5,
            duration: 5,
            opacity: 100,
            muted: false,
            volume: 50,
            previousVolume: 50
          })
        }
        // 一个文本片段 5s（默认对齐第一个视频片段）
        initTextTrack.elements.push({
          id: Date.now() + 100,
          name: '文字片段 1',
          type: 'text',
          content: '默认文案',
          startTime: 0,
          duration: 5,
          opacity: 100,
          fontSize: 24,
          color: '#ffffff'
        })
        // 一个音频片段，初始 5s
        initAudioTrack.elements.push({
          id: Date.now() + 200,
          name: '音频片段 1',
          type: 'audio',
          url: '',
          startTime: 0,
          duration: 5,
          opacity: 100,
          muted: false,
          volume: 50,
          previousVolume: 50
        })
        this.tracks = [initVideoTrack, initAudioTrack, initTextTrack]
        // 设置总时长等于最后一个视频片段末尾
        this.totalDuration = 3 * 5
      // 初始化后统一更新总时长并同步音频
      this.updateTotalDuration()
      this.syncAudioToVideo()
      }
    },

    // 格式化时间显示
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },

    // 格式化持续时间
    formatDuration(seconds) {
      return this.formatTime(seconds)
    },

    // 播放控制
    togglePlayback() {
      this.isPlaying = !this.isPlaying
      if (this.isPlaying) {
        this.startPlayback()
      } else {
        this.pausePlayback()
      }
    },

    startPlayback() {
      // 应用所有音频设置
      this.applyAllAudioSettings()

      this.playbackTimer = setInterval(() => {
        this.currentTime += 0.1
        if (this.currentTime >= this.totalDuration) {
          this.pausePlayback()
          this.currentTime = this.totalDuration
        }
        this.renderFrame()
      }, 100)
    },

    pausePlayback() {
      this.isPlaying = false
      if (this.playbackTimer) {
        clearInterval(this.playbackTimer)
      }
    },

    seekToStart() {
      this.currentTime = 0
      this.renderFrame()
    },

    seekToEnd() {
      this.currentTime = this.totalDuration
      this.renderFrame()
    },

    // 渲染当前帧
    renderFrame() {
      const canvas = this.$refs.previewCanvas
      const ctx = canvas.getContext('2d')

      // 清空画布
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

      // 渲染所有可见轨道的元素
      this.tracks.forEach(track => {
        if (track.visible) {
          track.elements.forEach(element => {
            if (this.currentTime >= element.startTime &&
                this.currentTime <= element.startTime + element.duration) {
              this.renderElement(ctx, element)
            }
          })
        }
      })
    },

    // 渲染单个元素
    renderElement(ctx, element) {
      ctx.save()

      // 设置透明度
      if (element.opacity !== undefined) {
        ctx.globalAlpha = element.opacity / 100
      }

      switch (element.type) {
        case 'text':
          this.renderTextElement(ctx, element)
          break
        case 'image':
          this.renderImageElement(ctx, element)
          break
        case 'video':
          this.renderVideoElement(ctx, element)
          break
      }

      ctx.restore()
    },

    // 渲染文字元素
    renderTextElement(ctx, element) {
      ctx.fillStyle = element.color || '#ffffff'
      ctx.font = `${element.fontSize || 24}px Arial`
      ctx.textAlign = 'center'
      ctx.fillText(
        element.content || element.name,
        this.canvasWidth / 2,
        this.canvasHeight / 2
      )
    },

    // 渲染图片元素
    renderImageElement(ctx, element) {
      // 这里需要实际的图片加载逻辑
      ctx.fillStyle = '#666666'
      ctx.fillRect(100, 100, 200, 150)
      ctx.fillStyle = '#ffffff'
      ctx.font = '16px Arial'
      ctx.fillText(element.name, 200, 175)
    },

    // 渲染视频元素
    renderVideoElement(ctx, element) {
      // 这里需要实际的视频渲染逻辑
      ctx.fillStyle = '#333333'
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight)
      ctx.fillStyle = '#ffffff'
      ctx.font = '24px Arial'
      ctx.textAlign = 'center'
      ctx.fillText(element.name, this.canvasWidth / 2, this.canvasHeight / 2)
    },

    // 元素选择
    selectElement(element) {
      this.selectedElement = element

      // 确保音频/视频元素有必要的属性
      if (element && (element.type === 'audio' || element.type === 'video')) {
        if (typeof element.muted === 'undefined') {
          this.$set(element, 'muted', false)
        }
        if (typeof element.volume === 'undefined') {
          this.$set(element, 'volume', 50)
        }
        if (typeof element.previousVolume === 'undefined') {
          this.$set(element, 'previousVolume', 50)
        }
      }
    },

    // 获取元素在时间轴上的样式
    getElementTimelineStyle(element) {
      const left = element.startTime * this.timelineZoom * 100
      const width = element.duration * this.timelineZoom * 100
      return {
        left: left + 'px',
        width: Math.max(width, 20) + 'px'
      }
    },

    // 时间轴缩放
    zoomTimelineIn() {
      this.timelineZoom = Math.min(this.timelineZoom * 1.2, 5)
    },

    zoomTimelineOut() {
      this.timelineZoom = Math.max(this.timelineZoom / 1.2, 0.1)
    },

    // 轨道操作
    addVideoTrack() {
      const newTrack = {
        id: Date.now(),
        name: `视频轨道 ${this.tracks.filter(t => t.type === 'video').length + 1}`,
        type: 'video',
        visible: true,
        elements: []
      }
      this.tracks.push(newTrack)
    },

    // 视频轨道按钮：添加固定5s片段
    addFiveSecondClip(track) {
      if (!track || track.type !== 'video') return
      const lastEnd = track.elements.length ? Math.max(...track.elements.map(e => e.startTime + e.duration)) : 0
      const newEl = {
        id: Date.now(),
        name: `视频片段 ${track.elements.length + 1}`,
        type: 'video',
        url: '',
        startTime: lastEnd,
        duration: 5,
        opacity: 100,
        muted: false,
        volume: 50,
        previousVolume: 50
      }
      track.elements.push(newEl)
      // 自动扩展音频片段长度
      this.syncAudioToVideo()
      // 更新总时长
      this.updateTotalDuration()
      this.saveToHistory()
    },


    addAudioTrack() {
      const newTrack = {
        id: Date.now(),
        name: `音频轨道 ${this.tracks.filter(t => t.type === 'audio').length + 1}`,
        type: 'audio',
        visible: true,
        elements: []
      }
      this.tracks.push(newTrack)
    },

    // 同步音频片段时长以对齐视频总时长（受音频最大长度约束）
    syncAudioToVideo() {
      const videoTrack = this.tracks.find(t => t.type === 'video')
      const audioTrack = this.tracks.find(t => t.type === 'audio')
      if (!videoTrack || !audioTrack || audioTrack.elements.length === 0) return
      const totalVideoEnd = videoTrack.elements.reduce((max, e) => Math.max(max, e.startTime + e.duration), 0)
      const audioEl = audioTrack.elements[0]
      const maxAudioLength = audioEl.maxDuration || 120 // 可配置的音频最大长度（占位）
      audioEl.duration = Math.min(totalVideoEnd, maxAudioLength)
    },

    // 计算总时长：以所有视频片段的末尾作为总时长
    updateTotalDuration() {
      const videoTrack = this.tracks.find(t => t.type === 'video')
      if (!videoTrack) return
      this.totalDuration = videoTrack.elements.reduce((max, e) => Math.max(max, e.startTime + e.duration), 0) || 0
    },

    // 双击片段：视频片段 -> 打开视频库（带回跳参数）；文本片段 -> 暂无特殊
    onElementDblClick(element, track) {
      if (element.type === 'video') {
        const returnUrl = encodeURIComponent(window.location.origin + this.$route.fullPath)
        const url = `http://localhost:8080/storer/up?returnUrl=${returnUrl}&videoElementId=${element.id}`
        window.open(url, '_blank')
      } else if (element.type === 'audio') {
        // 预留：可打开音频库
      }
    },

    addTextTrack() {
      const newTrack = {
        id: Date.now(),
        name: `文字轨道 ${this.tracks.filter(t => t.type === 'text').length + 1}`,
        type: 'text',
        visible: true,
        elements: []
      }
      this.tracks.push(newTrack)
    },

    // 打开文案库并按标点拆分，映射到视频片段
    openCopywritingAndMap() {
      // 打开文案库页面（新标签）
      window.open('http://localhost:8080/storer/shipin', '_blank')
      this.$message.info('请在新开页面选择文案后返回本页手动粘贴；自动对接API后将一键提取并拆分映射')
    },

    // 根据选中的长文案，按标点拆分并映射到视频片段
    mapCopyToVideoSegments(longText) {
      if (!longText) return
      const videoTrack = this.tracks.find(t => t.type === 'video')
      const textTrack = this.tracks.find(t => t.type === 'text')
      if (!videoTrack || !textTrack) return
      const parts = longText
        .split(/[。！？!?；;、,，]/)
        .map(s => s.trim())
        .filter(Boolean)
      if (parts.length === 0) return
      while (videoTrack.elements.length < parts.length) {
        this.addFiveSecondClip(videoTrack)
      }
      textTrack.elements = []
      parts.forEach((sentence, idx) => {
        const v = videoTrack.elements[idx]
        if (!v) return
        textTrack.elements.push({
          id: Date.now() + idx,
          name: `文字片段 ${idx + 1}`,
          type: 'text',
          content: sentence,
          startTime: v.startTime,
          duration: v.duration,
          opacity: 100,
          fontSize: 24,
          color: '#ffffff'
        })
      })
      this.updateTotalDuration()
      this.saveToHistory()
      this.$message.success('已根据标点拆分并映射到视频片段')
    },

    // 根据 elementId 回填视频选择的 URL
    applyVideoSelection(elementId, url) {
      if (!elementId || !url) return
      for (const t of this.tracks) {
        const idx = t.elements.findIndex(e => e.id == elementId)
        if (idx !== -1) {
          const el = t.elements[idx]
          if (el.type === 'video') {
            this.$set(el, 'url', url)
            try {
              const nameFromUrl = decodeURIComponent(url.split('/').pop() || '')
              if (nameFromUrl) this.$set(el, 'name', nameFromUrl)
            } catch (e) {}
            this.$message.success('已回填视频片段')
            this.saveToHistory()
          }
          break
        }
      }
    },

    toggleTrackVisibility(track) {
      track.visible = !track.visible
      this.renderFrame()
    },

    deleteTrack(track) {
      const index = this.tracks.findIndex(t => t.id === track.id)
      if (index > -1) {
        this.tracks.splice(index, 1)
      }
    },

    setActiveTrack(trackId) {
      this.activeTrackId = trackId
    },

    // 拖拽处理
    onDragStart(event, item) {
      event.dataTransfer.setData('application/json', JSON.stringify(item))
    },

    onTimelineDrop(event, track) {
      event.preventDefault()
      const itemData = JSON.parse(event.dataTransfer.getData('application/json'))

      // 计算放置时间
      const rect = event.currentTarget.getBoundingClientRect()
      const x = event.clientX - rect.left
      const dropTime = x / (this.timelineZoom * 100)

      // 创建新元素
      const newElement = {
        id: Date.now(),
        name: itemData.name,
        type: itemData.type,
        url: itemData.url,
        startTime: Math.max(0, dropTime),
        duration: itemData.duration || 5,
        opacity: 100
      }

      // 为音频和视频元素添加音频相关属性
      if (itemData.type === 'audio' || itemData.type === 'video') {
        newElement.volume = 50
        newElement.muted = false
        newElement.previousVolume = 50
      }

      // 如果是文字样式，添加文字特有属性
      if (itemData.css) {
        Object.assign(newElement, itemData.css)
        newElement.content = itemData.name
      }

      track.elements.push(newElement)
      this.saveToHistory()
    },

    // 添加文字
    addTextWithStyle(style) {
      const textTrack = this.tracks.find(t => t.type === 'text')
      if (textTrack) {
        const newElement = {
          id: Date.now(),
          name: style.name,
          type: 'text',
          content: style.name,
          startTime: this.currentTime,
          duration: 5,
          opacity: 100,
          ...style.css
        }
        textTrack.elements.push(newElement)
        this.saveToHistory()
      }
    },

    // 添加转场效果
    addTransition(transition) {
      // 检查是否有选中的元素
      if (!this.selectedElement) {
        this.$message.warning('请先选择一个视频或图片元素')
        return
      }

      // 找到对应的轨道
      const track = this.tracks.find(t => t.elements.includes(this.selectedElement))
      if (!track) {
        this.$message.error('未找到对应的轨道')
        return
      }

      // 创建转场效果元素
      const transitionElement = {
        id: Date.now(),
        name: `转场-${transition.name}`,
        type: 'transition',
        transitionType: transition.type,
        startTime: this.selectedElement.startTime + this.selectedElement.duration - 1, // 在元素结束前1秒开始
        duration: 2, // 转场持续2秒
        opacity: 100,
        easing: 'ease-in-out'
      }

      // 添加到轨道
      track.elements.push(transitionElement)
      this.saveToHistory()
      this.$message.success(`已添加转场效果: ${transition.name}`)
    },

    // 切换静音状态
    toggleMute() {
      console.log('toggleMute 被调用')
      console.log('selectedElement:', this.selectedElement)

      if (this.selectedElement && (this.selectedElement.type === 'audio' || this.selectedElement.type === 'video')) {
        console.log('切换静音前状态:', {
          muted: this.selectedElement.muted,
          volume: this.selectedElement.volume
        })

        // 使用 Vue.set 确保响应式更新
        this.$set(this.selectedElement, 'muted', !this.selectedElement.muted)

        // 如果静音，记录当前音量并设为0；如果取消静音，恢复之前的音量
        if (this.selectedElement.muted) {
          this.$set(this.selectedElement, 'previousVolume', this.selectedElement.volume)
          this.$set(this.selectedElement, 'volume', 0)
        } else {
          this.$set(this.selectedElement, 'volume', this.selectedElement.previousVolume || 50)
        }

        console.log('切换静音后状态:', {
          muted: this.selectedElement.muted,
          volume: this.selectedElement.volume,
          previousVolume: this.selectedElement.previousVolume
        })

        // 应用到实际的音频元素
        this.applyAudioSettings(this.selectedElement)

        this.saveToHistory()
        this.$message.success(this.selectedElement.muted ? '已静音' : '已取消静音')
      } else {
        console.log('无法切换静音: 没有选中的音频/视频元素')
        this.$message.warning('请先选择一个音频或视频元素')
      }
    },

    // 测试音频功能
    testAudio() {
      if (this.selectedElement && (this.selectedElement.type === 'audio' || this.selectedElement.type === 'video')) {
        // 创建一个测试音频元素
        const testAudio = new Audio()

        // 生成一个简单的测试音频（440Hz 正弦波）
        const audioContext = new (window.AudioContext || window.webkitAudioContext)()
        const oscillator = audioContext.createOscillator()
        const gainNode = audioContext.createGain()

        oscillator.connect(gainNode)
        gainNode.connect(audioContext.destination)

        oscillator.frequency.value = 440 // A4 音符
        oscillator.type = 'sine'

        // 应用当前元素的音频设置
        gainNode.gain.value = this.selectedElement.muted ? 0 : (this.selectedElement.volume || 50) / 100

        console.log('测试音频设置:', {
          muted: this.selectedElement.muted,
          volume: this.selectedElement.volume,
          gainValue: gainNode.gain.value
        })

        // 播放1秒钟的测试音
        oscillator.start()
        setTimeout(() => {
          oscillator.stop()
          audioContext.close()
        }, 1000)

        this.$message.info(`测试音频播放 - 静音: ${this.selectedElement.muted ? '是' : '否'}, 音量: ${this.selectedElement.volume}%`)
      } else {
        this.$message.warning('请先选择一个音频或视频元素')
      }
    },

    // 音量变化处理
    onVolumeChange(value) {
      if (this.selectedElement && (this.selectedElement.type === 'audio' || this.selectedElement.type === 'video')) {
        // 如果音量大于0，自动取消静音
        if (value > 0 && this.selectedElement.muted) {
          this.$set(this.selectedElement, 'muted', false)
        }
        // 如果音量为0，自动静音
        else if (value === 0 && !this.selectedElement.muted) {
          this.$set(this.selectedElement, 'muted', true)
        }

        // 应用到实际的音频元素
        this.applyAudioSettings(this.selectedElement)
        this.saveToHistory()
      }
    },

    // 应用所有音频设置
    applyAllAudioSettings() {
      this.tracks.forEach(track => {
        track.elements.forEach(element => {
          if (element.type === 'audio' || element.type === 'video') {
            this.applyAudioSettings(element)
          }
        })
      })
    },

    // 应用音频设置到实际的音频元素
    applyAudioSettings(element) {
      if (!element || (element.type !== 'audio' && element.type !== 'video')) return

      console.log('应用音频设置:', {
        name: element.name,
        muted: element.muted,
        volume: element.volume,
        url: element.url
      })

      // 查找页面中对应的音频/视频元素
      const mediaElements = document.querySelectorAll('audio, video')
      mediaElements.forEach(mediaEl => {
        if (mediaEl.src && (mediaEl.src.includes(element.url) || mediaEl.src === element.url)) {
          mediaEl.muted = element.muted || false
          mediaEl.volume = element.muted ? 0 : (element.volume || 50) / 100
          console.log('已应用到媒体元素:', mediaEl.src, {
            muted: mediaEl.muted,
            volume: mediaEl.volume
          })
        }
      })

      // 如果有预览播放器，也应用设置
      const previewPlayer = this.$refs.previewPlayer
      if (previewPlayer) {
        const playerMedia = previewPlayer.querySelector('audio, video')
        if (playerMedia) {
          playerMedia.muted = element.muted || false
          playerMedia.volume = element.muted ? 0 : (element.volume || 50) / 100
          console.log('已应用到预览播放器')
        }
      }
    },

    // 历史记录
    saveToHistory() {
      const state = JSON.parse(JSON.stringify(this.tracks))
      this.history = this.history.slice(0, this.historyIndex + 1)
      this.history.push(state)
      this.historyIndex++
    },

    undo() {
      if (this.canUndo) {
        this.historyIndex--
        this.tracks = JSON.parse(JSON.stringify(this.history[this.historyIndex]))
      }
    },

    redo() {
      if (this.canRedo) {
        this.historyIndex++
        this.tracks = JSON.parse(JSON.stringify(this.history[this.historyIndex]))
      }
    },

    // 导出模板数据（保留占位，不做本地生成）
    exportTemplateData() {
      const exportData = {
        tracks: this.tracks,
        settings: {
          width: this.canvasWidth,
          height: this.canvasHeight,
          duration: this.totalDuration,
          fps: 30
        }
      }
      this.$emit('export', exportData)
      this.$message.success('模板数据已准备，可用于后续混剪/上传流程')
    },

    // 事件处理占位符
    onCanvasClick(event) {
      // 画布点击处理
    },

    startElementDrag(event, element) {
      // 元素拖拽开始
    },

    startResize(event, element, direction) {
      // 元素大小调整开始
    },

    startPlayheadDrag(event) {
      // 播放头拖拽开始
    },

    // 获取编辑器数据（供父组件调用）
    getEditorData() {
      return {
        tracks: this.tracks,
        totalDuration: this.totalDuration,
        canvasWidth: this.canvasWidth,
        canvasHeight: this.canvasHeight
      }
    },

    // 开始预览（供父组件调用）
    startPreview() {
      if (!this.isPlaying) {
        this.togglePlayback()
      }
    },

    // 导出视频（供父组件调用）
    exportVideo() {
      const exportData = {
        tracks: this.tracks,
        settings: {
          width: this.canvasWidth,
          height: this.canvasHeight,
          duration: this.totalDuration,
          fps: 30
        }
      }

      // 触发导出事件
      this.$emit('export', exportData)

      // 这里可以添加实际的视频导出逻辑
      this.$message.info('正在导出视频，请稍候...')
    }
  },

  beforeDestroy() {
    if (this.playbackTimer) {
      clearInterval(this.playbackTimer)
    }
  }
}
</script>

<style scoped>
.fly-cut-core {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1e1e1e;
  color: #ffffff;
}

/* 工具栏 */
.toolbar {
  height: 50px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  padding: 0 15px;
  gap: 20px;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 主工作区 */
.workspace {
  flex: 1;
  display: flex;
  min-height: 0;
}

/* 左侧资源面板 */
.resource-panel {
  width: 280px;
  background: #252525;
  border-right: 1px solid #404040;
  overflow: hidden;
}

.media-grid {
  padding: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.media-item {
  margin-bottom: 10px;
  border: 1px solid #404040;
  border-radius: 6px;
  overflow: hidden;
  cursor: grab;
  transition: all 0.3s;
}

.media-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.media-item:active {
  cursor: grabbing;
}

.media-thumbnail {
  height: 80px;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.media-thumbnail img,
.media-thumbnail video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-thumbnail i {
  font-size: 32px;
  color: #409eff;
}

.media-info {
  padding: 8px;
  background: #2a2a2a;
}

.media-name {
  display: block;
  font-size: 12px;
  color: #ffffff;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.media-duration {
  font-size: 11px;
  color: #999999;
}

/* 文字样式 */
.text-styles {
  padding: 10px;
}

.text-style-item {
  padding: 12px;
  margin-bottom: 8px;
  background: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.text-style-item:hover {
  border-color: #409eff;
  background: #2f2f2f;
}

.style-preview {
  text-align: center;
}

/* 转场效果 */
.transitions-grid {
  padding: 10px;
}

.transition-item {
  padding: 12px;
  margin-bottom: 8px;
  background: #2a2a2a;
  border: 1px solid #404040;
  border-radius: 6px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s;
}

.transition-item:hover {
  border-color: #409eff;
  background: #2f2f2f;
}

/* 中央预览区 */
.preview-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #1a1a1a;
  padding: 20px;
}

.canvas-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.canvas-wrapper canvas {
  max-width: 100%;
  max-height: 60vh;
  border: 2px solid #404040;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.playback-controls {
  margin-top: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.time-display {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #cccccc;
  background: #2d2d2d;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 右侧属性面板 */
.properties-panel {
  width: 280px;
  background: #252525;
  border-left: 1px solid #404040;
  overflow-y: auto;
}

.panel-header {
  padding: 15px;
  border-bottom: 1px solid #404040;
  background: #2d2d2d;
}

.panel-header h4 {
  margin: 0;
  color: #ffffff;
  font-size: 16px;
}

.panel-content {
  padding: 15px;
}

.no-selection {
  padding: 30px 15px;
  text-align: center;
  color: #999999;
}

/* 底部时间轴 */
.timeline-area {
  height: 220px;
  background: #252525;
  border-top: 1px solid #404040;
  display: flex;
  flex-direction: column;
}

.timeline-header {
  height: 40px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.timeline-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.timeline-info {
  font-size: 12px;
  color: #cccccc;
}

.timeline-content {
  flex: 1;
  position: relative;
  overflow: auto;
}

.tracks-container {
  min-height: 100%;
}

.track {
  height: 45px;
  border-bottom: 1px solid #404040;
  display: flex;
  transition: background-color 0.3s;
}

.track.active {
  background: rgba(64, 158, 255, 0.1);
}

.track-header {
  width: 150px;
  background: #2d2d2d;
  border-right: 1px solid #404040;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  flex-shrink: 0;
}

.track-name {
  font-size: 12px;
  color: #ffffff;
  font-weight: 500;
}

.track-controls {
  display: flex;
  gap: 4px;
}

.track-timeline {
  flex: 1;
  position: relative;
  background: #1e1e1e;
  cursor: crosshair;
  min-width: 1000px;
}

.timeline-element {
  position: absolute;
  top: 3px;
  height: 39px;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  cursor: pointer;
  user-select: none;
  border: 1px solid transparent;
  transition: all 0.3s;
}

.timeline-element:hover {
  background: linear-gradient(135deg, #66b1ff, #409eff);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
}

.timeline-element.selected {
  border-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.3);
}

.element-label {
  font-size: 11px;
  color: #ffffff;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 6px;
  background: rgba(255, 255, 255, 0.8);
  cursor: ew-resize;
  opacity: 0;
  transition: opacity 0.3s;
}

.timeline-element:hover .resize-handle {
  opacity: 1;
}

.resize-handle.left {
  left: 0;
  border-radius: 4px 0 0 4px;
}

.resize-handle.right {
  right: 0;
  border-radius: 0 4px 4px 0;
}

/* 时间标尺 */
.timeline-ruler {
  position: absolute;
  top: 0;
  left: 150px;
  right: 0;
  height: 25px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  z-index: 10;
}

.ruler-ticks {
  position: relative;
  height: 100%;
}

.ruler-tick {
  position: absolute;
  top: 0;
  bottom: 0;
  border-left: 1px solid #555555;
  padding-left: 4px;
}

.tick-label {
  font-size: 10px;
  color: #cccccc;
  line-height: 25px;
}

.playhead {
  position: absolute;
  top: 0;
  bottom: -180px;
  width: 2px;
  background: #f56c6c;
  z-index: 20;
  cursor: ew-resize;
  box-shadow: 0 0 4px rgba(245, 108, 108, 0.6);
}

.playhead::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  width: 10px;
  height: 10px;
  background: #f56c6c;
  border-radius: 50%;
  box-shadow: 0 0 6px rgba(245, 108, 108, 0.8);
}

/* 深色主题的Element UI组件覆盖 */
.fly-cut-core ::v-deep .el-tabs__header {
  background: #2d2d2d;
  margin: 0;
}

.fly-cut-core ::v-deep .el-tabs__nav-wrap::after {
  background: #404040;
}

.fly-cut-core ::v-deep .el-tabs__item {
  color: #cccccc;
  border-bottom: 2px solid transparent;
}

.fly-cut-core ::v-deep .el-tabs__item.is-active {
  color: #409eff;
  border-bottom-color: #409eff;
}

.fly-cut-core ::v-deep .el-tabs__content {
  background: #252525;
}

.fly-cut-core ::v-deep .el-button {
  background: #404040;
  border-color: #555555;
  color: #ffffff;
}

.fly-cut-core ::v-deep .el-button:hover {
  background: #4a4a4a;
  border-color: #666666;
}

.fly-cut-core ::v-deep .el-button--primary {
  background: #409eff;
  border-color: #409eff;
}

.fly-cut-core ::v-deep .el-button--primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

.fly-cut-core ::v-deep .el-input__inner {
  background: #404040;
  border-color: #555555;
  color: #ffffff;
}

.fly-cut-core ::v-deep .el-input__inner:focus {
  border-color: #409eff;
}

.fly-cut-core ::v-deep .el-textarea__inner {
  background: #404040;
  border-color: #555555;
  color: #ffffff;
}

.fly-cut-core ::v-deep .el-form-item__label {
  color: #cccccc;
}

.fly-cut-core ::v-deep .el-slider__runway {
  background: #404040;
}

.fly-cut-core ::v-deep .el-slider__bar {
  background: #409eff;
}

.fly-cut-core ::v-deep .el-slider__button {
  border-color: #409eff;
}
</style>
