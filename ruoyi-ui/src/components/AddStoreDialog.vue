<template>
  <el-dialog
    title="新增门店"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="storeForm"
      :model="storeForm"
      :rules="rules"
      label-width="120px"
      class="store-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="门店名称" prop="storeName">
            <el-input
              v-model="storeForm.storeName"
              placeholder="请输入门店名称"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="联系电话" prop="phone">
            <el-input
              v-model="storeForm.phone"
              placeholder="请输入联系电话"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="门店地址" prop="address">
            <el-input
              v-model="storeForm.address"
              placeholder="请输入门店详细地址"
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="门店Logo">
            <el-upload
              class="logo-uploader"
              action="/api/upload"
              :show-file-list="false"
              :on-success="handleLogoSuccess"
              :before-upload="beforeLogoUpload"
            >
              <img v-if="storeForm.avatar" :src="storeForm.avatar" class="logo">
              <i v-else class="el-icon-plus logo-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="营销状态" prop="marketingStatus">
            <el-switch
              v-model="storeForm.marketingStatus"
              :active-value="1"
              :inactive-value="0"
              active-text="营销中"
              inactive-text="已暂停"
            ></el-switch>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">素材重复检测配置</el-divider>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="文案检测">
            <el-switch
              v-model="storeForm.duplicateCheck.text"
              active-text="开启"
              inactive-text="关闭"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="图片检测">
            <el-switch
              v-model="storeForm.duplicateCheck.image"
              active-text="开启"
              inactive-text="关闭"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="视频检测">
            <el-switch
              v-model="storeForm.duplicateCheck.video"
              active-text="开启"
              inactive-text="关闭"
            ></el-switch>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">推广设置</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="NFC推广">
            <el-switch
              v-model="storeForm.nfcPromotion"
              :active-value="1"
              :inactive-value="0"
              active-text="开启"
              inactive-text="关闭"
            ></el-switch>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="二维码推广">
            <el-switch
              v-model="storeForm.qrCodePromotion"
              :active-value="1"
              :inactive-value="0"
              active-text="开启"
              inactive-text="关闭"
            ></el-switch>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">平台账号配置</el-divider>
      <div class="platform-config">
        <h4>平台账号绑定</h4>
        <el-row :gutter="10">
          <el-col 
            v-for="platform in storeForm.platformAccounts" 
            :key="platform.name"
            :span="6"
          >
            <div class="platform-item">
              <div class="platform-header">
                <span class="platform-icon">{{ platform.icon }}</span>
                <span class="platform-name">{{ platform.label }}</span>
              </div>
              <el-switch
                v-model="platform.bound"
                size="mini"
              ></el-switch>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="platform-config">
        <h4>视频素材发布平台</h4>
        <el-row :gutter="10">
          <el-col 
            v-for="platform in storeForm.videoMaterialPlatforms" 
            :key="platform.name"
            :span="6"
          >
            <div class="platform-item">
              <div class="platform-header">
                <span class="platform-icon">{{ platform.icon }}</span>
                <span class="platform-name">{{ platform.label }}</span>
              </div>
              <el-switch
                v-model="platform.bound"
                size="mini"
              ></el-switch>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="platform-config">
        <h4>图片素材发布平台</h4>
        <el-row :gutter="10">
          <el-col 
            v-for="platform in storeForm.imageMaterialPlatforms" 
            :key="platform.name"
            :span="6"
          >
            <div class="platform-item">
              <div class="platform-header">
                <span class="platform-icon">{{ platform.icon }}</span>
                <span class="platform-name">{{ platform.label }}</span>
              </div>
              <el-switch
                v-model="platform.bound"
                size="mini"
              ></el-switch>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ loading ? '创建中...' : '创建门店' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AddStoreDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      loading: false,
      storeForm: {
        storeName: '',
        phone: '',
        address: '',
        avatar: '',
        marketingStatus: 1,
        // 素材重复检测
        duplicateCheck: {
          text: false,
          image: false,
          video: false
        },
        // NFC推广和二维码推广
        nfcPromotion: 0,
        qrCodePromotion: 0,
        // 平台账号绑定
        platformAccounts: [
          { name: 'douyin', label: '抖音', icon: '♫', bound: false },
          { name: 'kuaishou', label: '快手', icon: '⚡', bound: false },
          { name: 'xiaohongshu', label: '小红书', icon: '📚', bound: false },
          { name: 'shipinhao', label: '视频号', icon: '📱', bound: false },
          { name: 'gaode', label: '高德地图', icon: '🗺️', bound: false },
          { name: 'baidu', label: '百度地图', icon: '🔍', bound: false },
          { name: 'meituan', label: '美团', icon: '🍔', bound: false },
          { name: 'dianping', label: '大众点评', icon: '⭐', bound: false }
        ],
        // 视频素材发布平台
        videoMaterialPlatforms: [
          { name: 'douyin', label: '抖音', icon: '♫', bound: false },
          { name: 'kuaishou', label: '快手', icon: '⚡', bound: false },
          { name: 'xiaohongshu', label: '小红书', icon: '📚', bound: false },
          { name: 'pengyouquan', label: '朋友圈', icon: '💬', bound: false },
          { name: 'shipinhao', label: '视频号', icon: '📱', bound: false }
        ],
        // 图片素材发布平台
        imageMaterialPlatforms: [
          { name: 'xiaohongshu', label: '小红书', icon: '📚', bound: false },
          { name: 'pengyouquan', label: '朋友圈', icon: '💬', bound: false },
          { name: 'gaode', label: '高德地图', icon: '🗺️', bound: false },
          { name: 'baidu', label: '百度地图', icon: '🔍', bound: false },
          { name: 'meituan', label: '美团', icon: '🍔', bound: false },
          { name: 'dianping', label: '大众点评', icon: '⭐', bound: false }
        ]
      },
      rules: {
        storeName: [
          { required: true, message: '请输入门店名称', trigger: 'blur' },
          { min: 2, max: 50, message: '门店名称长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '请输入门店地址', trigger: 'blur' },
          { min: 5, max: 200, message: '地址长度在 5 到 200 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    // 处理关闭
    handleClose() {
      this.resetForm()
      this.dialogVisible = false
    },
    
    // 重置表单
    resetForm() {
      this.$refs.storeForm.resetFields()
      this.storeForm = {
        storeName: '',
        phone: '',
        address: '',
        avatar: '',
        marketingStatus: 1,
        duplicateCheck: {
          text: false,
          image: false,
          video: false
        },
        nfcPromotion: 0,
        qrCodePromotion: 0,
        platformAccounts: [
          { name: 'douyin', label: '抖音', icon: '♫', bound: false },
          { name: 'kuaishou', label: '快手', icon: '⚡', bound: false },
          { name: 'xiaohongshu', label: '小红书', icon: '📚', bound: false },
          { name: 'shipinhao', label: '视频号', icon: '📱', bound: false },
          { name: 'gaode', label: '高德地图', icon: '🗺️', bound: false },
          { name: 'baidu', label: '百度地图', icon: '🔍', bound: false },
          { name: 'meituan', label: '美团', icon: '🍔', bound: false },
          { name: 'dianping', label: '大众点评', icon: '⭐', bound: false }
        ],
        videoMaterialPlatforms: [
          { name: 'douyin', label: '抖音', icon: '♫', bound: false },
          { name: 'kuaishou', label: '快手', icon: '⚡', bound: false },
          { name: 'xiaohongshu', label: '小红书', icon: '📚', bound: false },
          { name: 'pengyouquan', label: '朋友圈', icon: '💬', bound: false },
          { name: 'shipinhao', label: '视频号', icon: '📱', bound: false }
        ],
        imageMaterialPlatforms: [
          { name: 'xiaohongshu', label: '小红书', icon: '📚', bound: false },
          { name: 'pengyouquan', label: '朋友圈', icon: '💬', bound: false },
          { name: 'gaode', label: '高德地图', icon: '🗺️', bound: false },
          { name: 'baidu', label: '百度地图', icon: '🔍', bound: false },
          { name: 'meituan', label: '美团', icon: '🍔', bound: false },
          { name: 'dianping', label: '大众点评', icon: '⭐', bound: false }
        ]
      }
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.storeForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.createStore()
        } else {
          this.$message.error('请完善门店信息')
          return false
        }
      })
    },
    
    // 创建门店
    async createStore() {
      try {
        // 生成新的门店ID（实际项目中应该由后端生成）
        const newStoreId = Date.now()
        
        // 构造门店数据
        const storeData = {
          id: newStoreId,
          ...this.storeForm,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        
        // 这里应该调用API创建门店
        // await this.$api.store.create(storeData)
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        // 创建成功后自动创建推广页面
        await this.createPromotionPage(newStoreId, this.storeForm.storeName)
        
        this.$message.success('门店创建成功！推广页面已自动生成')
        
        // 发送事件通知父组件刷新列表
        this.$emit('store-created', storeData)
        
        // 询问是否立即配置推广页面
        this.$confirm('门店创建成功！是否立即配置推广页面？', '提示', {
          confirmButtonText: '立即配置',
          cancelButtonText: '稍后配置',
          type: 'success'
        }).then(() => {
          // 打开推广页面配置
          this.$emit('config-promotion', newStoreId, this.storeForm.storeName)
        }).catch(() => {
          // 用户选择稍后配置
        })
        
        this.handleClose()
      } catch (error) {
        console.error('创建门店失败:', error)
        this.$message.error('创建门店失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    // 创建推广页面
    async createPromotionPage(storeId, storeName) {
      const promotionPageConfig = {
        storeId: storeId,
        storeName: storeName,
        url: `/promotion/${storeId}`,
        config: {
          // 使用默认配置
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          header: {
            title: '碰一碰，领福利',
            subtitle: '请点击进行操作吧'
          },
          store: {
            name: storeName,
            logo: this.storeForm.avatar || '/src/assets/images/default-store.png',
            promotionText: '爆款团购'
          }
          // ... 其他默认配置
        }
      }
      
      // 这里应该调用API创建推广页面配置
      // await this.$api.promotion.create(promotionPageConfig)
      
      console.log('推广页面创建成功:', promotionPageConfig)
    },
    
    // 处理Logo上传成功
    handleLogoSuccess(res, file) {
      this.storeForm.avatar = URL.createObjectURL(file.raw)
    },
    
    // 上传前验证
    beforeLogoUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG) {
        this.$message.error('只能上传 JPG/PNG 格式的图片!')
      }
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 2MB!')
      }
      return isJPG && isLt2M
    }
  }
}
</script>

<style lang="scss" scoped>
.store-form {
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 20px;
}

.platform-config {
  margin-bottom: 20px;
  
  h4 {
    margin: 10px 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
  }
  
  .platform-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    text-align: center;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #409eff;
      background: #ecf5ff;
    }
    
    .platform-header {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 8px;
      
      .platform-icon {
        font-size: 20px;
        margin-bottom: 4px;
      }
      
      .platform-name {
        font-size: 12px;
        color: #606266;
        font-weight: 500;
      }
    }
  }
}

// Logo上传样式
.logo-uploader {
  .logo {
    width: 78px;
    height: 78px;
    display: block;
    border-radius: 6px;
    object-fit: cover;
  }
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 78px;
  height: 78px;
  line-height: 78px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  
  &:hover {
    border-color: #409eff;
  }
}

.dialog-footer {
  text-align: right;
}

.el-divider {
  margin: 20px 0;
}
</style>
