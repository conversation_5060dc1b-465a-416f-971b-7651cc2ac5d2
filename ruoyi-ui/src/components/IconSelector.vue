<template>
  <div class="icon-selector">
    <div class="icon-preview" v-if="value">
      <img :src="value" alt="图标" class="preview-img">
    </div>
    <div class="icon-placeholder" v-else>
      <i class="el-icon-picture"></i>
    </div>
    <el-button size="mini" @click="showIconGrid = !showIconGrid">
      {{ showIconGrid ? '收起' : '选择图标' }}
    </el-button>
    
    <div v-if="showIconGrid" class="icon-grid">
      <div 
        v-for="icon in presetIcons" 
        :key="icon.name"
        class="icon-item"
        @click="selectIcon(icon.path)"
      >
        <img :src="icon.path" :alt="icon.label" class="grid-icon">
        <span class="icon-name">{{ icon.label }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IconSelector',
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showIconGrid: false,
      presetIcons: [
        { name: 'douyin', label: '抖音', path: require('@/assets/images/platforms/douyin.png') },
        { name: 'kuaishou', label: '快手', path: require('@/assets/images/platforms/kuaishou.png') },
        { name: 'xiaohongshu', label: '小红书', path: require('@/assets/images/platforms/xiaohongshu.png') },
        { name: 'pengyouquan', label: '朋友圈', path: require('@/assets/images/platforms/pengyouquan.png') },
        { name: 'shipinhao', label: '视频号', path: require('@/assets/images/platforms/shipinhao.png') },
        { name: 'weixin', label: '微信', path: require('@/assets/images/platforms/weixin.png') },
        { name: 'qq', label: 'QQ', path: require('@/assets/images/platforms/qq.png') },
        { name: 'qiye', label: '企微', path: require('@/assets/images/platforms/qiye.png') },
        { name: 'douyindian', label: '抖音点评', path: require('@/assets/images/platforms/douyindian.png') },
        { name: 'gaodedian', label: '高德点评', path: require('@/assets/images/platforms/gaodedian.png') },
        { name: 'baidudian', label: '百度点评', path: require('@/assets/images/platforms/baidudian.png') },
        { name: 'meituandian', label: '美团点评', path: require('@/assets/images/platforms/meituandian.png') },
        { name: 'dazhongdian', label: '大众点评', path: require('@/assets/images/platforms/dazhongdian.png') }
      ]
    }
  },
  methods: {
    selectIcon(iconPath) {
      this.$emit('input', iconPath)
      this.showIconGrid = false
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-selector {
  display: inline-block;
  
  .icon-preview {
    display: inline-block;
    margin-right: 10px;
    
    .preview-img {
      width: 32px;
      height: 32px;
      object-fit: cover;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
  }
  
  .icon-placeholder {
    display: inline-block;
    width: 32px;
    height: 32px;
    border: 1px dashed #ddd;
    border-radius: 4px;
    margin-right: 10px;
    text-align: center;
    line-height: 30px;
    
    .el-icon-picture {
      color: #999;
    }
  }
  
  .icon-grid {
    position: absolute;
    z-index: 1000;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    margin-top: 5px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    max-width: 300px;
    
    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px;
      cursor: pointer;
      border: 1px solid transparent;
      border-radius: 4px;
      
      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
      }
      
      .grid-icon {
        width: 32px;
        height: 32px;
        object-fit: cover;
        margin-bottom: 4px;
      }
      
      .icon-name {
        font-size: 12px;
        color: #666;
      }
    }
  }
}
</style>
