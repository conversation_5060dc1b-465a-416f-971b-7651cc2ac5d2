<template>
  <div style="padding: 20px; background: white; min-height: 100vh;">
    <h1>🔴 测试页面</h1>
    <p>如果你看到这个页面，说明路由工作正常</p>
    <div style="background: red; color: white; padding: 20px; margin: 20px 0;">
      Vue组件加载成功！当前时间：{{ currentTime }}
    </div>
    <button @click="testClick" style="padding: 10px 20px; background: blue; color: white; border: none;">
      点击测试：{{ clickCount }}
    </button>
  </div>
</template>

<script>
export default {
  name: 'UpdateProgressTest',
  data() {
    return {
      currentTime: new Date().toLocaleString(),
      clickCount: 0
    }
  },
  mounted() {
    console.log('UpdateProgress 测试组件已加载')
  },
  methods: {
    testClick() {
      this.clickCount++
      console.log('按钮被点击了', this.clickCount, '次')
    }
  }
}
</script>
