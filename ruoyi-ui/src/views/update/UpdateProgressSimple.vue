<template>
  <div style="padding: 50px; text-align: center; background: #f0f0f0; min-height: 100vh;">
    <h1 style="color: red; font-size: 2em;">🔴 极简测试页面</h1>
    <p style="font-size: 1.5em; color: green;">如果您看到这个页面，说明Vue路由工作正常！</p>
    <div style="background: yellow; padding: 20px; margin: 20px; border: 3px solid red;">
      <h2>测试信息</h2>
      <p>当前时间: {{ currentTime }}</p>
      <p>组件状态: 正常工作</p>
    </div>
    <button @click="testClick" style="padding: 15px 30px; font-size: 1.2em; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
      点击测试 (点击了 {{ clickCount }} 次)
    </button>
  </div>
</template>

<script>
export default {
  name: 'UpdateProgressSimple',
  data() {
    return {
      currentTime: new Date().toLocaleString(),
      clickCount: 0
    }
  },
  mounted() {
    console.log('✅ 极简测试组件加载成功')
    setInterval(() => {
      this.currentTime = new Date().toLocaleString()
    }, 1000)
  },
  methods: {
    testClick() {
      this.clickCount++
      console.log('按钮被点击了:', this.clickCount)
    }
  }
}
</script>

<style scoped>
button:hover {
  background: #0056b3 !important;
}
</style>
