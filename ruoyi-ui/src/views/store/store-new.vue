<template>
  <div class="store-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-shop"></i>
          门店营销配置
        </h1>
        <p class="page-description">管理门店信息，配置营销推广平台账号</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-plus" @click="addStore">新增门店</el-button>
        <el-button icon="el-icon-refresh" @click="refreshData">刷新</el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="门店名称">
          <el-input
            v-model="searchForm.storeName"
            placeholder="请输入门店名称"
            clearable
            @keyup.enter.native="handleSearch"
          ></el-input>
        </el-form-item>
        <el-form-item label="营销状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="营销中" :value="1"></el-option>
            <el-option label="已暂停" :value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="marketingList"
        style="width: 100%;"
        class="marketing-table"
        :header-cell-style="{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: '#fff', fontWeight: 'bold', fontSize: '13px', textAlign: 'center', whiteSpace: 'nowrap' }"
        border
        stripe
      >
        <!-- 店铺信息 -->
        <el-table-column label="店铺信息" width="180" fixed="left" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="store-info">
              <el-avatar
                :src="scope.row.avatar"
                :size="32"
                class="store-avatar"
              >
                {{ scope.row.storeName.charAt(0) }}
              </el-avatar>
              <div class="store-details">
                <div class="store-name">{{ scope.row.storeName }}</div>
                <div class="store-phone">{{ scope.row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 状态 -->
        <el-table-column label="营销状态" width="120" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="status-control">
              <el-switch
                v-model="scope.row.marketingStatus"
                :active-value="1"
                :inactive-value="0"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
              <div :class="['status-text', scope.row.marketingStatus ? 'active' : 'inactive']">
                {{ scope.row.marketingStatus ? '营销中' : '已暂停' }}
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 绑定各个平台账号 -->
        <el-table-column label="平台账号绑定" width="170" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="platform-summary">
              <div class="platform-stats">
                <span class="bound-count">{{ getBoundCount(scope.row.allPlatforms) }}</span>
                <span class="total-count">/ {{ scope.row.allPlatforms.length }}</span>
                <div class="stats-label">已绑定平台</div>
              </div>
              <el-button
                type="primary"
                size="small"
                @click="showPlatformDialog(scope.row, 'account')"
                class="config-summary-btn"
                icon="el-icon-setting"
              >
                配置账号绑定
              </el-button>
            </div>
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="editStore(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="deleteStore(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Store',
  data() {
    return {
      // 搜索表单
      searchForm: {
        storeName: '',
        status: ''
      },
      // 营销配置列表
      marketingList: [
        {
          id: 1,
          storeName: '谷菱科技总店',
          phone: '13800138000',
          avatar: '',
          marketingStatus: 1,
          allPlatforms: [
            { name: 'douyin', bound: true },
            { name: 'kuaishou', bound: false },
            { name: 'xiaohongshu', bound: true }
          ]
        }
      ],
      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0
    }
  },
  created() {
    this.loadStoreList()
  },
  methods: {
    loadStoreList() {
      // 加载门店列表
      console.log('加载门店列表')
      this.total = this.marketingList.length
    },
    refreshData() {
      this.loadStoreList()
      this.$message.success('数据已刷新')
    },
    handleSearch() {
      this.currentPage = 1
      this.loadStoreList()
    },
    resetSearch() {
      this.searchForm = {
        storeName: '',
        status: ''
      }
      this.handleSearch()
    },
    handleStatusChange(row) {
      this.$message.success(`${row.storeName} 营销状态已${row.marketingStatus ? '开启' : '关闭'}`)
    },
    getBoundCount(platforms) {
      return platforms.filter(p => p.bound).length
    },
    showPlatformDialog(row, type) {
      this.$message.info('平台配置功能开发中...')
    },
    addStore() {
      this.$message.info('新增门店功能开发中...')
    },
    editStore(row) {
      this.$message.info('编辑门店功能开发中...')
    },
    deleteStore(row) {
      this.$confirm('确定要删除这个门店吗？', '删除门店', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
      })
    },
    handleSizeChange(size) {
      this.pageSize = size
      this.loadStoreList()
    },
    handleCurrentChange(page) {
      this.currentPage = page
      this.loadStoreList()
    }
  }
}
</script>

<style lang="scss" scoped>
.store-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  color: #fff;

  .header-content {
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #fff;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 12px;
      }
    }

    .page-description {
      color: rgba(255, 255, 255, 0.9);
      margin: 0;
    }
  }
}

.search-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.store-info {
  display: flex;
  align-items: center;
  
  .store-avatar {
    margin-right: 12px;
  }
  
  .store-details {
    .store-name {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 4px;
    }
    
    .store-phone {
      font-size: 12px;
      color: #7f8c8d;
    }
  }
}

.status-control {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  
  .status-text {
    font-size: 12px;
    
    &.active {
      color: #13ce66;
    }
    
    &.inactive {
      color: #ff4949;
    }
  }
}

.platform-summary {
  text-align: center;
  
  .platform-stats {
    margin-bottom: 8px;
    
    .bound-count {
      font-size: 16px;
      font-weight: 600;
      color: #409eff;
    }
    
    .total-count {
      font-size: 14px;
      color: #7f8c8d;
    }
    
    .stats-label {
      font-size: 11px;
      color: #95a5a6;
    }
  }
  
  .config-summary-btn {
    font-size: 11px;
  }
}

.pagination-container {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    
    .header-actions {
      margin-top: 16px;
    }
  }
}
</style>
