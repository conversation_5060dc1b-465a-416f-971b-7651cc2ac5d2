<template>
  <div class="video-platform-container">
    <!-- 顶部导航 -->
    <div class="top-nav">
      <div class="nav-left">
        <h1>智能视频制作平台</h1>
      </div>
      <div class="nav-right">
        <el-button type="primary" @click="activeView = 'materials'">素材管理</el-button>
        <el-button type="success" @click="activeView = 'projects'">AI剪辑</el-button>
        <el-button @click="showHelp">帮助</el-button>
      </div>
    </div>

    <!-- 素材管理界面 (完全按照图片设计) -->
    <div v-if="activeView === 'materials'" class="materials-workspace">
      <!-- 顶部标签页 -->
      <div class="materials-tabs">
        <div class="tab-buttons">
          <el-button
            :type="materialTab === 'upload' ? 'primary' : 'default'"
            @click="materialTab = 'upload'"
            class="tab-button">
            编辑
          </el-button>
          <el-button
            :type="materialTab === 'cloud' ? 'primary' : 'default'"
            @click="materialTab = 'cloud'"
            class="tab-button">
            调色云端
          </el-button>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="materials-main">
        <!-- 左侧文件夹树 -->
        <div class="folder-sidebar">
          <div class="folder-header">
            <div class="folder-actions">
              <span class="add-folder-icon">+</span>
              <span class="folder-text">新建文件夹</span>
              <el-button type="primary" size="small" @click="showUploadDialog">上传</el-button>
            </div>
          </div>

          <div class="folder-list">
            <div
              v-for="folder in simpleFolderTree"
              :key="folder.id"
              class="folder-item"
              :class="{ active: selectedFolder === folder.id }"
              @click="selectFolder(folder)">
              <i class="folder-icon">📁</i>
              <span class="folder-name">{{ folder.name }}</span>
              <span class="folder-count">{{ folder.count }}</span>
            </div>
          </div>
        </div>

        <!-- 右侧文件区域 -->
        <div class="files-area">
          <!-- 文件操作栏 -->
          <div class="files-toolbar">
            <div class="toolbar-left">
              <el-checkbox v-model="selectAll" @change="handleSelectAll">全选</el-checkbox>
              <span class="file-actions">
                <span class="action-text">重命名</span>
                <span class="action-text">删除</span>
              </span>
            </div>
            <div class="toolbar-right">
              <span class="file-count">共 {{ filteredFiles.length }} 项</span>
              <div class="pagination-info">
                <span>{{ currentPage }}</span>
                <span>/</span>
                <span>{{ totalPages }}</span>
                <span>页</span>
              </div>
            </div>
          </div>

          <!-- 文件列表 -->
          <div class="files-content">
            <div v-if="filteredFiles.length === 0" class="empty-state">
              <div class="empty-icon">📁</div>
              <div class="empty-text">暂无文件</div>
            </div>
            <div v-else class="files-table">
              <div class="table-header">
                <div class="header-cell checkbox-cell">
                  <el-checkbox v-model="selectAll" @change="handleSelectAll"></el-checkbox>
                </div>
                <div class="header-cell name-cell">名称</div>
                <div class="header-cell size-cell">大小</div>
                <div class="header-cell time-cell">修改时间</div>
              </div>
              <div class="table-body">
                <div
                  v-for="file in paginatedFiles"
                  :key="file.id"
                  class="table-row"
                  :class="{ selected: selectedFiles.includes(file.id) }"
                  @click="toggleFileSelection(file.id)">
                  <div class="cell checkbox-cell">
                    <el-checkbox
                      :value="selectedFiles.includes(file.id)"
                      @change="toggleFileSelection(file.id)">
                    </el-checkbox>
                  </div>
                  <div class="cell name-cell">
                    <i :class="getSimpleFileIcon(file.type)" class="file-icon"></i>
                    <span class="file-name">{{ file.name }}</span>
                  </div>
                  <div class="cell size-cell">{{ file.size }}</div>
                  <div class="cell time-cell">{{ file.uploadTime }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI剪辑项目界面 (对应图二) -->
    <div v-if="activeView === 'projects'" class="projects-workspace">
      <div class="projects-header">
        <div class="header-left">
          <h2>视频剪辑列表</h2>
        </div>
        <div class="header-center">
          <el-input 
            v-model="projectSearch" 
            placeholder="请输入关键字" 
            style="width: 300px;">
            <el-button slot="append" icon="el-icon-search" @click="searchProjects">搜索</el-button>
          </el-input>
        </div>
        <div class="header-right">
          <el-button type="primary" icon="el-icon-plus" @click="createNewModel">创建新剪辑模型</el-button>
        </div>
      </div>

      <!-- 项目网格 -->
      <div class="projects-grid">
        <div 
          v-for="project in projectList" 
          :key="project.id" 
          class="project-card"
          @click="openEditor(project)">
          <div class="project-thumbnail">
            <img :src="project.thumbnail" :alt="project.name" />
            <div class="project-overlay">
              <el-button type="primary" size="small">打开编辑</el-button>
            </div>
          </div>
          <div class="project-info">
            <h3>{{ project.name }}</h3>
            <p class="project-id">ID: {{ project.id }}</p>
            <p class="project-time">{{ project.createTime }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 专业编辑器界面 (对应图三) -->
    <div v-if="activeView === 'editor'" class="editor-workspace">
      <!-- 编辑器顶部工具栏 -->
      <div class="editor-toolbar">
        <div class="toolbar-left">
          <el-button size="small" @click="activeView = 'projects'">
            <i class="el-icon-back"></i> 返回
          </el-button>
          <span class="project-title">{{ (currentProject && currentProject.name) || '未命名项目' }}</span>
        </div>
        <div class="toolbar-center">
          <div class="playback-controls">
            <el-button size="small" icon="el-icon-refresh-left">重置</el-button>
            <el-button size="small" :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'" @click="togglePlay">
              {{ isPlaying ? '暂停' : '播放' }}
            </el-button>
            <el-button size="small" icon="el-icon-refresh-right">重做</el-button>
          </div>
        </div>
        <div class="toolbar-right">
          <el-button size="small" type="success" @click="saveProject">保存</el-button>
          <el-button size="small" type="primary" @click="renderVideo">渲染</el-button>
        </div>
      </div>

      <!-- 编辑器主体 -->
      <div class="editor-main">
        <!-- 左侧面板 -->
        <div class="editor-left-panel">
          <el-tabs v-model="leftPanelTab" type="card">
            <el-tab-pane label="素材" name="materials">
              <div class="materials-panel">
                <div class="panel-search">
                  <el-input 
                    v-model="materialSearch" 
                    placeholder="搜索素材..." 
                    size="small"
                    clearable>
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                  </el-input>
                </div>
                <div class="material-categories">
                  <div 
                    v-for="category in materialCategories" 
                    :key="category.key"
                    class="category-item"
                    :class="{ active: selectedCategory === category.key }"
                    @click="selectedCategory = category.key">
                    <i :class="category.icon"></i>
                    <span>{{ category.name }}</span>
                  </div>
                </div>
                <div class="materials-list">
                  <div 
                    v-for="material in filteredMaterials" 
                    :key="material.id"
                    class="material-item"
                    draggable="true"
                    @dragstart="onMaterialDragStart(material, $event)">
                    <div class="material-thumb">
                      <img v-if="material.type === 'image'" :src="material.thumbnail" />
                      <video v-else-if="material.type === 'video'" :src="material.url" :poster="material.thumbnail"></video>
                      <i v-else :class="getFileIcon(material.type)"></i>
                    </div>
                    <div class="material-name">{{ material.name }}</div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="效果" name="effects">
              <div class="effects-panel">
                <div class="effect-categories">
                  <h4>转场效果</h4>
                  <div class="effects-grid">
                    <div v-for="effect in transitionEffects" :key="effect.id" class="effect-item">
                      <div class="effect-preview">{{ effect.icon }}</div>
                      <span>{{ effect.name }}</span>
                    </div>
                  </div>
                  <h4>滤镜效果</h4>
                  <div class="effects-grid">
                    <div v-for="filter in filterEffects" :key="filter.id" class="effect-item">
                      <div class="effect-preview">{{ filter.icon }}</div>
                      <span>{{ filter.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- 中间预览区 -->
        <div class="editor-center-panel">
          <div class="video-preview-container">
            <div class="preview-header">
              <div class="preview-info">
                <span>分辨率: 1920x1080</span>
                <span>帧率: 30fps</span>
                <span>时长: {{ formatTime(totalDuration) }}</span>
              </div>
              <div class="preview-controls">
                <el-button-group size="mini">
                  <el-button icon="el-icon-zoom-out" @click="zoomOut"></el-button>
                  <el-button>{{ Math.round(zoomLevel * 100) }}%</el-button>
                  <el-button icon="el-icon-zoom-in" @click="zoomIn"></el-button>
                </el-button-group>
              </div>
            </div>
            
            <div class="video-canvas-container">
              <div class="video-canvas" :style="{ transform: `scale(${zoomLevel})` }">
                <div class="canvas-content">
                  <div v-if="!hasContent" class="canvas-placeholder">
                    <i class="el-icon-video-camera"></i>
                    <p>拖拽素材到时间轴开始编辑</p>
                  </div>
                  <video v-else ref="previewVideo" :src="previewUrl" @timeupdate="onTimeUpdate"></video>
                </div>
              </div>
            </div>

            <div class="playback-bar">
              <div class="time-info">
                <span>{{ formatTime(currentTime) }}</span>
                <el-slider 
                  v-model="currentTime" 
                  :max="totalDuration" 
                  :step="0.1"
                  @change="seekTo"
                  style="flex: 1; margin: 0 15px;">
                </el-slider>
                <span>{{ formatTime(totalDuration) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧属性面板 -->
        <div class="editor-right-panel">
          <el-tabs v-model="rightPanelTab" type="card">
            <el-tab-pane label="属性" name="properties">
              <div class="properties-panel">
                <div v-if="selectedElement">
                  <h4>{{ selectedElement.name }}</h4>
                  <el-form label-width="60px" size="small">
                    <el-form-item label="位置">
                      <div style="display: flex; gap: 8px;">
                        <el-input-number v-model="selectedElement.x" :step="1" size="small" style="width: 70px;"></el-input-number>
                        <el-input-number v-model="selectedElement.y" :step="1" size="small" style="width: 70px;"></el-input-number>
                      </div>
                    </el-form-item>
                    <el-form-item label="大小">
                      <div style="display: flex; gap: 8px;">
                        <el-input-number v-model="selectedElement.width" :step="1" size="small" style="width: 70px;"></el-input-number>
                        <el-input-number v-model="selectedElement.height" :step="1" size="small" style="width: 70px;"></el-input-number>
                      </div>
                    </el-form-item>
                    <el-form-item label="旋转">
                      <el-slider v-model="selectedElement.rotation" :min="-180" :max="180"></el-slider>
                    </el-form-item>
                    <el-form-item label="透明度">
                      <el-slider v-model="selectedElement.opacity" :max="100"></el-slider>
                    </el-form-item>
                  </el-form>
                </div>
                <div v-else class="no-selection">
                  <p>请选择一个元素</p>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="音频" name="audio">
              <div class="audio-panel">
                <h4>音频设置</h4>
                <el-form label-width="60px" size="small">
                  <el-form-item label="主音量">
                    <el-slider v-model="audioSettings.masterVolume" :max="100"></el-slider>
                  </el-form-item>
                  <el-form-item label="对比度">
                    <el-slider v-model="audioSettings.contrast" :max="100"></el-slider>
                  </el-form-item>
                  <el-form-item label="饱和度">
                    <el-slider v-model="audioSettings.saturation" :max="100"></el-slider>
                  </el-form-item>
                </el-form>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 底部时间轴 -->
      <div class="timeline-container">
        <div class="timeline-header">
          <div class="timeline-controls">
            <el-button size="mini" icon="el-icon-plus" @click="addTrack">添加轨道</el-button>
            <el-button size="mini" icon="el-icon-delete" @click="deleteTrack">删除轨道</el-button>
            <div class="timeline-scale">
              <span>缩放:</span>
              <el-slider v-model="timelineScale" :min="10" :max="200" style="width: 80px; margin-left: 8px;"></el-slider>
            </div>
          </div>
        </div>
        
        <div class="timeline-content">
          <!-- 时间标尺 -->
          <div class="timeline-ruler">
            <div class="ruler-track">
              <div class="ruler-marks">
                <div v-for="mark in timelineMarks" :key="mark.time" 
                     class="time-mark" 
                     :style="{ left: mark.position + 'px' }">
                  <div class="mark-line"></div>
                  <div class="mark-text">{{ formatTime(mark.time) }}</div>
                </div>
              </div>
              <div class="playhead" :style="{ left: playheadPosition + 'px' }"></div>
            </div>
          </div>
          
          <!-- 轨道区域 -->
          <div class="timeline-tracks">
            <div v-for="track in timelineTracks" :key="track.id" class="timeline-track">
              <div class="track-header">
                <div class="track-controls">
                  <el-button size="mini" :type="track.visible ? 'primary' : 'info'" 
                             icon="el-icon-view" @click="toggleTrackVisibility(track)"></el-button>
                  <el-button size="mini" :type="track.muted ? 'danger' : 'primary'" 
                             icon="el-icon-headset" @click="toggleTrackMute(track)"></el-button>
                </div>
                <div class="track-name">{{ track.name }}</div>
              </div>
              
              <div class="track-content" 
                   @drop="onTrackDrop(track, $event)" 
                   @dragover.prevent 
                   @dragenter.prevent>
                <div v-for="clip in track.clips" :key="clip.id" 
                     class="timeline-clip" 
                     :class="{ selected: selectedClip === clip }"
                     :style="{ 
                       left: (clip.startTime * timelineScale / 100) + 'px', 
                       width: (clip.duration * timelineScale / 100) + 'px' 
                     }"
                     @click="selectClip(clip)">
                  <div class="clip-content">
                    <div class="clip-thumbnail">
                      <img v-if="clip.type === 'image'" :src="clip.thumbnail" />
                      <video v-else-if="clip.type === 'video'" :src="clip.url"></video>
                      <div v-else class="audio-wave"></div>
                    </div>
                    <div class="clip-name">{{ clip.name }}</div>
                  </div>
                  <div class="clip-resize-left" @mousedown.stop="startResize(clip, 'left', $event)"></div>
                  <div class="clip-resize-right" @mousedown.stop="startResize(clip, 'right', $event)"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog title="上传素材" :visible.sync="uploadDialogVisible" width="500px">
      <el-upload
        class="upload-dragger"
        drag
        action="/api/upload"
        multiple
        :before-upload="beforeUpload"
        :on-success="onUploadSuccess">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">支持 jpg、png、mp4、mp3 格式文件</div>
      </el-upload>
    </el-dialog>

    <!-- 新建模型对话框 -->
    <el-dialog title="创建新剪辑模型" :visible.sync="newModelDialogVisible" width="400px">
      <el-form :model="newModelForm" label-width="80px">
        <el-form-item label="模型名称">
          <el-input v-model="newModelForm.name" placeholder="请输入模型名称"></el-input>
        </el-form-item>
        <el-form-item label="模板类型">
          <el-select v-model="newModelForm.template" placeholder="选择模板">
            <el-option label="空白模板" value="blank"></el-option>
            <el-option label="产品展示" value="product"></el-option>
            <el-option label="生活记录" value="lifestyle"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="视频比例">
          <el-radio-group v-model="newModelForm.ratio">
            <el-radio label="16:9">横屏</el-radio>
            <el-radio label="9:16">竖屏</el-radio>
            <el-radio label="1:1">方形</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="newModelDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createModel">创建</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'VideoPlatform',
  props: {
    initialView: {
      type: String,
      default: 'materials'
    }
  },
  data() {
    return {
      // 当前视图
      activeView: this.initialView, // materials, projects, editor

      // 搜索关键词
      searchKeyword: '',
      projectSearch: '',

      // 素材管理
      materialTab: 'upload', // upload, cloud
      selectedFolder: 1,
      selectAll: false,
      selectedFiles: [],
      currentPage: 1,
      pageSize: 10,

      // 简化的文件夹结构（按照图片设计）
      simpleFolderTree: [
        { id: 1, name: '总', count: '---' },
        { id: 2, name: '库', count: '---' },
        { id: 3, name: '中', count: '---' },
        { id: 4, name: '文', count: '---' },
        { id: 5, name: '内容1', count: '---' },
        { id: 6, name: '0.0', count: '---' },
        { id: 7, name: '内容3', count: '---' },
        { id: 8, name: '内容2', count: '---' },
        { id: 9, name: '内容', count: '---' },
        { id: 10, name: '片库', count: '---' },
        { id: 11, name: '片头', count: '---' }
      ],

      fileList: [
        {
          id: 1,
          name: '产品展示视频.mp4',
          type: 'video',
          size: '25.6MB',
          uploadTime: '2025-01-11 10:30',
          thumbnail: '/static/thumb1.jpg',
          url: '/static/video1.mp4',
          folderId: 2
        },
        {
          id: 2,
          name: '背景音乐.mp3',
          type: 'audio',
          size: '3.2MB',
          uploadTime: '2025-01-11 09:15',
          thumbnail: '',
          url: '/static/audio1.mp3',
          folderId: 4
        },
        {
          id: 3,
          name: '产品图片.jpg',
          type: 'image',
          size: '2.1MB',
          uploadTime: '2025-01-11 08:45',
          thumbnail: '/static/image1.jpg',
          url: '/static/image1.jpg',
          folderId: 3
        }
      ],

      // 项目列表
      projectList: [
        {
          id: 'ID-306618',
          name: '未命名剪辑模型2025.08.11',
          thumbnail: '/static/project1.jpg',
          createTime: '2025-08-09 16:07:01'
        },
        {
          id: 'ID-306298',
          name: '未命名剪辑模型2025.08.10',
          thumbnail: '/static/project2.jpg',
          createTime: '2025-08-08 15:05:12'
        },
        {
          id: 'ID-306199',
          name: '未命名剪辑模型2025.08.09',
          thumbnail: '/static/project3.jpg',
          createTime: '2025-08-08 14:27:36'
        },
        {
          id: 'ID-304802',
          name: '未命名剪辑模型2025.08.08',
          thumbnail: '/static/project4.jpg',
          createTime: '2025-08-08 13:47:31'
        }
      ],

      // 编辑器相关
      currentProject: null,
      leftPanelTab: 'materials',
      rightPanelTab: 'properties',
      selectedCategory: 'all',
      materialSearch: '',

      materialCategories: [
        { key: 'all', name: '全部', icon: 'el-icon-folder' },
        { key: 'video', name: '视频', icon: 'el-icon-video-camera' },
        { key: 'image', name: '图片', icon: 'el-icon-picture' },
        { key: 'audio', name: '音频', icon: 'el-icon-headset' }
      ],

      // 播放控制
      isPlaying: false,
      currentTime: 0,
      totalDuration: 0,
      zoomLevel: 1,
      hasContent: false,
      previewUrl: '',

      // 选中元素
      selectedElement: null,
      selectedClip: null,

      // 音频设置
      audioSettings: {
        masterVolume: 80,
        contrast: 50,
        saturation: 50
      },

      // 时间轴
      timelineScale: 100,
      playheadPosition: 0,
      timelineTracks: [
        {
          id: 1,
          name: '视频轨道',
          type: 'video',
          visible: true,
          muted: false,
          clips: []
        },
        {
          id: 2,
          name: '音频轨道',
          type: 'audio',
          visible: true,
          muted: false,
          clips: []
        }
      ],

      // 效果
      transitionEffects: [
        { id: 1, name: '淡入', icon: '🌅' },
        { id: 2, name: '淡出', icon: '🌇' },
        { id: 3, name: '滑动', icon: '➡️' },
        { id: 4, name: '缩放', icon: '🔍' }
      ],

      filterEffects: [
        { id: 1, name: '复古', icon: '📷' },
        { id: 2, name: '黑白', icon: '⚫' },
        { id: 3, name: '暖色', icon: '🔥' },
        { id: 4, name: '冷色', icon: '❄️' }
      ],

      // 对话框
      uploadDialogVisible: false,
      newModelDialogVisible: false,
      newModelForm: {
        name: '',
        template: '',
        ratio: '16:9'
      }
    }
  },

  computed: {
    filteredFiles() {
      let files = this.fileList

      // 按文件夹过滤（简化版本）
      if (this.selectedFolder !== 1) {
        // 这里可以根据实际需求添加过滤逻辑
        // 目前显示所有文件
      }

      // 按关键词搜索
      if (this.searchKeyword) {
        files = files.filter(f => f.name.toLowerCase().includes(this.searchKeyword.toLowerCase()))
      }

      return files
    },

    paginatedFiles() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredFiles.slice(start, end)
    },

    totalPages() {
      return Math.ceil(this.filteredFiles.length / this.pageSize)
    },

    filteredMaterials() {
      let materials = this.fileList

      if (this.selectedCategory !== 'all') {
        materials = materials.filter(m => m.type === this.selectedCategory)
      }

      if (this.materialSearch) {
        materials = materials.filter(m => m.name.toLowerCase().includes(this.materialSearch.toLowerCase()))
      }

      return materials
    },

    timelineMarks() {
      const marks = []
      const duration = this.totalDuration || 60 // 默认60秒
      const interval = 5 // 每5秒一个标记

      for (let i = 0; i <= duration; i += interval) {
        marks.push({
          time: i,
          position: (i / duration) * 800 // 假设时间轴宽度800px
        })
      }

      return marks
    }
  },

  methods: {
    // 基础方法
    showHelp() {
      this.$alert('智能视频制作平台帮助文档', '帮助', {
        confirmButtonText: '确定'
      })
    },

    // 素材管理方法
    selectFolder(folder) {
      this.selectedFolder = folder.id
    },

    showUploadDialog() {
      this.uploadDialogVisible = true
    },

    createFolder() {
      this.$prompt('请输入文件夹名称', '新建文件夹', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        const newFolder = {
          id: Date.now(),
          name: value,
          icon: 'el-icon-folder',
          count: 0
        }
        this.folderTree.push(newFolder)
        this.$message.success('文件夹创建成功')
      })
    },

    selectFile(file) {
      console.log('选择文件:', file)
    },

    previewFile(file) {
      this.$message.info(`预览文件: ${file.name}`)
    },

    deleteFile(file) {
      this.$confirm('确定要删除这个文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.fileList.findIndex(f => f.id === file.id)
        if (index > -1) {
          this.fileList.splice(index, 1)
          this.$message.success('删除成功')
        }
      })
    },

    getFileIcon(type) {
      const iconMap = {
        'video': 'el-icon-video-camera',
        'image': 'el-icon-picture',
        'audio': 'el-icon-headset'
      }
      return iconMap[type] || 'el-icon-document'
    },

    getSimpleFileIcon(type) {
      const iconMap = {
        'video': '🎬',
        'image': '🖼️',
        'audio': '🎵'
      }
      return iconMap[type] || '📄'
    },

    // 文件选择相关方法
    handleSelectAll(checked) {
      if (checked) {
        this.selectedFiles = this.paginatedFiles.map(f => f.id)
      } else {
        this.selectedFiles = []
      }
    },

    toggleFileSelection(fileId) {
      const index = this.selectedFiles.indexOf(fileId)
      if (index > -1) {
        this.selectedFiles.splice(index, 1)
      } else {
        this.selectedFiles.push(fileId)
      }

      // 更新全选状态
      this.selectAll = this.selectedFiles.length === this.paginatedFiles.length
    },

    // 上传相关
    beforeUpload(file) {
      const isValidType = ['video/', 'image/', 'audio/'].some(type => file.type.startsWith(type))
      const isLt50M = file.size / 1024 / 1024 < 50

      if (!isValidType) {
        this.$message.error('只能上传视频、图片或音频文件!')
        return false
      }
      if (!isLt50M) {
        this.$message.error('文件大小不能超过 50MB!')
        return false
      }
      return true
    },

    onUploadSuccess(response, file) {
      const newFile = {
        id: Date.now(),
        name: file.name,
        type: file.type.split('/')[0],
        size: (file.size / 1024 / 1024).toFixed(1) + 'MB',
        uploadTime: new Date().toLocaleString(),
        thumbnail: response.thumbnail || '',
        url: response.url || '',
        folderId: this.selectedFolder
      }
      this.fileList.push(newFile)
      this.$message.success(`${file.name} 上传成功`)
    },

    // 项目管理方法
    searchProjects() {
      console.log('搜索项目:', this.projectSearch)
    },

    createNewModel() {
      this.newModelDialogVisible = true
    },

    createModel() {
      if (!this.newModelForm.name) {
        this.$message.error('请输入模型名称')
        return
      }

      const newProject = {
        id: 'ID-' + Date.now(),
        name: this.newModelForm.name,
        thumbnail: '/static/default-project.jpg',
        createTime: new Date().toLocaleString()
      }

      this.projectList.unshift(newProject)
      this.newModelDialogVisible = false
      this.newModelForm = { name: '', template: '', ratio: '16:9' }
      this.$message.success('剪辑模型创建成功')
    },

    openEditor(project) {
      this.currentProject = project
      this.activeView = 'editor'
      this.$message.success(`打开编辑器: ${project.name}`)
    },

    // 编辑器方法
    togglePlay() {
      this.isPlaying = !this.isPlaying
      if (this.$refs.previewVideo) {
        if (this.isPlaying) {
          this.$refs.previewVideo.play()
        } else {
          this.$refs.previewVideo.pause()
        }
      }
    },

    zoomIn() {
      this.zoomLevel = Math.min(this.zoomLevel + 0.1, 3)
    },

    zoomOut() {
      this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.1)
    },

    seekTo(time) {
      this.currentTime = time
      if (this.$refs.previewVideo) {
        this.$refs.previewVideo.currentTime = time
      }
    },

    onTimeUpdate(event) {
      this.currentTime = event.target.currentTime
      this.playheadPosition = (this.currentTime / this.totalDuration) * 800
    },

    formatTime(seconds) {
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },

    // 素材拖拽
    onMaterialDragStart(material, event) {
      event.dataTransfer.setData('material', JSON.stringify(material))
    },

    onTrackDrop(track, event) {
      event.preventDefault()
      const materialData = event.dataTransfer.getData('material')
      if (materialData) {
        const material = JSON.parse(materialData)
        const newClip = {
          id: Date.now(),
          name: material.name,
          type: material.type,
          url: material.url,
          thumbnail: material.thumbnail,
          startTime: 0,
          duration: 5, // 默认5秒
          trackId: track.id
        }
        track.clips.push(newClip)
        this.hasContent = true
        this.$message.success(`已添加 ${material.name} 到 ${track.name}`)
      }
    },

    // 时间轴操作
    addTrack() {
      const newTrack = {
        id: Date.now(),
        name: `轨道 ${this.timelineTracks.length + 1}`,
        type: 'video',
        visible: true,
        muted: false,
        clips: []
      }
      this.timelineTracks.push(newTrack)
    },

    deleteTrack() {
      if (this.timelineTracks.length > 1) {
        this.timelineTracks.pop()
      }
    },

    selectClip(clip) {
      this.selectedClip = clip
      this.selectedElement = clip
    },

    toggleTrackVisibility(track) {
      track.visible = !track.visible
    },

    toggleTrackMute(track) {
      track.muted = !track.muted
    },

    startResize(clip, direction, event) {
      console.log('开始调整片段大小:', clip, direction)
    },

    // 项目操作
    saveProject() {
      if (this.currentProject) {
        this.$message.success('项目已保存')
      }
    },

    renderVideo() {
      this.$message.info('开始渲染视频...')
    }
  }
}
</script>

<style scoped>
/* 全局容器 */
.video-platform-container {
  height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

/* 顶部导航 */
.top-nav {
  background: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  z-index: 100;
}

.nav-left h1 {
  margin: 0;
  color: #303133;
  font-size: 20px;
}

.nav-right {
  display: flex;
  gap: 10px;
}

/* 素材管理界面 - 完全按照图片设计 */
.materials-workspace {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  margin: 0;
}

/* 顶部标签页 */
.materials-tabs {
  background: white;
  padding: 0;
  border-bottom: 1px solid #e8e8e8;
}

.tab-buttons {
  display: flex;
  gap: 0;
}

.tab-button {
  border-radius: 20px !important;
  margin: 10px 5px;
  padding: 8px 20px;
  border: none;
  font-size: 14px;
}

.tab-button.el-button--primary {
  background: #4a90e2;
  color: white;
}

.tab-button.el-button--default {
  background: #e8e8e8;
  color: #666;
}

/* 主要内容区域 */
.materials-main {
  flex: 1;
  display: flex;
  background: white;
}

/* 左侧文件夹树 - 按照图片样式 */
.folder-sidebar {
  width: 200px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.folder-header {
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
}

.folder-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-folder-icon {
  font-size: 16px;
  color: #666;
  cursor: pointer;
}

.folder-text {
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.folder-list {
  flex: 1;
  padding: 10px 0;
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  gap: 8px;
}

.folder-item:hover {
  background: #f5f5f5;
}

.folder-item.active {
  background: #e6f3ff;
}

.folder-icon {
  font-size: 16px;
}

.folder-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.folder-count {
  font-size: 12px;
  color: #999;
}

/* 右侧文件区域 */
.files-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

/* 文件操作栏 */
.files-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #e8e8e8;
  background: #fafafa;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.file-actions {
  display: flex;
  gap: 15px;
}

.action-text {
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.action-text:hover {
  color: #4a90e2;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #666;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 2px;
}

/* 文件内容区域 */
.files-content {
  flex: 1;
  overflow: auto;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
}

/* 文件表格 */
.files-table {
  width: 100%;
}

.table-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.table-body {
  background: white;
}

.table-row {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row.selected {
  background: #e6f3ff;
}

.header-cell,
.cell {
  display: flex;
  align-items: center;
}

.checkbox-cell {
  width: 50px;
  justify-content: center;
}

.name-cell {
  flex: 1;
  min-width: 200px;
}

.size-cell {
  width: 100px;
}

.time-cell {
  width: 150px;
}

.file-icon {
  margin-right: 8px;
  font-size: 16px;
}

.file-name {
  font-size: 14px;
  color: #333;
}

/* 项目管理界面 */
.projects-workspace {
  flex: 1;
  background: white;
  margin: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
}

.projects-header {
  padding: 20px;
  border-bottom: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.projects-header h2 {
  margin: 0;
  color: #303133;
  font-size: 18px;
}

.projects-grid {
  flex: 1;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  overflow-y: auto;
}

.project-card {
  border: 1px solid #EBEEF5;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
}

.project-card:hover {
  border-color: #409EFF;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.project-thumbnail {
  height: 160px;
  background: #f5f7fa;
  position: relative;
  overflow: hidden;
}

.project-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-info {
  padding: 15px;
}

.project-info h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #303133;
}

.project-id {
  margin: 0 0 5px 0;
  font-size: 12px;
  color: #909399;
}

.project-time {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

/* 编辑器界面 */
.editor-workspace {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #2c3e50;
  color: white;
}

.editor-toolbar {
  background: #34495e;
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #4a5f7a;
}

.project-title {
  margin-left: 15px;
  font-weight: bold;
  color: #ecf0f1;
}

.playback-controls {
  display: flex;
  gap: 8px;
}

.editor-main {
  flex: 1;
  display: flex;
  min-height: 0;
}

/* 左侧面板 */
.editor-left-panel {
  width: 250px;
  background: #34495e;
  border-right: 1px solid #4a5f7a;
}

.materials-panel {
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.panel-search {
  margin-bottom: 15px;
}

.material-categories {
  margin-bottom: 15px;
}

.category-item {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 5px;
  transition: background-color 0.2s;
}

.category-item:hover {
  background: #4a5f7a;
}

.category-item.active {
  background: #3498db;
}

.category-item i {
  margin-right: 8px;
}

.materials-list {
  flex: 1;
  overflow-y: auto;
}

.material-item {
  background: #4a5f7a;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: grab;
  transition: background-color 0.2s;
}

.material-item:hover {
  background: #5a6f8a;
}

.material-thumb {
  height: 60px;
  background: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px 4px 0 0;
}

.material-thumb img,
.material-thumb video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px 4px 0 0;
}

.material-thumb i {
  font-size: 24px;
  color: #7f8c8d;
}

.material-name {
  padding: 8px;
  font-size: 12px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.effects-panel {
  padding: 15px;
}

.effect-categories h4 {
  margin: 0 0 10px 0;
  color: #ecf0f1;
  font-size: 14px;
}

.effects-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 20px;
}

.effect-item {
  background: #4a5f7a;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.effect-item:hover {
  background: #5a6f8a;
}

.effect-preview {
  font-size: 20px;
  margin-bottom: 5px;
}

/* 中间预览区 */
.editor-center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #2c3e50;
}

.video-preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.preview-info {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #bdc3c7;
}

.video-canvas-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1a252f;
  border-radius: 8px;
  position: relative;
}

.video-canvas {
  max-width: 100%;
  max-height: 100%;
  transition: transform 0.2s;
}

.canvas-content {
  width: 640px;
  height: 360px;
  background: #000;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.canvas-placeholder {
  text-align: center;
  color: #7f8c8d;
}

.canvas-placeholder i {
  font-size: 48px;
  display: block;
  margin-bottom: 10px;
}

.canvas-content video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.playback-bar {
  margin-top: 15px;
}

.time-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 右侧属性面板 */
.editor-right-panel {
  width: 250px;
  background: #34495e;
  border-left: 1px solid #4a5f7a;
}

.properties-panel {
  padding: 15px;
}

.properties-panel h4 {
  margin: 0 0 15px 0;
  color: #ecf0f1;
}

.no-selection {
  text-align: center;
  color: #7f8c8d;
  padding: 20px;
}

.audio-panel {
  padding: 15px;
}

.audio-panel h4 {
  margin: 0 0 15px 0;
  color: #ecf0f1;
}

/* 时间轴 */
.timeline-container {
  height: 200px;
  background: #34495e;
  border-top: 1px solid #4a5f7a;
  display: flex;
  flex-direction: column;
}

.timeline-header {
  padding: 10px 15px;
  border-bottom: 1px solid #4a5f7a;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timeline-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.timeline-scale {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #bdc3c7;
}

.timeline-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.timeline-ruler {
  height: 30px;
  background: #2c3e50;
  border-bottom: 1px solid #4a5f7a;
  position: relative;
}

.ruler-track {
  height: 100%;
  position: relative;
}

.ruler-marks {
  height: 100%;
  position: relative;
}

.time-mark {
  position: absolute;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.mark-line {
  width: 1px;
  height: 15px;
  background: #7f8c8d;
}

.mark-text {
  font-size: 10px;
  color: #bdc3c7;
  margin-top: 2px;
}

.playhead {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background: #e74c3c;
  z-index: 10;
}

.timeline-tracks {
  flex: 1;
  overflow-y: auto;
}

.timeline-track {
  display: flex;
  height: 60px;
  border-bottom: 1px solid #4a5f7a;
}

.track-header {
  width: 120px;
  background: #2c3e50;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-right: 1px solid #4a5f7a;
}

.track-controls {
  display: flex;
  gap: 5px;
  margin-bottom: 5px;
}

.track-name {
  font-size: 12px;
  color: #ecf0f1;
}

.track-content {
  flex: 1;
  background: #1a252f;
  position: relative;
  min-height: 60px;
}

.timeline-clip {
  position: absolute;
  height: 50px;
  background: #3498db;
  border-radius: 4px;
  margin: 5px;
  cursor: move;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.timeline-clip.selected {
  border-color: #f39c12;
}

.timeline-clip:hover {
  background: #2980b9;
}

.clip-content {
  flex: 1;
  display: flex;
  align-items: center;
  padding: 5px;
  min-width: 0;
}

.clip-thumbnail {
  width: 30px;
  height: 30px;
  background: #2c3e50;
  border-radius: 2px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.clip-thumbnail img,
.clip-thumbnail video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.audio-wave {
  width: 100%;
  height: 20px;
  background: linear-gradient(90deg, #27ae60 0%, #2ecc71 50%, #27ae60 100%);
  border-radius: 2px;
}

.clip-name {
  font-size: 11px;
  color: white;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.clip-resize-left,
.clip-resize-right {
  position: absolute;
  top: 0;
  width: 5px;
  height: 100%;
  cursor: ew-resize;
  background: rgba(255,255,255,0.2);
  opacity: 0;
  transition: opacity 0.2s;
}

.clip-resize-left {
  left: 0;
}

.clip-resize-right {
  right: 0;
}

.timeline-clip:hover .clip-resize-left,
.timeline-clip:hover .clip-resize-right {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .editor-left-panel,
  .editor-right-panel {
    width: 200px;
  }
}

@media (max-width: 768px) {
  .materials-content {
    flex-direction: column;
  }

  .folder-sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #EBEEF5;
  }

  .projects-grid {
    grid-template-columns: 1fr;
  }

  .editor-main {
    flex-direction: column;
  }

  .editor-left-panel,
  .editor-right-panel {
    width: 100%;
    height: 200px;
  }
}
</style>
