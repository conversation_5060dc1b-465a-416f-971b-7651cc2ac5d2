<template>
  <div class="marketing-config-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="el-icon-setting"></i>
        门店营销配置管理
      </h1>
      <p class="page-description">管理门店的营销配置和发布设置</p>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        :data="configList"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266', fontWeight: 'bold', textAlign: 'center' }"
        :cell-style="{ verticalAlign: 'middle' }"
      >
        <!-- 店铺信息 -->
        <el-table-column label="店铺信息" width="180" align="left">
          <template slot-scope="scope">
            <div class="store-info">
              <el-avatar 
                :size="45" 
                :src="scope.row.storeAvatar" 
                icon="el-icon-shop"
                :style="getStoreAvatarStyle(scope.row.storeName)"
              ></el-avatar>
              <div class="store-details">
                <div class="store-name">{{ scope.row.storeName }}</div>
                <div class="store-id">ID: {{ scope.row.id }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 状态 -->
        <el-table-column label="状态" width="120" align="center">
          <template slot-scope="scope">
            <div class="toggle-group">
              <div class="toggle-item">
                <span class="toggle-label">素材：</span>
                <el-switch
                  v-model="scope.row.materialRepeat"
                  active-color="#409eff"
                  inactive-color="#dcdfe6"
                ></el-switch>
              </div>
              <div class="toggle-item">
                <span class="toggle-label">文案：</span>
                <el-switch
                  v-model="scope.row.copyRepeat"
                  active-color="#409eff"
                  inactive-color="#dcdfe6"
                ></el-switch>
              </div>
              <div class="toggle-item">
                <span class="toggle-label">启用：</span>
                <el-switch
                  v-model="scope.row.status"
                  active-color="#409eff"
                  inactive-color="#dcdfe6"
                ></el-switch>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 绑定各个平台账号 -->
        <el-table-column label="绑定各个平台账号" width="180" align="left">
          <template slot-scope="scope">
            <div class="platform-list">
              <div 
                v-for="platform in scope.row.videoPlatforms" 
                :key="platform"
                class="platform-item video-platform"
                @click="editPlatform('video', platform)"
              >
                <div class="platform-content">
                  <i :class="getPlatformIcon(platform)" class="platform-icon"></i>
                  <span>{{ platform }}</span>
                </div>
                <i class="el-icon-edit edit-icon"></i>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 配置各个平台发布素材 -->
        <el-table-column label="配置各个平台发布素材" width="200" align="left">
          <template slot-scope="scope">
            <div class="platform-list">
              <div 
                v-for="platform in scope.row.imagePlatforms" 
                :key="platform"
                class="platform-item image-platform"
                @click="editPlatform('image', platform)"
              >
                <div class="platform-content">
                  <i :class="getPlatformIcon(platform)" class="platform-icon"></i>
                  <span>{{ platform }}</span>
                </div>
                <i class="el-icon-edit edit-icon"></i>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 配置各个平台点评素材 -->
        <el-table-column label="配置各个平台点评素材" width="200" align="center">
          <template slot-scope="scope">
            <div class="config-list">
              <div class="config-item" @click="editConfig('review')">
                <span>点评文案</span>
                <i class="el-icon-edit edit-icon"></i>
              </div>
              <div class="config-item" @click="editConfig('reviewConfig')">
                <span>点评配置</span>
                <i class="el-icon-edit edit-icon"></i>
              </div>
              <div class="config-item" @click="editConfig('groupBuy')">
                <span>团购配置</span>
                <i class="el-icon-edit edit-icon"></i>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 其他配置 -->
        <el-table-column label="其他配置" width="150" align="left">
          <template slot-scope="scope">
            <div class="platform-list">
              <div class="config-item" @click="editConfig('wifi')">
                <span>WIFI配置</span>
                <i class="el-icon-edit edit-icon"></i>
              </div>
              <div 
                v-for="config in scope.row.otherConfigs" 
                :key="config"
                class="platform-item"
                @click="editConfig('other', config)"
              >
                <div class="platform-content">
                  <i class="el-icon-setting platform-icon"></i>
                  <span>{{ config }}</span>
                </div>
                <i class="el-icon-edit edit-icon"></i>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- DIY页面 -->
        <el-table-column label="DIY页面" width="120" align="center">
          <template slot-scope="scope">
            <el-button 
              type="text" 
              class="page-config-btn"
              @click="pageConfig(scope.row)"
            >
              页面配置
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MarketingConfig',
  data() {
    return {
      loading: false,
      configList: [
        {
          id: 1,
          storeName: '天府火锅(春熙路店)',
          storeAvatar: '',
          materialRepeat: true,
          copyRepeat: false,
          status: true,
          videoPlatforms: ['抖音', '快手', '小红书'],
          imagePlatforms: ['微信', '微博', '抖音', '小红书'],
          otherConfigs: ['音响设备', '投影配置']
        },
        {
          id: 2,
          storeName: '咖啡时光',
          storeAvatar: '',
          materialRepeat: false,
          copyRepeat: true,
          status: true,
          videoPlatforms: ['抖音', '小红书'],
          imagePlatforms: ['微信', '小红书', '美团'],
          otherConfigs: ['背景音乐', 'WiFi热点']
        },
        {
          id: 3,
          storeName: '便民超市',
          storeAvatar: '',
          materialRepeat: true,
          copyRepeat: true,
          status: false,
          videoPlatforms: ['抖音'],
          imagePlatforms: ['微信', '美团'],
          otherConfigs: ['收银系统']
        },
        {
          id: 4,
          storeName: '鲜花工坊',
          storeAvatar: '',
          materialRepeat: false,
          copyRepeat: false,
          status: true,
          videoPlatforms: ['小红书', '快手'],
          imagePlatforms: ['微信', '小红书', '微博'],
          otherConfigs: ['保鲜设备', '配送系统']
        }
      ]
    }
  },
  methods: {
    getStoreAvatarStyle(storeName) {
      const colors = ['#409eff', '#67c23a', '#e6a23c', '#f56c6c'];
      const index = storeName.length % colors.length;
      return {
        background: `linear-gradient(135deg, ${colors[index]}, ${colors[(index + 1) % colors.length]})`
      };
    },
    getPlatformIcon(platform) {
      const iconMap = {
        '抖音': 'el-icon-video-camera',
        '快手': 'el-icon-video-play',
        '小红书': 'el-icon-shopping-bag-1',
        '微信': 'el-icon-chat-dot-round',
        '微博': 'el-icon-chat-line-round',
        '美团': 'el-icon-food'
      };
      return iconMap[platform] || 'el-icon-platform';
    },
    editPlatform(type, platform) {
      this.$message.info(`编辑 ${platform} 平台${type === 'video' ? '视频' : '图片'}配置`);
    },
    editConfig(type, config) {
      this.$message.info(`编辑${config || type}配置`);
    },
    pageConfig(row) {
      this.$message.info(`配置 ${row.storeName} 的DIY页面`);
    }
  }
}
</script>

<style scoped>
.marketing-config-container {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  color: white;
}

.page-title {
  font-size: 28px;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.page-title i {
  margin-right: 10px;
}

.page-description {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.table-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.store-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.store-details {
  flex: 1;
}

.store-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.store-id {
  font-size: 12px;
  color: #999;
}

.toggle-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toggle-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.toggle-label {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.platform-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.platform-item, .config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
}

.platform-item:hover, .config-item:hover {
  border-color: #409eff;
  background: linear-gradient(135deg, #ecf5ff, #f0f9ff);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.video-platform {
  border-left: 3px solid #ff6b6b;
}

.image-platform {
  border-left: 3px solid #4ecdc4;
}

.platform-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.platform-icon {
  color: #409eff;
  font-size: 16px;
}

.edit-icon {
  color: #909399;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s;
}

.platform-item:hover .edit-icon,
.config-item:hover .edit-icon {
  opacity: 1;
}

.config-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.page-config-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  transition: all 0.3s;
}

.page-config-btn:hover {
  background: linear-gradient(135deg, #764ba2, #667eea);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .marketing-config-container {
    padding: 10px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .table-container {
    padding: 15px;
  }
  
  .toggle-group {
    gap: 6px;
  }
  
  .platform-item, .config-item {
    padding: 6px 10px;
  }
}
</style>
