<template>
  <div class="dashboard-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-info">
          <h1>🎉 欢迎回来！</h1>
          <p class="welcome-subtitle">谷菱碰一碰客户端</p>
          <p class="current-time">{{ currentTime }}</p>
        </div>
        <div class="banner-actions">
          <el-button 
            :type="systemVersion.hasUpdate ? 'danger' : 'success'" 
            size="medium" 
            :icon="systemVersion.hasUpdate ? 'el-icon-warning' : 'el-icon-check'"
            @click="checkUpdates">
            {{ systemVersion.hasUpdate ? '有新版本！' : '已是最新版本' }}
          </el-button>
          <el-button type="info" size="medium" icon="el-icon-document" @click="showUpdateLog">
            查看更新日志
          </el-button>
        </div>
      </div>
    </div>
    
    <!-- 核心数据统计 -->
    <div class="stats-overview">
      <h2 class="section-title">📊 核心数据概览</h2>
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stats-card user-stats">
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-share"></i>
              </div>
              <div class="card-info">
                <div class="card-value">{{ userStats.total.toLocaleString() }}</div>
                <div class="card-label">今日预计曝光量</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card merchant-stats">
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-video-camera"></i>
              </div>
              <div class="card-info">
                <div class="card-value">{{ merchantStats.total.toLocaleString() }}</div>
                <div class="card-label">剩余视频数量</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card revenue-stats">
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-picture"></i>
              </div>
              <div class="card-info">
                <div class="card-value">{{ revenueStats.today.toLocaleString() }}</div>
                <div class="card-label">剩余图片数量</div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stats-card growth-stats">
            <div class="card-content">
              <div class="card-icon">
                <i class="el-icon-cpu"></i>
              </div>
              <div class="card-info">
                <div class="card-value">{{ growthStats.rate.toLocaleString() }}</div>
                <div class="card-label">算力剩余量</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 配置流程指引 -->
    <div class="setup-guide">
      <h2 class="section-title">📋 配置流程(小白看这！！每配置一步返回到此页面查看步骤)</h2>
      <div class="progress-container">
        <div class="progress-steps">
          <div 
            v-for="(step, index) in setupSteps" 
            :key="index"
            class="step-item"
            :class="{ 'completed': step.completed, 'optional': step.optional }"
            @click="navigateToStep(step)"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-desc">{{ step.description }}</div>
            </div>
            <div class="step-status">
              <i v-if="step.completed" class="el-icon-check"></i>
              <i v-else-if="step.optional" class="el-icon-star-off"></i>
              <i v-else class="el-icon-right"></i>
            </div>
          </div>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
        </div>
        <div class="progress-text">
          完成进度: {{ completedSteps }}/{{ totalSteps }} ({{ progressPercentage }}%)
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      currentTime: '',
      // 系统版本信息
      systemVersion: {
        current: 'v1.2.4', // 默认版本，在created中更新
        buildDate: '2025-07-14',
        createDate: '2024-09-01',
        hasUpdate: false, // 初始设置为无更新，由检查函数动态设置
        latestVersion: '检测中...', // 从GitHub动态获取
        lastCheckTime: null, // 上次检查时间
        checkingUpdate: false, // 是否正在检查更新
        githubRepo: '66bubufan/guling-touch-customer-system', // GitHub仓库地址
        githubApiUrl: 'https://api.github.com/repos/66bubufan/guling-touch-customer-system/releases/latest', // GitHub API地址
        updateLog: [
          {
            version: 'v1.2.5',
            date: '2025-07-14',
            changes: [
              '🚀 新增一键自动更新功能',
              '� GitHub版本管理集成',
              '🎛️ 智能更新检测系统',
              '📋 详细更新日志展示',
              '⚡ 优化系统性能',
              '� 修复版本检测API问题'
            ],
            githubUrl: 'https://github.com/66bubufan/guling-touch-customer-system/releases/tag/v1.2.5'
          },
          {
            version: 'v1.2.3',
            date: '2025-06-20',
            changes: [
              '🎨 优化了用户界面设计',
              '📱 增强了移动端适配',
              '🔧 修复了若干已知问题'
            ]
          },
          {
            version: 'v1.2.0',
            date: '2025-05-15',
            changes: [
              '🚀 新增商家管理模块',
              '💰 完善财务管理功能',
              '📊 增加数据统计报表'
            ]
          },
          {
            version: 'v1.1.0',
            date: '2025-03-10',
            changes: [
              '👥 新增代理管理功能',
              '🔐 优化登录验证机制',
              '📋 完善系统日志记录'
            ]
          },
          {
            version: 'v1.0.0',
            date: '2024-09-01',
            changes: [
              '🎉 系统正式发布',
              '🏗️ 完成基础架构搭建',
              '👤 实现用户管理功能',
              '🛡️ 建立权限控制体系'
            ]
          }
        ]
      },
      // 今日预计曝光量统计数据
      userStats: {
        total: 15682
      },
      // 剩余视频数量统计数据
      merchantStats: {
        total: 89
      },
      // 剩余图片数量统计数据
      revenueStats: {
        today: 1523
      },
      // 算力剩余量统计数据
      growthStats: {
        rate: 8750
      },
      // 配置流程步骤
      setupSteps: [
        {
          title: 'AI生成文案',
          description: '使用AI智能生成营销文案',
          route: '/storer/shipin',
          completed: false,
          optional: false
        },
        {
          title: '上传并生成视频',
          description: '上传素材并生成营销视频',
          route: '/storer/up',
          completed: false,
          optional: false
        },
        {
          title: '创建话题',
          description: '创建营销话题标签',
          route: '/storer/huati',
          completed: false,
          optional: false
        },
        {
          title: '移入待发布库',
          description: '将图片/视频移入待发布库',
          route: '/storer/sk',
          completed: false,
          optional: false
        },
        {
          title: '配置营销活动',
          description: '设置营销活动策略(可选)',
          route: '/storer/jiang',
          completed: false,
          optional: true
        },
        {
          title: '绑定各平台账号',
          description: '绑定各平台账号进行发布',
          route: '/storer/store',
          completed: false,
          optional: false
        }
      ]
    }
  },
  computed: {
    // 计算完成的步骤数
    completedSteps() {
      return this.setupSteps.filter(step => step.completed).length
    },
    // 计算总步骤数（不包括可选步骤）
    totalSteps() {
      return this.setupSteps.filter(step => !step.optional).length
    },
    // 计算进度百分比
    progressPercentage() {
      if (this.totalSteps === 0) return 0
      return Math.round((this.completedSteps / this.totalSteps) * 100)
    }
  },
  created() {
    this.updateTime()
    // 每分钟更新一次时间
    setInterval(this.updateTime, 60000)
    // 模拟数据刷新
    this.refreshData()
    
    // 初始化版本信息
    this.initVersionInfo()
    
    // 监听系统版本更新事件
    window.addEventListener('systemVersionUpdated', this.handleVersionUpdated)
  },
  
  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('systemVersionUpdated', this.handleVersionUpdated)
  },
  methods: {
    // 导航到配置步骤
    navigateToStep(step) {
      this.$router.push(step.route)
      this.$message.success(`跳转到：${step.title}`)
    },
    
    // 获取当前系统版本
    getCurrentVersion() {
      // 优先从localStorage获取更新后的版本
      const updatedVersion = localStorage.getItem('systemCurrentVersion')
      if (updatedVersion) {
        return updatedVersion
      }
      
      // 尝试从API获取当前版本
      // 如果都没有，返回默认版本
      return 'v1.2.4'
    },
    
    // 设置当前版本（更新完成后调用）
    setCurrentVersion(version) {
      const versionWithPrefix = version.startsWith('v') ? version : `v${version}`
      localStorage.setItem('systemCurrentVersion', versionWithPrefix)
      this.systemVersion.current = versionWithPrefix
      this.checkVersionUpdate() // 重新检查是否有更新
    },
    
    // 检查版本更新状态
    checkVersionUpdate() {
      if (this.systemVersion.latestVersion && this.systemVersion.latestVersion !== '检测中...') {
        const currentVersion = this.systemVersion.current.replace('v', '')
        const latestVersion = this.systemVersion.latestVersion.replace('v', '')
        this.systemVersion.hasUpdate = this.compareVersions(currentVersion, latestVersion) < 0
      }
    },
    
    // 比较版本号
    compareVersions(version1, version2) {
      const v1parts = version1.split('.').map(Number)
      const v2parts = version2.split('.').map(Number)
      
      for (let i = 0; i < Math.max(v1parts.length, v2parts.length); i++) {
        const v1part = v1parts[i] || 0
        const v2part = v2parts[i] || 0
        
        if (v1part < v2part) return -1
        if (v1part > v2part) return 1
      }
      return 0
    },
    
    // 初始化版本信息
    async initVersionInfo() {
      // 更新当前版本
      this.systemVersion.current = this.getCurrentVersion()
      
      // 自动检查GitHub最新版本（延迟2秒，避免页面加载过慢）
      setTimeout(() => {
        this.fetchLatestVersionFromGithub()
      }, 2000)
    },
    
    // 处理版本更新事件
    handleVersionUpdated(event) {
      const newVersion = event.detail.newVersion
      console.log('收到版本更新通知:', newVersion)
      
      // 更新当前版本
      this.setCurrentVersion(newVersion)
      
      // 显示成功消息
      this.$message({
        message: `系统已成功更新到版本 ${newVersion}！`,
        type: 'success',
        duration: 5000
      })
      
      // 如果当前显示的最新版本和新版本一致，则表示已是最新版本
      if (this.systemVersion.latestVersion === newVersion) {
        this.systemVersion.hasUpdate = false
      }
    },
    
    updateTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1
      const date = now.getDate()
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const day = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][now.getDay()]
      this.currentTime = `${year}年${month}月${date}日 ${hours}:${minutes} ${day}`
    },
    
    // 刷新统计数据
    refreshData() {
      // 这里可以调用API获取实时数据
      // 现在使用模拟数据
      setTimeout(() => {
        // 模拟数据变化
        this.userStats.total += Math.floor(Math.random() * 10)
        this.merchantStats.total += Math.floor(Math.random() * 3)
        this.revenueStats.today += Math.floor(Math.random() * 500)
      }, 2000)
    },

    // 初始化更新状态 - 已禁用，使用固定演示数据
    // initUpdateStatus() {
    //   // 随机设置是否有更新（30%概率有更新）
    //   const hasUpdate = Math.random() < 0.3
    //   this.systemVersion.hasUpdate = hasUpdate
    //   
    //   if (!hasUpdate) {
    //     this.systemVersion.latestVersion = this.systemVersion.current
    //   }
    // },
    
    // 检查系统更新
    checkUpdates() {
      // 如果当前已有检测到的更新，直接显示详情
      if (this.systemVersion.hasUpdate) {
        this.showUpdateDetails()
        return
      }
      
      // 防止频繁检查，30秒内只能检查一次
      const now = Date.now()
      if (this.systemVersion.lastCheckTime && (now - this.systemVersion.lastCheckTime) < 30000) {
        this.$message({
          message: '请稍后再试，检查更新间隔不能少于30秒',
          type: 'warning'
        })
        return
      }
      
      // 如果正在检查中，避免重复检查
      if (this.systemVersion.checkingUpdate) {
        this.$message({
          message: '正在检查更新中，请稍候...',
          type: 'info'
        })
        return
      }
      
      this.systemVersion.checkingUpdate = true
      this.$message({
        message: '正在从GitHub检查更新...',
        type: 'info'
      })
      
      // 从GitHub API获取最新版本
      this.fetchLatestVersionFromGithub()
    },

    // 从GitHub API获取最新版本
    async fetchLatestVersionFromGithub() {
      try {
        // 使用更安全的fetch配置
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时
        
        const response = await fetch(this.systemVersion.githubApiUrl, {
          signal: controller.signal,
          headers: {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'GuLing-System-Update-Checker'
          }
        })
        
        clearTimeout(timeoutId)
        
        if (!response.ok) {
          throw new Error(`GitHub API请求失败: ${response.status}`)
        }
        
        const releaseData = await response.json()
        const latestVersion = releaseData.tag_name // GitHub release的tag名称
        const releaseDate = new Date(releaseData.published_at).toISOString().split('T')[0]
        const releaseNotes = releaseData.body || '暂无更新说明'
        
        this.systemVersion.checkingUpdate = false
        this.systemVersion.lastCheckTime = Date.now()
        this.systemVersion.latestVersion = latestVersion
        
        // 检查版本更新状态
        this.checkVersionUpdate()
        
        if (this.systemVersion.hasUpdate) {
          // 解析GitHub release notes
          const changes = this.parseReleaseNotes(releaseNotes)
          
          // 添加新的更新日志
          this.systemVersion.updateLog.unshift({
            version: latestVersion,
            date: releaseDate,
            changes: changes,
            githubUrl: releaseData.html_url // GitHub release页面链接
          })
          
          this.$confirm(`发现新版本 ${latestVersion}，是否查看更新详情？`, 'GitHub更新检测', {
            confirmButtonText: '查看详情',
            cancelButtonText: '稍后再说',
            type: 'success'
          }).then(() => {
            this.showUpdateDetails()
          }).catch(() => {
            // 用户取消，什么都不做
          })
        } else {
          this.$message({
            message: '当前已是最新版本！',
            type: 'success'
          })
        }
      } catch (error) {
        console.error('检查GitHub更新失败:', error)
        this.systemVersion.checkingUpdate = false
        
        // 如果GitHub API请求失败，降级为本地模拟检查
        this.$message({
          message: 'GitHub连接失败，使用本地检查模式',
          type: 'warning'
        })
        
        setTimeout(() => {
          this.fallbackVersionCheck()
        }, 1000)
      }
    },

    // 版本比较函数
    compareVersions(version1, version2) {
      // 移除v前缀并分割版本号
      const v1parts = version1.replace(/^v/, '').split('.').map(Number)
      const v2parts = version2.replace(/^v/, '').split('.').map(Number)
      
      const maxLength = Math.max(v1parts.length, v2parts.length)
      
      for (let i = 0; i < maxLength; i++) {
        const v1part = v1parts[i] || 0
        const v2part = v2parts[i] || 0
        
        if (v1part > v2part) return 1
        if (v1part < v2part) return -1
      }
      return 0
    },

    // 解析GitHub release notes
    parseReleaseNotes(releaseNotes) {
      // 简单的解析逻辑，您可以根据实际的release notes格式调整
      const lines = releaseNotes.split('\n').filter(line => line.trim())
      const changes = []
      
      for (const line of lines) {
        const trimmedLine = line.trim()
        // 识别常见的更新标记
        if (trimmedLine.startsWith('- ') || trimmedLine.startsWith('* ') || 
            trimmedLine.startsWith('+ ') || trimmedLine.match(/^\d+\./)) {
          changes.push(trimmedLine.replace(/^[-*+]\s*|\d+\.\s*/, ''))
        } else if (trimmedLine.length > 0 && changes.length < 8) {
          // 如果没有标记符号但内容有意义，也加入
          changes.push(trimmedLine)
        }
      }
      
      // 如果没有解析到有效内容，使用默认格式
      if (changes.length === 0) {
        changes.push('🆕 系统功能更新')
        changes.push('🐛 修复已知问题')
        changes.push('⚡ 性能优化改进')
      }
      
      // 限制最多显示8条
      return changes.slice(0, 8)
    },

    // 降级版本检查（当GitHub API不可用时）
    fallbackVersionCheck() {
      this.systemVersion.lastCheckTime = Date.now()
      
      // 模拟版本检查逻辑
      const hasUpdate = Math.random() > 0.85 // 15% 概率有更新
      
      if (hasUpdate) {
        this.systemVersion.hasUpdate = true
        this.systemVersion.latestVersion = 'v1.3.1'
        
        this.systemVersion.updateLog.unshift({
          version: 'v1.3.1',
          date: new Date().toISOString().split('T')[0],
          changes: [
            '🔥 紧急修复了安全漏洞',
            '⚡ 大幅提升系统响应速度',
            '🎯 优化了数据查询性能',
            '🛠️ 修复了多个已知问题'
          ]
        })
        
        this.$confirm('发现新版本 v1.3.1，是否查看更新详情？', '本地更新检测', {
          confirmButtonText: '查看详情',
          cancelButtonText: '稍后再说',
          type: 'success'
        }).then(() => {
          this.showUpdateDetails()
        })
      } else {
        this.$message({
          message: '当前已是最新版本！',
          type: 'success'
        })
      }
    },

    // 执行自动更新
    async completeUpdate() {
      // 显示更新进度提示
      const loadingMessage = this.$message({
        message: '🚀 正在执行自动更新，请稍候...',
        type: 'info',
        duration: 0,
        showClose: false
      })

      // 调用后端API执行更新脚本 - 使用配置的代理路径
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 60000) // 60秒超时
      try {
        const response = await fetch('/dev-api/system/update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': localStorage.getItem('token') ? `Bearer ${localStorage.getItem('token')}` : ''
          },
          body: JSON.stringify({}),
          signal: controller.signal
        })
        clearTimeout(timeoutId)
        loadingMessage.close()

        if (response.ok) {
          const result = await response.json()
          if (result.code === 200) {
            // 更新成功
            this.systemVersion.hasUpdate = false
            this.systemVersion.current = this.systemVersion.latestVersion
            this.systemVersion.buildDate = new Date().toISOString().split('T')[0]
            this.systemVersion.lastCheckTime = null
            this.$notify({
              title: '✅ 更新成功',
              message: '系统正在后台更新，请等待构建完成后刷新页面查看效果',
              type: 'success',
              duration: 5000,
              position: 'top-right'
            })
            // 显示更新进度对话框 - 跳转到专用更新页面
            this.$confirm(
              `<div style="text-align: left;">
                <h4 style="color: #67C23A; margin-bottom: 16px;">🚀 准备启动自动更新</h4>
                <p>系统将在专用页面显示详细的更新进度：</p>
                <ul style="margin: 12px 0; padding-left: 20px;">
                  <li>📊 实时进度显示</li>
                  <li>📝 详细更新日志</li>
                  <li>🎯 分阶段进度跟踪</li>
                </ul>
                <p style="margin-top: 16px; color: #E6A23C;">
                  ⏱️ 点击确认后将跳转到更新页面
                </p>
                <p style="margin-top: 12px; font-size: 12px; color: #909399;">
                  💡 更新页面有完整的进度条和状态显示
                </p>
              </div>`,
              '🚀 跳转到Vue更新页面',
              {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '🚀 开始更新',
                cancelButtonText: '取消',
                type: 'info',
                callback: (action) => {
                  if (action === 'confirm') {
                    // 跳转到Vue更新进度页面
                    this.$router.push('/update-progress')
                  }
                }
              }
            )
          } else {
            throw new Error(result.msg || '更新API返回错误')
          }
        } else {
          const errorText = await response.text()
          throw new Error(`HTTP ${response.status}: ${errorText}`)
        }
      } catch (error) {
        clearTimeout(timeoutId)
        loadingMessage.close()
        console.error('更新失败:', error)
        this.$message.closeAll()
        // 显示详细错误信息
        this.$confirm(
          `<div style="text-align: left;">
            <h4 style="color: #F56C6C; margin-bottom: 16px;">❌ 自动更新失败</h4>
            <p><strong>错误信息：</strong></p>
            <p style="background: #f5f5f5; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 8px 0;">
              ${error.message}
            </p>
            <h5 style="margin: 16px 0 8px 0; color: #E6A23C;">🛠️ 解决方案：</h5>
            <ol style="margin: 8px 0; padding-left: 20px; font-size: 14px;">
              <li>请手动双击项目根目录的 <code>update.bat</code> 文件</li>
              <li>或在命令行中执行：<code>git pull origin main</code></li>
              <li>然后重新构建前端：<code>cd ruoyi-ui && npm run build:prod</code></li>
            </ol>
            <p style="margin-top: 16px; font-size: 12px; color: #909399;">
              💡 如果问题持续，请联系技术支持
            </p>
          </div>`,
          '更新失败',
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '我知道了',
            cancelButtonText: '查看日志',
            type: 'error',
            callback: (action) => {
              if (action === 'cancel') {
                console.log('更新详细错误:', error)
                this.$message.info('错误详情已输出到浏览器控制台')
              }
            }
          }
        )
      }
    },

    // 显示更新详情
    showUpdateDetails() {
      const latestUpdate = this.systemVersion.updateLog[0]
      const updateContent = latestUpdate.changes.map(item => 
        `<p style="margin: 8px 0; padding-left: 12px;">${item}</p>`
      ).join('')
      
      const githubLink = latestUpdate.githubUrl ? 
        `<p style="margin-top: 16px;">
          <a href="${latestUpdate.githubUrl}" target="_blank" style="color: #409EFF; text-decoration: none;">
            🔗 在GitHub上查看完整更新说明
          </a>
        </p>` : ''
      
      this.$alert(
        `<div style="text-align: left;">
          <h4 style="margin: 0 0 16px 0; color: #409EFF;">🎉 版本 ${latestUpdate.version} 更新内容：</h4>
          ${updateContent}
          ${githubLink}
          <p style="margin-top: 16px; font-size: 12px; color: #909399;">
            发布时间：${latestUpdate.date}
          </p>
        </div>`, 
        '更新详情', 
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '立即更新',
          showCancelButton: latestUpdate.githubUrl ? true : false,
          cancelButtonText: '查看GitHub',
          callback: (action) => {
            if (action === 'confirm') {
              this.$message.success('正在更新系统...')
              // 模拟更新过程
              setTimeout(() => {
                this.completeUpdate()
              }, 2000)
            } else if (action === 'cancel' && latestUpdate.githubUrl) {
              // 打开GitHub页面
              window.open(latestUpdate.githubUrl, '_blank')
            }
          }
        }
      )
    },

    // 显示更新日志
    showUpdateLog() {
      const logContent = this.systemVersion.updateLog.map(log => {
        const githubLink = log.githubUrl ? 
          `<div style="margin-top: 8px;">
            <a href="${log.githubUrl}" target="_blank" style="color: #409EFF; font-size: 12px; text-decoration: none;">
              🔗 查看GitHub Release
            </a>
          </div>` : ''
        
        return `<div style="margin-bottom: 24px; padding: 16px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #409EFF;">
          <h5 style="margin: 0 0 12px 0; color: #409EFF;">
            📦 ${log.version} 
            <span style="font-size: 12px; color: #909399; font-weight: normal;">(${log.date})</span>
          </h5>
          ${log.changes.map(change => `<p style="margin: 4px 0; font-size: 13px;">${change}</p>`).join('')}
          ${githubLink}
        </div>`
      }).join('')
      
      this.$alert(
        `<div style="text-align: left; max-height: 500px; overflow-y: auto;">
          <h4 style="margin: 0 0 20px 0; color: #409EFF;">📋 系统更新日志</h4>
          <div style="margin-bottom: 16px; padding: 12px; background: #e8f5e8; border-radius: 6px; border-left: 4px solid #67C23A;">
            <strong>🏗️ 系统创建时间：</strong> ${this.systemVersion.createDate}
            <br><strong>📍 当前版本：</strong> ${this.systemVersion.current}
            <br><strong>📦 GitHub仓库：</strong> 
            <a href="https://github.com/${this.systemVersion.githubRepo}" target="_blank" style="color: #409EFF; text-decoration: none;">
              ${this.systemVersion.githubRepo}
            </a>
            <br><strong>🔄 更新状态：</strong> 
            <span style="color: ${this.systemVersion.hasUpdate ? '#E6A23C' : '#67C23A'};">
              ${this.systemVersion.hasUpdate ? '有新版本可用' : '已是最新版本'}
            </span>
          </div>
          ${logContent}
          <div style="margin-top: 16px; font-size: 12px; color: #909399; text-align: center;">
            © 2024-2025 谷菱网络科技有限公司 | 
            <a href="https://github.com/${this.systemVersion.githubRepo}" target="_blank" style="color: #409EFF;">
              GitHub开源项目
            </a>
          </div>
        </div>`, 
        '更新日志', 
        {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确定'
        }
      )
    },

    // 显示版本信息 (保留原有功能)
    showVersionInfo() {
      const versionInfo = `
        <div style="text-align: left;">
          <h4 style="margin: 0 0 16px 0; color: #409EFF;">📋 系统版本信息</h4>
          <div style="margin: 8px 0;">
            <strong>当前版本：</strong> 
            <span style="color: #67C23A;">${this.systemVersion.current}</span>
          </div>
          <div style="margin: 8px 0;">
            <strong>构建日期：</strong> ${this.systemVersion.buildDate}
          </div>
          <div style="margin: 8px 0;">
            <strong>创建时间：</strong> ${this.systemVersion.createDate}
          </div>
          <div style="margin: 8px 0;">
            <strong>系统名称：</strong> 谷菱碰一碰同城获客系统
          </div>
          <div style="margin: 8px 0;">
            <strong>技术栈：</strong> Vue.js + Element UI + Spring Boot
          </div>
          <div style="margin: 8px 0;">
            <strong>更新状态：</strong> 
            <span style="color: ${this.systemVersion.hasUpdate ? '#E6A23C' : '#67C23A'};">
              ${this.systemVersion.hasUpdate ? '有新版本可用' : '已是最新版本'}
            </span>
          </div>
          <div style="margin: 16px 0 8px 0; font-size: 12px; color: #909399;">
            © 2024-2025 谷菱网络科技
          </div>
        </div>
      `
      
      this.$alert(versionInfo, '版本信息', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: calc(100vh - 84px);

  // 通用section标题样式
  .section-title {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  // 欢迎横幅
  .welcome-banner {
    margin-bottom: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 200px;
      height: 200px;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
      border-radius: 50%;
      transform: translate(50%, -50%);
    }

    .banner-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      z-index: 1;

      .welcome-info {
        h1 {
          color: #fff;
          font-size: 32px;
          font-weight: 700;
          margin: 0 0 8px 0;
          text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .welcome-subtitle {
          color: rgba(255,255,255,0.9);
          font-size: 18px;
          margin: 0 0 12px 0;
          font-weight: 500;
        }

        .current-time {
          color: rgba(255,255,255,0.8);
          font-size: 14px;
          margin: 0;
          font-weight: 400;
        }
      }

      .banner-actions {
        .el-button {
          margin-left: 12px;
          border-radius: 8px;
          font-weight: 500;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
          }
        }
      }
    }
  }

  // 统计数据卡片
  .stats-overview {
    margin-bottom: 24px;

    .stats-card {
      background: #fff;
      border-radius: 16px;
      padding: 24px;
      height: 140px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
      border: 1px solid rgba(255,255,255,0.1);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
      }

      &.user-stats::before {
        background: linear-gradient(90deg, #4fc3f7, #29b6f6);
      }

      &.merchant-stats::before {
        background: linear-gradient(90deg, #66bb6a, #43a047);
      }

      &.revenue-stats::before {
        background: linear-gradient(90deg, #ffa726, #ff9800);
      }

      &.growth-stats::before {
        background: linear-gradient(90deg, #ab47bc, #8e24aa);
      }

      .card-content {
        display: flex;
        align-items: center;
        height: 100%;

        .card-icon {
          width: 64px;
          height: 64px;
          border-radius: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          background: linear-gradient(135deg, #667eea, #764ba2);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

          i {
            font-size: 28px;
            color: #fff;
          }
        }

        .card-info {
          flex: 1;

          .card-value {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 4px;
            line-height: 1;
          }

          .card-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 8px;
            font-weight: 500;
          }
        }
      }
    }
  }

  // 配置流程指引
  .setup-guide {
    margin-bottom: 24px;

    .progress-container {
      background: #fff;
      border-radius: 16px;
      padding: 32px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.08);
      border: 1px solid rgba(255,255,255,0.1);

      .progress-steps {
        margin-bottom: 24px;

        .step-item {
          display: flex;
          align-items: center;
          padding: 20px;
          margin-bottom: 16px;
          border-radius: 12px;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border: 2px solid transparent;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;

          &:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);

            .step-number {
              background: #fff;
              color: #667eea;
            }

            .step-title,
            .step-desc {
              color: #fff;
            }

            .step-status i {
              color: #fff;
            }
          }

          &.completed {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            border-color: #27ae60;

            .step-number {
              background: #fff;
              color: #27ae60;
            }

            .step-title,
            .step-desc {
              color: #fff;
            }

            .step-status i {
              color: #fff;
            }
          }

          &.optional {
            opacity: 0.7;
            
            &::after {
              content: '可选';
              position: absolute;
              top: 8px;
              right: 8px;
              background: #f39c12;
              color: #fff;
              padding: 2px 8px;
              border-radius: 4px;
              font-size: 10px;
              font-weight: 600;
            }
          }

          .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 16px;
            margin-right: 20px;
            flex-shrink: 0;
          }

          .step-content {
            flex: 1;

            .step-title {
              font-size: 16px;
              font-weight: 600;
              color: #2c3e50;
              margin-bottom: 4px;
            }

            .step-desc {
              font-size: 14px;
              color: #7f8c8d;
            }
          }

          .step-status {
            margin-left: 16px;
            
            i {
              font-size: 20px;
              color: #bdc3c7;
            }
          }
        }
      }

      .progress-bar {
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 16px;

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #667eea, #764ba2);
          border-radius: 4px;
          transition: width 0.3s ease;
        }
      }

      .progress-text {
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
      }
    }
  }
}

// 动画效果
@keyframes growUp {
  from {
    height: 0;
  }
  to {
    height: var(--height);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;

    .welcome-banner .banner-content {
      flex-direction: column;
      text-align: center;

      .banner-actions {
        margin-top: 20px;
      }
    }
  }
}
</style>
