<template>
  <div class="app-container">
    <div class="page-header">
      <h1 class="page-title">
        <i class="el-icon-money"></i>
        财务概览
      </h1>
      <p class="page-description">查看财务统计和数据分析</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FinanceOverview',
  data() {
    return {
      // 页面数据
    }
  },
  methods: {
    // 页面方法
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 12px;
  color: white;
  margin-bottom: 20px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title i {
  margin-right: 12px;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}
</style>
