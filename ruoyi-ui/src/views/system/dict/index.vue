<template>
  <div class="app-container">
    <div class="page-header">
      <h1 class="page-title">
        <i class="el-icon-s-order"></i>
        字典管理
      </h1>
      <p class="page-description">管理系统字典数据和配置</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dict',
  data() {
    return {
      // 页面数据
    }
  },
  methods: {
    // 页面方法
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.page-header {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
  border-radius: 12px;
  color: #2c3e50;
  margin-bottom: 20px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-title i {
  margin-right: 12px;
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}
</style>
