<template>
  <div class="diy-editor-pro">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button @click="goBack" icon="el-icon-arrow-left" size="small">返回</el-button>
        <span class="page-title">🎨 专业DIY编辑器</span>
        <el-tag type="success" size="mini">Pro版本</el-tag>
      </div>
      <div class="toolbar-right">
        <el-button @click="forceRefresh" icon="el-icon-refresh-right" size="small" type="warning">强制刷新</el-button>
        <el-button @click="resetToDefault" icon="el-icon-refresh" size="small">重置</el-button>
        <el-button @click="previewPage" type="primary" icon="el-icon-view" size="small">预览</el-button>
        <el-button @click="savePage" type="success" icon="el-icon-check" size="small">保存</el-button>
      </div>
    </div>

    <div class="editor-layout">
      <!-- 左侧：编辑面板 -->
      <div class="edit-panel">
        <el-tabs v-model="activeTab" tab-position="top" class="editor-tabs">

          <!-- 页面背景设置 -->
          <el-tab-pane label="🎨 页面背景" name="background">
            <div class="setting-section">
              <h4>背景设置</h4>
              <el-form label-width="100px" size="small">
                <el-form-item label="背景类型">
                  <el-radio-group v-model="config.background.type">
                    <el-radio label="color">纯色背景</el-radio>
                    <el-radio label="gradient">渐变背景</el-radio>
                    <el-radio label="image">图片背景</el-radio>
                  </el-radio-group>
                </el-form-item>

                <!-- 纯色背景 -->
                <el-form-item v-if="config.background.type === 'color'" label="背景颜色">
                  <el-color-picker v-model="config.background.color" show-alpha></el-color-picker>
                </el-form-item>

                <!-- 渐变背景 -->
                <template v-if="config.background.type === 'gradient'">
                  <el-form-item label="渐变方向">
                    <el-select v-model="config.background.gradientDirection">
                      <el-option label="从上到下" value="to bottom"></el-option>
                      <el-option label="从左到右" value="to right"></el-option>
                      <el-option label="对角线↘" value="135deg"></el-option>
                      <el-option label="对角线↙" value="45deg"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="起始颜色">
                    <el-color-picker v-model="config.background.gradientStart" show-alpha></el-color-picker>
                  </el-form-item>
                  <el-form-item label="结束颜色">
                    <el-color-picker v-model="config.background.gradientEnd" show-alpha></el-color-picker>
                  </el-form-item>
                </template>

                <!-- 图片背景 -->
                <template v-if="config.background.type === 'image'">
                  <el-form-item label="背景图片">
                    <div class="image-upload-section">
                      <el-upload
                        class="bg-uploader"
                        action="#"
                        :show-file-list="false"
                        :before-upload="handleBackgroundUpload"
                        accept="image/*"
                      >
                        <img v-if="config.background.image" :src="config.background.image" class="bg-preview">
                        <i v-else class="el-icon-plus bg-uploader-icon"></i>
                      </el-upload>
                      <div class="upload-tips">
                        <p>📐 建议尺寸：375x812px (iPhone比例)</p>
                        <p>📁 支持格式：JPG、PNG、WebP</p>
                        <p>📦 文件大小：≤2MB</p>
                      </div>
                    </div>
                  </el-form-item>
                </template>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- Banner设置 -->
          <el-tab-pane label="🎯 Banner横幅" name="banner">
            <div class="setting-section">
              <div class="section-header">
                <h4>Banner横幅设置</h4>
                <el-switch v-model="config.banner.enabled" active-text="显示" inactive-text="隐藏"></el-switch>
              </div>

              <div v-if="config.banner.enabled">
                <el-form label-width="100px" size="small">
                  <el-form-item label="背景图片">
                    <div class="image-upload-section">
                      <el-upload
                        class="banner-uploader"
                        action="#"
                        :show-file-list="false"
                        :before-upload="handleBannerUpload"
                        accept="image/*"
                      >
                        <img v-if="config.banner.backgroundImage" :src="config.banner.backgroundImage" class="banner-preview">
                        <i v-else class="el-icon-plus banner-uploader-icon"></i>
                      </el-upload>
                      <div class="upload-tips">
                        <p>📐 建议尺寸：375x210px (16:9比例)</p>
                        <p>📁 支持格式：JPG、PNG、WebP</p>
                        <p>📦 文件大小：≤1MB</p>
                      </div>
                    </div>
                  </el-form-item>

                  <el-form-item label="主标题">
                    <el-input v-model="config.banner.title" placeholder="碰一碰领福利" maxlength="20" show-word-limit></el-input>
                  </el-form-item>

                  <el-form-item label="副标题">
                    <el-input v-model="config.banner.subtitle" placeholder="请点击进行操作吧" maxlength="30" show-word-limit></el-input>
                  </el-form-item>

                  <el-form-item label="主标题颜色">
                    <el-color-picker v-model="config.banner.titleColor" show-alpha></el-color-picker>
                  </el-form-item>

                  <el-form-item label="副标题颜色">
                    <el-color-picker v-model="config.banner.subtitleColor" show-alpha></el-color-picker>
                  </el-form-item>

                  <el-form-item label="主标题字体">
                    <el-row :gutter="10">
                      <el-col :span="12">
                        <el-input-number v-model="config.banner.titleFontSize" :min="12" :max="36" controls-position="right"></el-input-number>
                        <span class="unit">px</span>
                      </el-col>
                      <el-col :span="12">
                        <el-select v-model="config.banner.titleFontWeight">
                          <el-option label="正常" value="400"></el-option>
                          <el-option label="中等" value="500"></el-option>
                          <el-option label="粗体" value="700"></el-option>
                          <el-option label="特粗" value="900"></el-option>
                        </el-select>
                      </el-col>
                    </el-row>
                  </el-form-item>

                  <el-form-item label="副标题字体">
                    <el-row :gutter="10">
                      <el-col :span="12">
                        <el-input-number v-model="config.banner.subtitleFontSize" :min="10" :max="24" controls-position="right"></el-input-number>
                        <span class="unit">px</span>
                      </el-col>
                      <el-col :span="12">
                        <el-select v-model="config.banner.subtitleFontWeight">
                          <el-option label="正常" value="400"></el-option>
                          <el-option label="中等" value="500"></el-option>
                          <el-option label="粗体" value="700"></el-option>
                        </el-select>
                      </el-col>
                    </el-row>
                  </el-form-item>

                  <el-form-item label="文字对齐">
                    <el-radio-group v-model="config.banner.textAlign">
                      <el-radio label="left">左对齐</el-radio>
                      <el-radio label="center">居中</el-radio>
                      <el-radio label="right">右对齐</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item label="背景遮罩">
                    <el-slider v-model="config.banner.maskOpacity" :min="0" :max="100" show-input></el-slider>
                    <span class="form-tip">调整遮罩透明度，让文字更清晰</span>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>

          <!-- 产品设置 -->
          <el-tab-pane label="🛍️ 产品展示" name="products">
            <div class="setting-section">
              <div class="section-header">
                <h4>产品展示设置</h4>
                <div class="header-controls">
                  <el-switch v-model="config.products.enabled" active-text="显示" inactive-text="隐藏"></el-switch>
                  <el-button type="primary" size="mini" icon="el-icon-plus" @click="addProduct">添加产品</el-button>
                </div>
              </div>

              <!-- 全局设置 -->
              <div v-if="config.products.enabled" class="global-settings">
                <h5>⚙️ 全局设置</h5>
                <el-form label-width="100px" size="small">
                  <el-form-item label="显示优先级">
                    <el-input-number v-model="config.products.priority" :min="1" :max="10" controls-position="right"></el-input-number>
                    <span class="form-tip">数字越小优先级越高，在页面中显示越靠前</span>
                  </el-form-item>

                  <el-form-item>
                    <div class="priority-controls">
                      <el-button size="mini" icon="el-icon-top" @click="moveSection('products', 'up')">上移</el-button>
                      <el-button size="mini" icon="el-icon-bottom" @click="moveSection('products', 'down')">下移</el-button>
                    </div>
                  </el-form-item>
                </el-form>
              </div>

              <div v-if="config.products.enabled">
                <div v-for="(product, index) in config.products.items" :key="'product-' + index" class="product-item">
                  <div class="product-header">
                    <span>产品 {{ index + 1 }}: {{ product.name }}</span>
                    <el-button type="danger" size="mini" icon="el-icon-delete" @click="removeProduct(index)" circle></el-button>
                  </div>

                  <el-form label-width="100px" size="small">
                    <el-form-item label="产品图片">
                      <div class="image-upload-section">
                        <el-upload
                          class="product-uploader"
                          action="#"
                          :show-file-list="false"
                          :before-upload="(file) => handleProductImageUpload(file, index)"
                          accept="image/*"
                        >
                          <img v-if="product.image" :src="product.image" class="product-preview">
                          <i v-else class="el-icon-plus product-uploader-icon"></i>
                        </el-upload>
                        <div class="upload-tips">
                          <p>📐 建议尺寸：300x300px (正方形)</p>
                          <p>📦 文件大小：≤1MB</p>
                        </div>
                      </div>
                    </el-form-item>

                    <el-form-item label="产品名称">
                      <el-input v-model="product.name" placeholder="请输入产品名称" maxlength="30" show-word-limit></el-input>
                    </el-form-item>

                    <el-form-item label="产品描述">
                      <el-input v-model="product.description" type="textarea" :rows="2" placeholder="请输入产品描述" maxlength="100" show-word-limit></el-input>
                    </el-form-item>

                    <el-form-item label="产品配置">
                      <el-input v-model="product.specifications" type="textarea" :rows="2" placeholder="如：容量500ml，保质期12个月" maxlength="80" show-word-limit></el-input>
                    </el-form-item>

                    <el-form-item label="原价">
                      <el-input v-model="product.originalPrice" placeholder="99.00">
                        <template slot="prepend">￥</template>
                      </el-input>
                    </el-form-item>

                    <el-form-item label="现价">
                      <el-input v-model="product.currentPrice" placeholder="79.00">
                        <template slot="prepend">￥</template>
                      </el-input>
                    </el-form-item>

                    <el-form-item label="折扣信息">
                      <el-input v-model="product.discount" placeholder="如：8折优惠，限时特价" maxlength="20" show-word-limit></el-input>
                    </el-form-item>

                    <el-form-item label="团购链接">
                      <el-input v-model="product.buyUrl" placeholder="https://..." type="url"></el-input>
                    </el-form-item>

                    <el-form-item label="按钮文字">
                      <el-input v-model="product.buttonText" placeholder="立即购买" maxlength="8" show-word-limit></el-input>
                    </el-form-item>

                    <div class="product-tags">
                      <h5>🏷️ 产品标签</h5>
                      <el-form-item label="爆款图标">
                        <el-switch v-model="product.isHot"></el-switch>
                        <span class="form-tip">显示"🔥爆款"标签</span>
                      </el-form-item>

                      <el-form-item label="热卖图标">
                        <el-switch v-model="product.isBestSeller"></el-switch>
                        <span class="form-tip">显示"⭐热卖"标签</span>
                      </el-form-item>

                      <el-form-item label="新品图标">
                        <el-switch v-model="product.isNew"></el-switch>
                        <span class="form-tip">显示"🆕新品"标签</span>
                      </el-form-item>

                      <el-form-item label="限时图标">
                        <el-switch v-model="product.isLimited"></el-switch>
                        <span class="form-tip">显示"⏰限时"标签</span>
                      </el-form-item>

                      <el-form-item label="自定义标签">
                        <el-input v-model="product.customTag" placeholder="如：包邮，满减" maxlength="10" show-word-limit></el-input>
                      </el-form-item>
                    </div>
                  </el-form>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 平台设置 -->
          <el-tab-pane label="📱 平台设置" name="platforms">
            <div class="setting-section">
              <div class="section-header">
                <h4>社交平台设置</h4>
                <el-switch v-model="config.platforms.enabled" active-text="显示" inactive-text="隐藏"></el-switch>
              </div>

              <!-- 全局设置 -->
              <div v-if="config.platforms.enabled" class="global-settings">
                <h5>⚙️ 全局设置</h5>
                <el-form label-width="100px" size="small">
                  <el-form-item label="显示优先级">
                    <el-input-number v-model="config.platforms.priority" :min="1" :max="10" controls-position="right"></el-input-number>
                    <span class="form-tip">数字越小优先级越高，在页面中显示越靠前</span>
                  </el-form-item>

                  <el-form-item label="每行显示数量">
                    <el-input-number v-model="config.platforms.itemsPerRow" :min="1" :max="5" controls-position="right"></el-input-number>
                    <span class="unit">个</span>
                    <span class="form-tip">设置每行显示1-5个平台</span>
                  </el-form-item>

                  <el-form-item>
                    <div class="priority-controls">
                      <el-button size="mini" icon="el-icon-top" @click="moveSection('platforms', 'up')">上移</el-button>
                      <el-button size="mini" icon="el-icon-bottom" @click="moveSection('platforms', 'down')">下移</el-button>
                    </div>
                  </el-form-item>
                </el-form>
              </div>

              <div v-if="config.platforms.enabled">
                <!-- 短视频平台 -->
                <div class="platform-group">
                  <div class="group-header">
                    <h5>📹 短视频平台</h5>
                    <div class="group-controls">
                      <el-switch v-model="config.platforms.videoPlatforms.enabled" active-text="显示" inactive-text="隐藏"></el-switch>
                      <el-button type="primary" size="mini" icon="el-icon-plus" @click="addPlatform('videoPlatforms')">添加平台</el-button>
                    </div>
                  </div>

                  <div v-if="config.platforms.videoPlatforms.enabled" class="platforms-list">
                    <div v-for="(platform, index) in config.platforms.videoPlatforms.items" :key="'video-' + index" class="platform-item">
                      <div class="platform-header">
                        <div class="platform-info">
                          <img v-if="platform.icon" :src="platform.icon" class="platform-icon-small" alt="平台图标">
                          <span class="platform-name">{{ platform.displayName || platform.name }}</span>
                        </div>
                        <div class="platform-controls">
                          <el-switch v-model="platform.enabled" size="mini"></el-switch>
                          <el-button type="text" size="mini" icon="el-icon-edit" @click="editPlatform('videoPlatforms', index)"></el-button>
                          <el-button type="text" size="mini" icon="el-icon-delete" @click="removePlatform('videoPlatforms', index)"></el-button>
                        </div>
                      </div>

                      <div v-if="platform.enabled && platform.editing" class="platform-settings">
                        <el-form label-width="80px" size="small">
                          <el-form-item label="显示名称">
                            <el-input v-model="platform.displayName" placeholder="如：抖音" maxlength="10" show-word-limit></el-input>
                          </el-form-item>

                          <el-form-item label="平台图标">
                            <div class="image-upload-section">
                              <el-upload
                                class="platform-icon-uploader"
                                action="#"
                                :show-file-list="false"
                                :before-upload="(file) => handlePlatformIconUpload(file, 'videoPlatforms', index)"
                                accept="image/*"
                              >
                                <img v-if="platform.icon" :src="platform.icon" class="platform-icon-preview">
                                <i v-else class="el-icon-plus platform-icon-uploader-icon"></i>
                              </el-upload>
                              <div class="upload-tips">
                                <p>📐 建议尺寸：64x64px (正方形)</p>
                                <p>📦 文件大小：≤200KB</p>
                              </div>
                            </div>
                          </el-form-item>

                          <el-form-item label="平台颜色">
                            <el-color-picker v-model="platform.color"></el-color-picker>
                          </el-form-item>

                          <el-form-item label="跳转链接">
                            <el-input v-model="platform.url" placeholder="https://..." type="url"></el-input>
                          </el-form-item>

                          <el-form-item label="描述文字">
                            <el-input v-model="platform.description" placeholder="如：关注我们获取更多优惠" maxlength="30" show-word-limit></el-input>
                          </el-form-item>

                          <el-form-item>
                            <el-button type="success" size="mini" @click="savePlatform('videoPlatforms', index)">保存</el-button>
                            <el-button size="mini" @click="cancelEditPlatform('videoPlatforms', index)">取消</el-button>
                          </el-form-item>
                        </el-form>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 图文/点评平台 -->
                <div class="platform-group">
                  <div class="group-header">
                    <h5>📝 图文/点评平台</h5>
                    <div class="group-controls">
                      <el-switch v-model="config.platforms.reviewPlatforms.enabled" active-text="显示" inactive-text="隐藏"></el-switch>
                      <el-button type="primary" size="mini" icon="el-icon-plus" @click="addPlatform('reviewPlatforms')">添加平台</el-button>
                    </div>
                  </div>

                  <div v-if="config.platforms.reviewPlatforms.enabled" class="platforms-list">
                    <div v-for="(platform, index) in config.platforms.reviewPlatforms.items" :key="'review-' + index" class="platform-item">
                      <div class="platform-header">
                        <div class="platform-info">
                          <img v-if="platform.icon" :src="platform.icon" class="platform-icon-small" alt="平台图标">
                          <span class="platform-name">{{ platform.displayName || platform.name }}</span>
                        </div>
                        <div class="platform-controls">
                          <el-switch v-model="platform.enabled" size="mini"></el-switch>
                          <el-button type="text" size="mini" icon="el-icon-edit" @click="editPlatform('reviewPlatforms', index)"></el-button>
                          <el-button type="text" size="mini" icon="el-icon-delete" @click="removePlatform('reviewPlatforms', index)"></el-button>
                        </div>
                      </div>

                      <div v-if="platform.enabled && platform.editing" class="platform-settings">
                        <el-form label-width="80px" size="small">
                          <el-form-item label="显示名称">
                            <el-input v-model="platform.displayName" placeholder="如：美团" maxlength="10" show-word-limit></el-input>
                          </el-form-item>

                          <el-form-item label="平台图标">
                            <div class="image-upload-section">
                              <el-upload
                                class="platform-icon-uploader"
                                action="#"
                                :show-file-list="false"
                                :before-upload="(file) => handlePlatformIconUpload(file, 'reviewPlatforms', index)"
                                accept="image/*"
                              >
                                <img v-if="platform.icon" :src="platform.icon" class="platform-icon-preview">
                                <i v-else class="el-icon-plus platform-icon-uploader-icon"></i>
                              </el-upload>
                              <div class="upload-tips">
                                <p>📐 建议尺寸：64x64px (正方形)</p>
                                <p>📦 文件大小：≤200KB</p>
                              </div>
                            </div>
                          </el-form-item>

                          <el-form-item label="平台颜色">
                            <el-color-picker v-model="platform.color"></el-color-picker>
                          </el-form-item>

                          <el-form-item label="跳转链接">
                            <el-input v-model="platform.url" placeholder="https://..." type="url"></el-input>
                          </el-form-item>

                          <el-form-item label="描述文字">
                            <el-input v-model="platform.description" placeholder="如：给我们好评吧" maxlength="30" show-word-limit></el-input>
                          </el-form-item>

                          <el-form-item>
                            <el-button type="success" size="mini" @click="savePlatform('reviewPlatforms', index)">保存</el-button>
                            <el-button size="mini" @click="cancelEditPlatform('reviewPlatforms', index)">取消</el-button>
                          </el-form-item>
                        </el-form>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- WiFi设置 -->
          <el-tab-pane label="📶 WiFi设置" name="wifi">
            <div class="setting-section">
              <div class="section-header">
                <h4>WiFi信息设置</h4>
                <el-switch v-model="config.wifi.enabled" active-text="显示" inactive-text="隐藏"></el-switch>
              </div>

              <div v-if="config.wifi.enabled">
                <el-form label-width="100px" size="small">
                  <el-form-item label="WiFi名称">
                    <el-input v-model="config.wifi.ssid" placeholder="如：Starbucks_Free_WiFi" maxlength="32" show-word-limit></el-input>
                  </el-form-item>

                  <el-form-item label="WiFi密码">
                    <el-input v-model="config.wifi.password" placeholder="如：coffee2024" maxlength="32" show-word-limit show-password></el-input>
                  </el-form-item>

                  <el-form-item label="连接按钮文字">
                    <el-input v-model="config.wifi.buttonText" placeholder="一键连接WiFi" maxlength="15" show-word-limit></el-input>
                  </el-form-item>

                  <el-form-item label="提示文字">
                    <el-input v-model="config.wifi.description" type="textarea" :rows="2" placeholder="如：免费WiFi，欢迎使用" maxlength="50" show-word-limit></el-input>
                  </el-form-item>

                  <el-form-item label="显示优先级">
                    <el-input-number v-model="config.wifi.priority" :min="1" :max="10" controls-position="right"></el-input-number>
                    <span class="form-tip">数字越小优先级越高，在页面中显示越靠前</span>
                  </el-form-item>

                  <el-form-item label="背景颜色">
                    <el-color-picker v-model="config.wifi.backgroundColor" show-alpha></el-color-picker>
                  </el-form-item>

                  <el-form-item label="文字颜色">
                    <el-color-picker v-model="config.wifi.textColor"></el-color-picker>
                  </el-form-item>

                  <el-form-item label="按钮颜色">
                    <el-color-picker v-model="config.wifi.buttonColor"></el-color-picker>
                  </el-form-item>

                  <el-form-item label="圆角大小">
                    <el-input-number v-model="config.wifi.borderRadius" :min="0" :max="20" controls-position="right"></el-input-number>
                    <span class="unit">px</span>
                  </el-form-item>

                  <el-form-item label="字体大小">
                    <el-input-number v-model="config.wifi.fontSize" :min="12" :max="18" controls-position="right"></el-input-number>
                    <span class="unit">px</span>
                  </el-form-item>

                  <el-form-item>
                    <div class="priority-controls">
                      <el-button size="mini" icon="el-icon-top" @click="moveSection('wifi', 'up')">上移</el-button>
                      <el-button size="mini" icon="el-icon-bottom" @click="moveSection('wifi', 'down')">下移</el-button>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>

        </el-tabs>
      </div>

      <!-- 右侧：预览面板 -->
      <div class="preview-panel">
        <div class="phone-frame">
          <div class="volume-buttons"></div>
          <div class="phone-screen">
            <div class="preview-content" :style="pageBackgroundStyle">

              <!-- 按优先级排序显示内容 -->
              <div v-for="section in sortedSections" :key="section.name" class="section-wrapper">

                <!-- Banner区域 -->
                <div v-if="section.name === 'banner' && config.banner.enabled" class="preview-banner" :style="bannerStyle">
                  <div class="banner-mask" :style="bannerMaskStyle"></div>
                  <div class="banner-content" :style="bannerContentStyle">
                    <h1 :style="bannerTitleStyle">{{ config.banner.title }}</h1>
                    <p :style="bannerSubtitleStyle">{{ config.banner.subtitle }}</p>
                  </div>
                </div>

                <!-- 产品信息 -->
                <div v-if="section.name === 'products' && config.products.enabled && config.products.items.length > 0"
                     class="preview-products">
                  <div
                    v-for="(product, index) in config.products.items"
                    :key="'product-' + index"
                    v-show="index === currentProductIndex"
                    class="product-slide"
                    @click="buyProduct(product)"
                  >
                    <div class="product-card-horizontal">
                      <!-- 左侧图片区域 -->
                      <div class="product-image-section">
                        <div class="product-image-container">
                          <img v-if="product.image" :src="product.image" class="product-image" alt="产品图片">
                          <div v-else class="product-image-placeholder">
                            <i class="el-icon-picture-outline"></i>
                            <span>产品图片</span>
                          </div>
                          <!-- 产品标签 -->
                          <div class="product-tags">
                            <span v-if="product.isHot" class="product-tag hot">🔥爆款</span>
                            <span v-if="product.isBestSeller" class="product-tag bestseller">⭐热卖</span>
                            <span v-if="product.isNew" class="product-tag new">🆕新品</span>
                            <span v-if="product.isLimited" class="product-tag limited">⏰限时</span>
                            <span v-if="product.customTag" class="product-tag custom">{{ product.customTag }}</span>
                          </div>
                        </div>
                      </div>

                      <!-- 右侧文字信息区域 -->
                      <div class="product-info-section">
                        <div class="product-content">
                          <div class="product-name">{{ product.name }}</div>
                          <div class="product-description">{{ product.description }}</div>
                          <div class="product-specs">{{ product.specifications }}</div>

                          <div class="product-price-section">
                            <div class="product-price">
                              <span class="current-price">￥{{ product.currentPrice }}</span>
                              <span class="original-price">￥{{ product.originalPrice }}</span>
                            </div>
                            <div v-if="product.discount" class="discount-info">{{ product.discount }}</div>
                          </div>

                          <button class="buy-button">{{ product.buttonText || '立即购买' }}</button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 轮播指示器 -->
                  <div v-if="config.products.items.length > 1" class="carousel-indicators">
                    <span
                      v-for="(product, index) in config.products.items"
                      :key="'indicator-' + index"
                      :class="{ active: index === currentProductIndex }"
                      @click="currentProductIndex = index"
                    ></span>
                  </div>
                </div>

                <!-- 平台展示 -->
                <div v-if="section.name === 'platforms' && config.platforms.enabled" class="preview-platforms">
                  <!-- 短视频平台 -->
                  <div v-if="config.platforms.videoPlatforms.enabled && getEnabledPlatforms('videoPlatforms').length > 0" class="platform-section">
                    <h4>📹 短视频平台</h4>
                    <div class="platform-grid" :style="{ gridTemplateColumns: `repeat(${config.platforms.itemsPerRow}, 1fr)` }">
                      <div
                        v-for="(platform, index) in getEnabledPlatforms('videoPlatforms')"
                        :key="'video-platform-' + index"
                        class="platform-item"
                        :style="{ backgroundColor: platform.color }"
                        @click="openPlatformUrl(platform.url)"
                      >
                        <img v-if="platform.icon" :src="platform.icon" class="platform-icon" alt="平台图标">
                        <div class="platform-name">{{ platform.displayName || platform.name }}</div>
                        <div v-if="platform.description" class="platform-description">{{ platform.description }}</div>
                      </div>
                    </div>
                  </div>

                  <!-- 图文/点评平台 -->
                  <div v-if="config.platforms.reviewPlatforms.enabled && getEnabledPlatforms('reviewPlatforms').length > 0" class="platform-section">
                    <h4>📝 图文/点评平台</h4>
                    <div class="platform-grid" :style="{ gridTemplateColumns: `repeat(${config.platforms.itemsPerRow}, 1fr)` }">
                      <div
                        v-for="(platform, index) in getEnabledPlatforms('reviewPlatforms')"
                        :key="'review-platform-' + index"
                        class="platform-item"
                        :style="{ backgroundColor: platform.color }"
                        @click="openPlatformUrl(platform.url)"
                      >
                        <img v-if="platform.icon" :src="platform.icon" class="platform-icon" alt="平台图标">
                        <div class="platform-name">{{ platform.displayName || platform.name }}</div>
                        <div v-if="platform.description" class="platform-description">{{ platform.description }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- WiFi信息 -->
                <div v-if="section.name === 'wifi' && config.wifi.enabled" class="preview-wifi" :style="wifiStyle">
                  <div class="wifi-header">
                    <i class="el-icon-connection wifi-icon"></i>
                    <span class="wifi-title">免费WiFi</span>
                  </div>
                  <div class="wifi-info">
                    <div class="wifi-item">
                      <span class="wifi-label">网络名称：</span>
                      <span class="wifi-value">{{ config.wifi.ssid }}</span>
                    </div>
                    <div class="wifi-item">
                      <span class="wifi-label">连接密码：</span>
                      <span class="wifi-value">{{ config.wifi.password }}</span>
                    </div>
                    <div v-if="config.wifi.description" class="wifi-description">{{ config.wifi.description }}</div>
                  </div>
                  <button class="wifi-button" :style="wifiButtonStyle" @click="connectWifi">
                    {{ config.wifi.buttonText }}
                  </button>
                </div>

              </div>







            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import promotionConfigSync from '@/utils/promotionConfigSync'

export default {
  name: 'PromotionDIYFixed',
  data() {
    return {
      activeTab: 'background',
      currentProductIndex: 0,
      productTimer: null,
      
      // 配置数据
      config: {
        // 页面背景
        background: {
          type: 'gradient', // color, gradient, image
          color: '#ffffff',
          gradientDirection: '135deg',
          gradientStart: '#667eea',
          gradientEnd: '#764ba2',
          image: ''
        },
        
        // Banner设置
        banner: {
          enabled: true,
          backgroundImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=375&h=210&fit=crop',
          title: '碰一碰领福利',
          subtitle: '请点击进行操作吧',
          titleColor: '#ffffff',
          subtitleColor: '#f0f0f0',
          titleFontSize: 24,
          subtitleFontSize: 16,
          titleFontWeight: '700',
          subtitleFontWeight: '400',
          textAlign: 'center',
          maskOpacity: 30
        },
        
        // 产品信息
        products: {
          enabled: true,
          priority: 2,
          items: [
            {
              image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300&h=300&fit=crop',
              name: '经典美式咖啡',
              description: '精选优质咖啡豆，香醇浓郁，回味悠长',
              specifications: '容量：350ml，温度：65-75℃',
              originalPrice: '35.00',
              currentPrice: '28.00',
              discount: '限时8折',
              buyUrl: 'https://example.com/coffee',
              isHot: true,
              isBestSeller: false,
              isNew: false,
              isLimited: true,
              customTag: '包邮',
              buttonText: '立即购买'
            }
          ]
        },

        // 平台设置
        platforms: {
          enabled: true,
          priority: 3,
          itemsPerRow: 2, // 1-5个
          videoPlatforms: {
            enabled: true,
            items: [
              {
                name: 'douyin',
                displayName: '抖音',
                icon: require('@/assets/images/platforms/douyin.png'),
                color: '#FE2974',
                url: 'https://www.douyin.com',
                description: '关注我们获取更多优惠',
                enabled: true,
                editing: false
              },
              {
                name: 'kuaishou',
                displayName: '快手',
                icon: require('@/assets/images/platforms/kuaishou.png'),
                color: '#FF4100',
                url: 'https://www.kuaishou.com',
                description: '精彩短视频等你来看',
                enabled: true,
                editing: false
              },
              {
                name: 'xiaohongshu',
                displayName: '小红书',
                icon: require('@/assets/images/platforms/xiaohongshu.png'),
                color: '#FF2D92',
                url: 'https://www.xiaohongshu.com',
                description: '分享生活的美好',
                enabled: true,
                editing: false
              },
              {
                name: 'pengyouquan_video',
                displayName: '朋友圈视频',
                icon: require('@/assets/images/platforms/pengyouquan.png'),
                color: '#07C160',
                url: 'https://weixin.qq.com',
                description: '分享到朋友圈',
                enabled: true,
                editing: false
              },
              {
                name: 'shipinhao',
                displayName: '视频号',
                icon: require('@/assets/images/platforms/shipinhao.png'),
                color: '#07C160',
                url: 'https://channels.weixin.qq.com',
                description: '微信视频号',
                enabled: true,
                editing: false
              }
            ]
          },
          reviewPlatforms: {
            enabled: true,
            items: [
              {
                name: 'douyin_review',
                displayName: '抖音点评',
                icon: require('@/assets/images/platforms/douyin.png'),
                color: '#FE2974',
                url: 'https://www.douyin.com',
                description: '抖音店铺点评',
                enabled: true,
                editing: false
              },
              {
                name: 'meituan',
                displayName: '美团',
                icon: require('@/assets/images/platforms/meituandian.png'),
                color: '#FFBE00',
                url: 'https://www.meituan.com',
                description: '给我们好评吧',
                enabled: true,
                editing: false
              },
              {
                name: 'dianping',
                displayName: '大众点评',
                icon: require('@/assets/images/platforms/dazhongdian.png'),
                color: '#FF6900',
                url: 'https://www.dianping.com',
                description: '查看更多评价',
                enabled: true,
                editing: false
              },
              {
                name: 'xiaohongshu_text',
                displayName: '小红书图文',
                icon: require('@/assets/images/platforms/xiaohongshu.png'),
                color: '#FF2D92',
                url: 'https://www.xiaohongshu.com',
                description: '小红书图文分享',
                enabled: true,
                editing: false
              },
              {
                name: 'pengyouquan_text',
                displayName: '朋友圈图文',
                icon: require('@/assets/images/platforms/pengyouquan.png'),
                color: '#07C160',
                url: 'https://weixin.qq.com',
                description: '朋友圈图文分享',
                enabled: true,
                editing: false
              },
              {
                name: 'gaode',
                displayName: '高德地图',
                icon: require('@/assets/images/platforms/gaodedian.png'),
                color: '#00A6FB',
                url: 'https://www.amap.com',
                description: '查看位置信息',
                enabled: true,
                editing: false
              },
              {
                name: 'baidu',
                displayName: '百度地图',
                icon: require('@/assets/images/platforms/baidudian.png'),
                color: '#3385FF',
                url: 'https://map.baidu.com',
                description: '导航到店',
                enabled: true,
                editing: false
              }
            ]
          }
        },

        // WiFi设置
        wifi: {
          enabled: true,
          priority: 4,
          ssid: 'Starbucks_Free_WiFi',
          password: 'coffee2024',
          buttonText: '一键连接WiFi',
          description: '免费WiFi，欢迎使用',
          backgroundColor: 'rgba(240,248,255,0.95)',
          textColor: '#333333',
          buttonColor: '#409EFF',
          borderRadius: 12,
          fontSize: 14
        }
      }
    }
  },

  computed: {
    storeId() {
      return this.$route.params.storeId || '1'
    },

    // 页面背景样式
    pageBackgroundStyle() {
      const bg = this.config.background
      let style = {}

      if (bg.type === 'color') {
        style.background = bg.color
      } else if (bg.type === 'gradient') {
        style.background = `linear-gradient(${bg.gradientDirection}, ${bg.gradientStart}, ${bg.gradientEnd})`
      } else if (bg.type === 'image' && bg.image) {
        style.backgroundImage = `url(${bg.image})`
        style.backgroundSize = 'cover'
        style.backgroundPosition = 'center'
        style.backgroundRepeat = 'no-repeat'
      }

      return style
    },

    // Banner样式
    bannerStyle() {
      const banner = this.config.banner
      return {
        height: '210px',
        borderRadius: '12px',
        backgroundImage: banner.backgroundImage ? `url(${banner.backgroundImage})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        position: 'relative',
        overflow: 'hidden',
        margin: '16px'
      }
    },

    bannerMaskStyle() {
      return {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: `rgba(0,0,0,${this.config.banner.maskOpacity / 100})`,
        zIndex: 1
      }
    },

    bannerContentStyle() {
      return {
        position: 'relative',
        zIndex: 2,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: this.config.banner.textAlign === 'center' ? 'center' :
                   this.config.banner.textAlign === 'right' ? 'flex-end' : 'flex-start',
        textAlign: this.config.banner.textAlign,
        padding: '20px'
      }
    },

    bannerTitleStyle() {
      const banner = this.config.banner
      return {
        fontSize: `${banner.titleFontSize}px`,
        fontWeight: banner.titleFontWeight,
        color: banner.titleColor,
        textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
        margin: '0 0 8px 0',
        lineHeight: '1.2'
      }
    },

    bannerSubtitleStyle() {
      const banner = this.config.banner
      return {
        fontSize: `${banner.subtitleFontSize}px`,
        fontWeight: banner.subtitleFontWeight,
        color: banner.subtitleColor,
        textShadow: '1px 1px 2px rgba(0,0,0,0.5)',
        margin: 0,
        lineHeight: '1.4'
      }
    },

    // WiFi样式
    wifiStyle() {
      const wifi = this.config.wifi
      return {
        backgroundColor: wifi.backgroundColor,
        color: wifi.textColor,
        borderRadius: `${wifi.borderRadius}px`,
        fontSize: `${wifi.fontSize}px`,
        margin: '16px',
        padding: '16px'
      }
    },

    wifiButtonStyle() {
      const wifi = this.config.wifi
      return {
        backgroundColor: wifi.buttonColor,
        borderColor: wifi.buttonColor
      }
    },

    // 当前显示的产品
    currentProduct() {
      return this.config.products.items[this.currentProductIndex] || this.config.products.items[0] || {}
    },

    // 按优先级排序的区域
    sortedSections() {
      const sections = []

      if (this.config.banner.enabled) {
        sections.push({ name: 'banner', priority: this.config.banner.priority })
      }

      if (this.config.products.enabled) {
        sections.push({ name: 'products', priority: this.config.products.priority })
      }

      if (this.config.platforms.enabled) {
        sections.push({ name: 'platforms', priority: this.config.platforms.priority })
      }

      if (this.config.wifi.enabled) {
        sections.push({ name: 'wifi', priority: this.config.wifi.priority })
      }

      return sections.sort((a, b) => a.priority - b.priority)
    },

    // 产品卡片样式
    productCardStyle() {
      return {
        backgroundColor: '#ffffff',
        borderRadius: '12px',
        boxShadow: '0 2px 12px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }
    }
  },

  mounted() {
    this.loadConfig()
    this.startProductCarousel()

    // 初始化标签页居中显示
    this.$nextTick(() => {
      this.centerActiveTab()

      // 调试信息：显示平台数量
      console.log('短视频平台数量:', this.config.platforms.videoPlatforms.items.length)
      console.log('图文/点评平台数量:', this.config.platforms.reviewPlatforms.items.length)
      console.log('短视频平台列表:', this.config.platforms.videoPlatforms.items.map(p => p.displayName))
      console.log('图文/点评平台列表:', this.config.platforms.reviewPlatforms.items.map(p => p.displayName))
    })
  },

  beforeDestroy() {
    this.stopProductCarousel()
  },

  methods: {
    // 加载配置
    loadConfig() {
      this.config = promotionConfigSync.loadConfig(this.storeId)
      console.log('DIY页面配置加载完成:', this.config)
    },

    // 保存配置
    savePage() {
      if (promotionConfigSync.saveConfig(this.storeId, this.config)) {
        this.$message.success('页面配置已保存并同步到所有页面')
      } else {
        this.$message.error('保存配置失败')
      }
    },

    // 强制刷新配置
    forceRefresh() {
      this.$message.info('正在强制刷新配置...')

      // 使用配置管理器清除配置
      promotionConfigSync.clearConfig(this.storeId)

      // 强制重新加载页面，绕过缓存
      setTimeout(() => {
        window.location.reload(true)
      }, 500)
    },

    // 重置到默认配置
    resetToDefault() {
      this.$confirm('确定要重置所有配置到默认值吗？这将清除所有自定义设置。', '重置确认', {
        confirmButtonText: '确定重置',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 使用配置管理器清除配置
        promotionConfigSync.clearConfig(this.storeId)

        // 重新加载页面以应用默认配置
        window.location.reload()
      }).catch(() => {
        // 用户取消
      })
    },

    // 预览页面
    previewPage() {
      this.savePage()
      const previewUrl = `/promotion/${this.storeId}`
      window.open(this.$router.resolve({ path: previewUrl }).href, '_blank')
    },

    // 返回
    goBack() {
      this.$router.push('/storer/store')
    },

    // 重置到默认
    resetToDefault() {
      this.$confirm('确定要重置所有设置到默认状态吗？', '重置确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置配置
        location.reload()
      })
    },

    // 购买产品
    buyProduct(product) {
      if (product.buyUrl) {
        window.open(product.buyUrl, '_blank')
      } else {
        this.$message.info('产品购买链接未设置')
      }
    },

    // 添加产品
    addProduct() {
      const newProduct = {
        image: '',
        name: '新产品',
        description: '请输入产品描述',
        specifications: '请输入产品配置信息',
        originalPrice: '99.00',
        currentPrice: '79.00',
        discount: '',
        buyUrl: '',
        isHot: false,
        isBestSeller: false,
        isNew: true,
        isLimited: false,
        customTag: '',
        buttonText: '立即购买'
      }

      this.config.products.items.push(newProduct)
      this.$message.success('产品添加成功')
    },

    // 删除产品
    removeProduct(index) {
      if (this.config.products.items.length <= 1) {
        this.$message.warning('至少需要保留一个产品')
        return
      }

      this.$confirm('确定要删除这个产品吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.config.products.items.splice(index, 1)
        if (this.currentProductIndex >= this.config.products.items.length) {
          this.currentProductIndex = 0
        }
        this.$message.success('产品删除成功')
      })
    },

    // 打开产品链接
    openProductUrl(url) {
      if (url) {
        window.open(url, '_blank')
      } else {
        this.$message.info('请先设置团购链接')
      }
    },

    // 开始产品轮播
    startProductCarousel() {
      this.stopProductCarousel()
      if (this.config.products.items.length > 1) {
        this.productTimer = setInterval(() => {
          this.currentProductIndex = (this.currentProductIndex + 1) % this.config.products.items.length
        }, 3000)
      }
    },

    // 停止产品轮播
    stopProductCarousel() {
      if (this.productTimer) {
        clearInterval(this.productTimer)
        this.productTimer = null
      }
    },

    // 图片上传处理
    handleBackgroundUpload(file) {
      if (file.size > 2 * 1024 * 1024) {
        this.$message.error('图片大小不能超过2MB')
        return false
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        this.config.background.image = e.target.result
      }
      reader.readAsDataURL(file)
      return false
    },

    handleBannerUpload(file) {
      if (file.size > 1024 * 1024) {
        this.$message.error('图片大小不能超过1MB')
        return false
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        this.config.banner.backgroundImage = e.target.result
      }
      reader.readAsDataURL(file)
      return false
    },

    handleProductImageUpload(file, index) {
      if (file.size > 1024 * 1024) {
        this.$message.error('图片大小不能超过1MB')
        return false
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        this.config.products.items[index].image = e.target.result
      }
      reader.readAsDataURL(file)
      return false
    },

    // 获取启用的平台
    getEnabledPlatforms(type) {
      return this.config.platforms[type].items.filter(platform => platform.enabled)
    },

    // 添加平台
    addPlatform(type) {
      const newPlatform = {
        name: 'custom',
        displayName: '自定义平台',
        icon: '',
        color: '#667eea',
        url: '',
        description: '',
        enabled: true,
        editing: true
      }

      this.config.platforms[type].items.push(newPlatform)
      this.$message.success('平台添加成功')
    },

    // 删除平台
    removePlatform(type, index) {
      this.$confirm('确定要删除这个平台吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.config.platforms[type].items.splice(index, 1)
        this.$message.success('平台删除成功')
      })
    },

    // 编辑平台
    editPlatform(type, index) {
      this.config.platforms[type].items[index].editing = true
    },

    // 保存平台
    savePlatform(type, index) {
      this.config.platforms[type].items[index].editing = false
      this.$message.success('平台设置已保存')
    },

    // 取消编辑平台
    cancelEditPlatform(type, index) {
      this.config.platforms[type].items[index].editing = false
    },

    // 打开平台链接
    openPlatformUrl(url) {
      if (url) {
        window.open(url, '_blank')
      } else {
        this.$message.info('该平台暂未设置链接')
      }
    },

    // 连接WiFi
    connectWifi() {
      this.$message.success('WiFi连接功能需要在实际设备上使用')
    },

    // WiFi设置变化处理
    handleWifiToggle(value) {
      console.log('WiFi toggle:', value)
      this.$forceUpdate()
    },

    handleWifiChange() {
      console.log('WiFi config changed')
      this.$forceUpdate()
    },

    // 居中显示当前活动的标签页
    centerActiveTab() {
      try {
        const tabsContainer = this.$el.querySelector('.el-tabs__nav-scroll')
        const activeTab = this.$el.querySelector('.el-tabs__item.is-active')

        if (tabsContainer && activeTab) {
          const containerWidth = tabsContainer.clientWidth
          const tabLeft = activeTab.offsetLeft
          const tabWidth = activeTab.clientWidth

          // 计算需要滚动的距离，让活动标签页居中
          const scrollLeft = tabLeft - (containerWidth - tabWidth) / 2

          // 平滑滚动到目标位置
          tabsContainer.scrollTo({
            left: Math.max(0, scrollLeft),
            behavior: 'smooth'
          })
        }
      } catch (error) {
        console.log('Tab centering error:', error)
      }
    },

    // 移动区域
    moveSection(sectionName, direction) {
      const currentPriority = this.config[sectionName].priority

      if (direction === 'up' && currentPriority > 1) {
        this.config[sectionName].priority = currentPriority - 1
        this.$message.success(`${this.getSectionDisplayName(sectionName)}已上移`)
      } else if (direction === 'down' && currentPriority < 10) {
        this.config[sectionName].priority = currentPriority + 1
        this.$message.success(`${this.getSectionDisplayName(sectionName)}已下移`)
      } else {
        const limitText = direction === 'up' ? '已经在最顶部' : '已经在最底部'
        this.$message.warning(`${this.getSectionDisplayName(sectionName)}${limitText}`)
      }
    },

    // 获取区域显示名称
    getSectionDisplayName(sectionName) {
      const names = {
        products: '产品展示',
        platforms: '平台设置',
        wifi: 'WiFi信息'
      }
      return names[sectionName] || sectionName
    },

    // 处理平台图标上传
    handlePlatformIconUpload(file, type, index) {
      if (file.size > 200 * 1024) {
        this.$message.error('图片大小不能超过200KB')
        return false
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        this.config.platforms[type].items[index].icon = e.target.result
      }
      reader.readAsDataURL(file)
      return false
    },

    // 深度合并对象
    deepMerge(target, source) {
      const result = { ...target }
      for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      }
      return result
    }
  },

  watch: {
    // 监听配置变化，自动保存
    config: {
      handler(newConfig) {
        // 重启产品轮播（如果产品数量或设置发生变化）
        if (this.config.products.enabled && this.config.products.items.length > 1) {
          this.startProductCarousel()
        } else {
          this.stopProductCarousel()
        }
      },
      deep: true
    },

    // 监听标签页切换，自动居中显示
    activeTab: {
      handler(newTab) {
        this.$nextTick(() => {
          this.centerActiveTab()
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.diy-editor-pro {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

// 工具栏样式
.toolbar {
  height: 70px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
  z-index: 100;

  .toolbar-left, .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    z-index: 1;
  }

  .page-title {
    font-size: 20px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .el-button {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .el-tag {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    backdrop-filter: blur(10px);
  }
}

// 编辑器布局
.editor-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// 左侧编辑面板
.edit-panel {
  width: 420px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  overflow-y: auto;
  box-shadow: 4px 0 20px rgba(0,0,0,0.1);

  .editor-tabs {
    height: 100%;

    ::v-deep .el-tabs__header {
      margin: 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 0 16px;
    }

    ::v-deep .el-tabs__nav-wrap {
      padding: 8px 0;

      // 隐藏默认的滑动按钮
      .el-tabs__nav-prev,
      .el-tabs__nav-next {
        display: none !important;
      }
    }

    ::v-deep .el-tabs__nav-scroll {
      overflow: hidden;

      // 自动居中显示活动标签页
      .el-tabs__nav {
        display: flex;
        justify-content: flex-start;
        transition: transform 0.3s ease;
      }
    }

    ::v-deep .el-tabs__item {
      font-weight: 600;
      color: #495057;
      border-radius: 8px 8px 0 0;
      margin-right: 4px;
      transition: all 0.3s ease;
      padding: 0 12px; // 减少padding让标签页更紧凑
      font-size: 13px; // 稍微减小字体

      &.is-active {
        background: white;
        color: #667eea;
        box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
      }

      &:hover {
        color: #667eea;
      }
    }

    ::v-deep .el-tabs__content {
      height: calc(100% - 60px);
      overflow-y: auto;
      padding: 0;
    }
  }
}

// 设置区域
.setting-section {
  padding: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0fe 100%);
    border-radius: 12px;
    border-left: 4px solid #667eea;

    h4 {
      margin: 0;
      color: #2c3e50;
      font-size: 18px;
      font-weight: 700;
    }
  }

  h4 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
  }

  .unit {
    margin-left: 8px;
    color: #6c757d;
    font-size: 12px;
  }

  .form-tip {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
  }
}

// 产品项样式
.product-item {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 16px;
  background: #fafafa;

  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-weight: 600;
    color: #303133;
  }

  .product-tags {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;

    h5 {
      margin: 0 0 16px 0;
      color: #495057;
      font-size: 14px;
      font-weight: 600;
    }
  }
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

// 图片上传样式
.image-upload-section {
  .bg-uploader, .banner-uploader, .product-uploader {
    ::v-deep .el-upload {
      border: 2px dashed #d9d9d9;
      border-radius: 12px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      background: #fafafa;

      &:hover {
        border-color: #667eea;
        background: #f0f4ff;
      }
    }
  }

  .bg-uploader ::v-deep .el-upload {
    width: 200px;
    height: 120px;
  }

  .banner-uploader ::v-deep .el-upload {
    width: 180px;
    height: 100px;
  }

  .product-uploader ::v-deep .el-upload {
    width: 120px;
    height: 120px;
  }

  .bg-preview, .banner-preview, .product-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
  }

  .bg-uploader-icon, .banner-uploader-icon, .product-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .upload-tips {
    margin-top: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;

    p {
      margin: 4px 0;
      font-size: 12px;
      color: #495057;
      line-height: 1.4;

      &:first-child {
        font-weight: 600;
        color: #667eea;
      }
    }
  }
}

// 右侧预览面板
.preview-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

// iPhone框架样式
.phone-frame {
  width: 390px;
  height: 844px;
  background: #000;
  border-radius: 50px;
  padding: 12px;
  box-shadow:
    0 20px 40px rgba(0,0,0,0.3),
    0 0 0 1px rgba(255,255,255,0.1),
    inset 0 0 0 1px rgba(255,255,255,0.1);
  position: relative;
  z-index: 1;

  // 动态岛
  &::before {
    content: '';
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 35px;
    background: #000;
    border-radius: 20px;
    z-index: 10;
  }

  // 音量按钮
  .volume-buttons {
    position: absolute;
    top: 150px;
    left: -2px;
    width: 4px;
    height: 100px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    &::before, &::after {
      content: '';
      width: 4px;
      height: 40px;
      background: #333;
      border-radius: 0 2px 2px 0;
    }
  }
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 38px;
  overflow: hidden;
  position: relative;
}

// 预览内容样式
.preview-content {
  height: 100%;
  overflow-y: auto;
  position: relative;
}

.preview-banner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-products {
  padding: 16px;

  .product-slide {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 16px;

    span {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #ddd;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: #667eea;
        transform: scale(1.2);
      }
    }
  }
}

// 水平布局产品卡片
.product-card-horizontal {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  display: flex;
  height: 210px;

  &:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    transform: translateY(-2px);
  }

  // 左侧图片区域
  .product-image-section {
    width: 65%;
    position: relative;

    .product-image-container {
      width: 100%;
      height: 100%;
      position: relative;

      .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .product-image-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f5f7fa;
        color: #909399;

        i {
          font-size: 32px;
          margin-bottom: 8px;
        }

        span {
          font-size: 12px;
        }
      }

      .product-tags {
        position: absolute;
        top: 8px;
        left: 8px;
        display: flex;
        flex-direction: column;
        gap: 4px;

        .product-tag {
          padding: 2px 6px;
          border-radius: 10px;
          font-size: 9px;
          font-weight: 600;
          color: white;
          white-space: nowrap;

          &.hot {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
          }

          &.bestseller {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
          }

          &.new {
            background: linear-gradient(135deg, #48dbfb, #0abde3);
          }

          &.limited {
            background: linear-gradient(135deg, #ff9ff3, #f368e0);
          }

          &.custom {
            background: linear-gradient(135deg, #667eea, #764ba2);
          }
        }
      }
    }
  }

  // 右侧文字信息区域
  .product-info-section {
    width: 35%;
    padding: 12px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .product-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;

      .product-name {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 6px;
        color: #333;
        line-height: 1.2;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .product-description {
        font-size: 11px;
        margin-bottom: 6px;
        opacity: 0.8;
        color: #666;
        line-height: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .product-specs {
        font-size: 9px;
        margin-bottom: 8px;
        opacity: 0.7;
        color: #999;
        line-height: 1.2;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      .product-price-section {
        margin-bottom: 8px;

        .product-price {
          display: flex;
          align-items: center;
          gap: 6px;
          margin-bottom: 3px;

          .current-price {
            font-size: 16px;
            font-weight: 700;
            color: #ff4757;
          }

          .original-price {
            font-size: 11px;
            text-decoration: line-through;
            opacity: 0.6;
            color: #999;
          }
        }

        .discount-info {
          font-size: 9px;
          background: #ff4757;
          color: white;
          padding: 1px 4px;
          border-radius: 6px;
          font-weight: 600;
          display: inline-block;
        }
      }

      .buy-button {
        width: 100%;
        padding: 8px 12px;
        border: none;
        border-radius: 16px;
        background: #ff4757;
        color: white;
        font-weight: 600;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: auto;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
          background: #ff3742;
        }
      }
    }
  }
}

// 保留原有的垂直布局产品卡片（备用）
.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  }

  .product-image-container {
    position: relative;

    .product-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }

    .product-tags {
      position: absolute;
      top: 8px;
      left: 8px;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .product-tag {
        padding: 2px 6px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 600;
        color: white;

        &.hot {
          background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        &.bestseller {
          background: linear-gradient(135deg, #feca57, #ff9ff3);
        }

        &.new {
          background: linear-gradient(135deg, #48dbfb, #0abde3);
        }

        &.limited {
          background: linear-gradient(135deg, #ff9ff3, #f368e0);
        }

        &.custom {
          background: linear-gradient(135deg, #667eea, #764ba2);
        }
      }
    }
  }

  .product-info {
    padding: 16px;

    .product-name {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }

    .product-description {
      font-size: 12px;
      margin-bottom: 8px;
      opacity: 0.8;
      color: #666;
    }

    .product-specs {
      font-size: 11px;
      margin-bottom: 12px;
      opacity: 0.7;
      color: #999;
    }

    .product-price {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;

      .current-price {
        font-size: 18px;
        font-weight: 700;
        color: #ff4757;
      }

      .original-price {
        font-size: 12px;
        text-decoration: line-through;
        opacity: 0.6;
      }

      .discount-info {
        font-size: 10px;
        background: #ff4757;
        color: white;
        padding: 2px 6px;
        border-radius: 8px;
        font-weight: 600;
      }
    }

    .buy-button {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 8px;
      background: #ff4757;
      color: white;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      }
    }
  }
}

// 平台组样式
.platform-group {
  margin-bottom: 24px;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  overflow: hidden;

  .group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0fe 100%);
    border-bottom: 1px solid #e4e7ed;

    h5 {
      margin: 0;
      color: #2c3e50;
      font-size: 16px;
      font-weight: 600;
    }

    .group-controls {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .platforms-list {
    padding: 16px;
  }
}

.platform-item {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;

  .platform-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;

    .platform-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .platform-icon-small {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        object-fit: cover;
      }

      .platform-name {
        font-weight: 600;
        color: #303133;
      }
    }

    .platform-controls {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  .platform-settings {
    padding: 16px;
    border-top: 1px solid #e4e7ed;
    background: white;
  }
}

.platform-icon-uploader {
  ::v-deep .el-upload {
    width: 64px;
    height: 64px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
    }
  }
}

.platform-icon-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.platform-icon-uploader-icon {
  font-size: 24px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 产品展示样式
.preview-products {
  padding: 16px;
}

// 全局设置样式
.global-settings {
  margin-bottom: 20px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border-radius: 12px;
  border-left: 4px solid #409EFF;

  h5 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
  }

  .priority-controls {
    display: flex;
    gap: 8px;

    .el-button {
      flex: 1;
    }
  }
}

// 预览区域平台样式
.preview-platforms {
  padding: 16px;

  .platform-section {
    margin-bottom: 20px;

    h4 {
      font-size: 16px;
      margin: 0 0 12px 0;
      color: #303133;
      font-weight: 600;
    }

    .platform-grid {
      display: grid;
      gap: 12px;
      // 默认2列，通过内联样式动态设置
    }

    .platform-item {
      padding: 12px;
      background: white;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;
      margin: 0;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      }

      .platform-icon {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        object-fit: cover;
        margin-bottom: 8px;
      }

      .platform-name {
        font-size: 12px;
        font-weight: 600;
        color: white;
        margin-bottom: 4px;
      }

      .platform-description {
        font-size: 10px;
        color: rgba(255,255,255,0.8);
        line-height: 1.2;
      }
    }
  }
}

// WiFi预览样式
.preview-wifi {
  text-align: center;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);

  .wifi-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 12px;

    .wifi-icon {
      font-size: 20px;
      color: #409EFF;
    }

    .wifi-title {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .wifi-info {
    margin-bottom: 16px;

    .wifi-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
      font-size: 14px;

      .wifi-label {
        opacity: 0.8;
      }

      .wifi-value {
        font-weight: 600;
      }
    }

    .wifi-description {
      margin-top: 12px;
      font-size: 12px;
      opacity: 0.8;
      font-style: italic;
    }
  }

  .wifi-button {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }
  }
}

// 表单组样式增强
.form-group {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;

  h5 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
  }
}
</style>
