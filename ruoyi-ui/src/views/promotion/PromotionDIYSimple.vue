<template>
  <div class="diy-editor">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button @click="goBack" icon="el-icon-arrow-left" size="small">返回</el-button>
        <span class="page-title">推广页面DIY编辑器</span>
      </div>
      <div class="toolbar-right">
        <el-button @click="previewPage" type="primary" icon="el-icon-view" size="small">预览</el-button>
        <el-button @click="savePage" type="success" icon="el-icon-check" size="small">保存</el-button>
      </div>
    </div>

    <div class="editor-layout">
      <!-- 左侧：编辑面板 -->
      <div class="edit-panel">
        <el-tabs v-model="activeTab" tab-position="top">
          <!-- Banner设置 -->
          <el-tab-pane label="Banner设置" name="banner">
            <div class="setting-section">
              <h4>Banner文字</h4>
              <el-form label-width="80px">
                <el-form-item label="主标题">
                  <el-input v-model="config.banner.title" placeholder="碰一碰领福利"></el-input>
                </el-form-item>
                <el-form-item label="副标题">
                  <el-input v-model="config.banner.subtitle" placeholder="请点击进行操作吧"></el-input>
                </el-form-item>
                <el-form-item label="标题颜色">
                  <el-color-picker v-model="config.banner.titleColor"></el-color-picker>
                </el-form-item>
                <el-form-item label="副标题颜色">
                  <el-color-picker v-model="config.banner.subtitleColor"></el-color-picker>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- 产品设置 -->
          <el-tab-pane label="产品信息" name="product">
            <div class="setting-section">
              <div class="section-header">
                <h4>产品展示</h4>
                <el-button type="primary" size="mini" icon="el-icon-plus" @click="addProduct">
                  添加产品
                </el-button>
              </div>

              <div v-for="(product, index) in config.products" :key="index" class="product-item">
                <div class="product-header">
                  <span>产品 {{ index + 1 }}: {{ product.name }}</span>
                  <el-button type="danger" size="mini" icon="el-icon-delete" @click="removeProduct(index)" circle></el-button>
                </div>
                
                <el-form label-width="80px">
                  <el-form-item label="产品名称">
                    <el-input v-model="product.name"></el-input>
                  </el-form-item>
                  <el-form-item label="产品描述">
                    <el-input v-model="product.description" type="textarea" :rows="2"></el-input>
                  </el-form-item>
                  <el-form-item label="原价">
                    <el-input v-model="product.originalPrice" placeholder="￥99.00"></el-input>
                  </el-form-item>
                  <el-form-item label="现价">
                    <el-input v-model="product.currentPrice" placeholder="￥79.00"></el-input>
                  </el-form-item>
                  <el-form-item label="团购链接">
                    <el-input v-model="product.groupBuyUrl" placeholder="https://..."></el-input>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>

          <!-- 平台设置 -->
          <el-tab-pane label="平台设置" name="platforms">
            <div class="setting-section">
              <h4>显示设置</h4>
              <el-form label-width="120px">
                <el-form-item label="显示短视频平台">
                  <el-switch v-model="config.showVideoPlatforms"></el-switch>
                </el-form-item>
                <el-form-item label="显示图文平台">
                  <el-switch v-model="config.showReviewPlatforms"></el-switch>
                </el-form-item>
                <el-form-item label="显示WiFi信息">
                  <el-switch v-model="config.showWifi"></el-switch>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 右侧：预览面板 -->
      <div class="preview-panel">
        <div class="phone-frame">
          <div class="phone-screen">
            <div class="preview-content">
              <!-- Banner区域 -->
              <div class="preview-banner">
                <div class="banner-content">
                  <h1 :style="{ color: config.banner.titleColor }">{{ config.banner.title }}</h1>
                  <p :style="{ color: config.banner.subtitleColor }">{{ config.banner.subtitle }}</p>
                </div>
              </div>

              <!-- 产品信息 -->
              <div v-if="config.products.length > 0" class="preview-products">
                <div class="product-carousel">
                  <div 
                    v-for="(product, index) in config.products" 
                    :key="index"
                    v-show="index === currentProductIndex"
                    class="product-slide"
                    @click="openGroupBuy(product.groupBuyUrl)"
                  >
                    <div class="product-info">
                      <div class="product-name">{{ product.name }}</div>
                      <div class="product-description">{{ product.description }}</div>
                      <div class="product-price">
                        <span class="current-price">{{ product.currentPrice }}</span>
                        <span class="original-price">{{ product.originalPrice }}</span>
                      </div>
                      <button class="buy-button">立即购买</button>
                    </div>
                  </div>
                  
                  <!-- 轮播指示器 -->
                  <div v-if="config.products.length > 1" class="carousel-indicators">
                    <span 
                      v-for="(product, index) in config.products" 
                      :key="index"
                      :class="{ active: index === currentProductIndex }"
                      @click="currentProductIndex = index"
                    ></span>
                  </div>
                </div>
              </div>

              <!-- 平台图标 -->
              <div class="preview-platforms">
                <div v-if="config.showVideoPlatforms" class="platform-section">
                  <h4>短视频平台</h4>
                  <div class="platform-grid">
                    <div class="platform-item">抖音</div>
                    <div class="platform-item">快手</div>
                    <div class="platform-item">小红书</div>
                    <div class="platform-item">视频号</div>
                  </div>
                </div>
                
                <div v-if="config.showReviewPlatforms" class="platform-section">
                  <h4>图文/点评平台</h4>
                  <div class="platform-grid">
                    <div class="platform-item">美团</div>
                    <div class="platform-item">大众点评</div>
                    <div class="platform-item">高德</div>
                    <div class="platform-item">百度</div>
                  </div>
                </div>
              </div>

              <!-- WiFi信息 -->
              <div v-if="config.showWifi" class="preview-wifi">
                <div>Wi-Fi: Starbucks_Free_WiFi</div>
                <div>密码: coffee2024</div>
                <button class="wifi-btn">一键连接WiFi</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PromotionDIYSimple',
  data() {
    return {
      activeTab: 'banner',
      currentProductIndex: 0,
      config: {
        banner: {
          title: '碰一碰领福利',
          subtitle: '请点击进行操作吧',
          titleColor: '#ffffff',
          subtitleColor: '#f0f0f0'
        },
        products: [
          {
            name: '经典美式咖啡',
            description: '精选优质咖啡豆，香醇浓郁',
            originalPrice: '￥35.00',
            currentPrice: '￥28.00',
            groupBuyUrl: 'https://example.com/coffee'
          }
        ],
        showVideoPlatforms: true,
        showReviewPlatforms: true,
        showWifi: true
      }
    }
  },
  computed: {
    storeId() {
      return this.$route.params.storeId || '1'
    }
  },
  mounted() {
    this.loadConfig()
    this.startCarousel()
  },
  beforeDestroy() {
    this.stopCarousel()
  },
  methods: {
    loadConfig() {
      const configKey = `promotion_simple_config_${this.storeId}`
      const savedConfig = localStorage.getItem(configKey)
      
      if (savedConfig) {
        try {
          const parsedConfig = JSON.parse(savedConfig)
          this.config = { ...this.config, ...parsedConfig }
        } catch (error) {
          console.error('加载配置失败:', error)
        }
      }
    },

    savePage() {
      const configKey = `promotion_simple_config_${this.storeId}`
      localStorage.setItem(configKey, JSON.stringify(this.config))
      this.$message.success('页面配置已保存')
    },

    previewPage() {
      this.savePage()
      const previewUrl = `/promotion/${this.storeId}`
      window.open(this.$router.resolve({ path: previewUrl }).href, '_blank')
    },

    goBack() {
      this.$router.push('/storer/store')
    },

    addProduct() {
      const newProduct = {
        name: '新产品',
        description: '产品描述',
        originalPrice: '￥99.00',
        currentPrice: '￥79.00',
        groupBuyUrl: ''
      }
      this.config.products.push(newProduct)
      this.$message.success('产品添加成功')
    },

    removeProduct(index) {
      if (this.config.products.length <= 1) {
        this.$message.warning('至少需要保留一个产品')
        return
      }
      
      this.config.products.splice(index, 1)
      if (this.currentProductIndex >= this.config.products.length) {
        this.currentProductIndex = 0
      }
      this.$message.success('产品删除成功')
    },

    startCarousel() {
      this.stopCarousel()
      if (this.config.products.length > 1) {
        this.carouselTimer = setInterval(() => {
          this.currentProductIndex = (this.currentProductIndex + 1) % this.config.products.length
        }, 3000)
      }
    },

    stopCarousel() {
      if (this.carouselTimer) {
        clearInterval(this.carouselTimer)
        this.carouselTimer = null
      }
    },

    openGroupBuy(url) {
      if (url) {
        window.open(url, '_blank')
      } else {
        this.$message.info('请先设置团购链接')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.diy-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.toolbar {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .page-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .toolbar-right {
    display: flex;
    gap: 12px;
  }
}

.editor-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.edit-panel {
  width: 400px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;

  .el-tabs {
    height: 100%;

    ::v-deep .el-tabs__content {
      height: calc(100% - 55px);
      overflow-y: auto;
      padding: 20px;
    }
  }
}

.setting-section {
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }

  h4 {
    margin: 0 0 16px 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }
}

.product-item {
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 16px;
  background: #fafafa;

  .product-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.preview-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.phone-frame {
  width: 390px;
  height: 844px;
  background: #000;
  border-radius: 50px;
  padding: 12px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
  position: relative;
  border: 1px solid #222;

  &::before {
    content: '';
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 35px;
    background: #000;
    border-radius: 20px;
    z-index: 10;
  }
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
}

.preview-content {
  height: 100%;
  overflow-y: auto;
}

.preview-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  aspect-ratio: 16/9;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.3);
  }

  .banner-content {
    position: relative;
    z-index: 1;
    padding: 40px 20px;

    h1 {
      font-size: 24px;
      font-weight: bold;
      margin: 0 0 8px 0;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    }

    p {
      font-size: 16px;
      margin: 0;
      text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }
  }
}

.preview-products {
  padding: 20px;
  background: #f8f9fa;

  .product-carousel {
    position: relative;

    .product-slide {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }

    .product-info {
      text-align: center;

      .product-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
      }

      .product-description {
        font-size: 14px;
        margin-bottom: 12px;
        color: #666;
      }

      .product-price {
        margin-bottom: 16px;

        .current-price {
          font-size: 20px;
          font-weight: bold;
          color: #ff4757;
          margin-right: 8px;
        }

        .original-price {
          font-size: 16px;
          text-decoration: line-through;
          color: #999;
        }
      }

      .buy-button {
        background: #ff4757;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.05);
          background: #ff3742;
        }
      }
    }

    .carousel-indicators {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-top: 16px;

      span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #ddd;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background: #ff4757;
          transform: scale(1.2);
        }
      }
    }
  }
}

.preview-platforms {
  padding: 20px;

  .platform-section {
    margin-bottom: 20px;

    h4 {
      font-size: 16px;
      margin: 0 0 12px 0;
      color: #303133;
    }

    .platform-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .platform-item {
      padding: 12px;
      background: #fff;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      font-weight: 600;
      color: #333;
    }
  }
}

.preview-wifi {
  padding: 20px;
  background: rgba(240,240,240,0.5);
  text-align: center;

  div {
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
  }

  .wifi-btn {
    background: #409eff;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
