<template>
  <div class="diy-editor-test">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button @click="goBack" icon="el-icon-arrow-left" size="small">返回</el-button>
        <span class="page-title">🎨 DIY编辑器测试</span>
      </div>
      <div class="toolbar-right">
        <el-button @click="previewPage" type="primary" icon="el-icon-view" size="small">预览</el-button>
        <el-button @click="savePage" type="success" icon="el-icon-check" size="small">保存</el-button>
      </div>
    </div>

    <div class="editor-layout">
      <!-- 左侧：编辑面板 -->
      <div class="edit-panel">
        <el-tabs v-model="activeTab" tab-position="top">
          
          <!-- Banner设置 -->
          <el-tab-pane label="🎯 Banner横幅" name="banner">
            <div class="setting-section">
              <h4>Banner设置</h4>
              <el-form label-width="100px" size="small">
                <el-form-item label="主标题">
                  <el-input v-model="config.banner.title" placeholder="碰一碰领福利"></el-input>
                </el-form-item>
                <el-form-item label="副标题">
                  <el-input v-model="config.banner.subtitle" placeholder="请点击进行操作吧"></el-input>
                </el-form-item>
                <el-form-item label="标题颜色">
                  <el-color-picker v-model="config.banner.titleColor"></el-color-picker>
                </el-form-item>
                <el-form-item label="副标题颜色">
                  <el-color-picker v-model="config.banner.subtitleColor"></el-color-picker>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- 产品设置 -->
          <el-tab-pane label="🛍️ 产品展示" name="products">
            <div class="setting-section">
              <h4>产品设置</h4>
              <el-button type="primary" size="mini" icon="el-icon-plus" @click="addProduct">添加产品</el-button>
              
              <div v-for="(product, index) in config.products" :key="index" class="product-item">
                <h5>产品 {{ index + 1 }}</h5>
                <el-form label-width="80px" size="small">
                  <el-form-item label="产品名称">
                    <el-input v-model="product.name"></el-input>
                  </el-form-item>
                  <el-form-item label="现价">
                    <el-input v-model="product.currentPrice"></el-input>
                  </el-form-item>
                  <el-form-item label="原价">
                    <el-input v-model="product.originalPrice"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="danger" size="mini" @click="removeProduct(index)">删除</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </el-tab-pane>

        </el-tabs>
      </div>

      <!-- 右侧：预览面板 -->
      <div class="preview-panel">
        <div class="phone-frame">
          <div class="phone-screen">
            <div class="preview-content">
              
              <!-- Banner区域 -->
              <div class="preview-banner">
                <h1 :style="{ color: config.banner.titleColor }">{{ config.banner.title }}</h1>
                <p :style="{ color: config.banner.subtitleColor }">{{ config.banner.subtitle }}</p>
              </div>

              <!-- 产品信息 -->
              <div class="preview-products">
                <div v-for="(product, index) in config.products" :key="index" class="product-card">
                  <div class="product-name">{{ product.name }}</div>
                  <div class="product-price">
                    <span class="current-price">￥{{ product.currentPrice }}</span>
                    <span class="original-price">￥{{ product.originalPrice }}</span>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PromotionDIYTest',
  data() {
    return {
      activeTab: 'banner',
      config: {
        banner: {
          title: '碰一碰领福利',
          subtitle: '请点击进行操作吧',
          titleColor: '#ffffff',
          subtitleColor: '#f0f0f0'
        },
        products: [
          {
            name: '经典美式咖啡',
            currentPrice: '28.00',
            originalPrice: '35.00'
          }
        ]
      }
    }
  },
  computed: {
    storeId() {
      return this.$route.params.storeId || '1'
    }
  },
  mounted() {
    console.log('DIY测试页面加载成功')
    console.log('配置:', this.config)
  },
  methods: {
    goBack() {
      this.$router.push('/storer/store')
    },
    
    previewPage() {
      this.$message.success('预览功能')
    },
    
    savePage() {
      this.$message.success('保存成功')
    },
    
    addProduct() {
      this.config.products.push({
        name: '新产品',
        currentPrice: '99.00',
        originalPrice: '129.00'
      })
      this.$message.success('产品添加成功')
    },
    
    removeProduct(index) {
      if (this.config.products.length > 1) {
        this.config.products.splice(index, 1)
        this.$message.success('产品删除成功')
      } else {
        this.$message.warning('至少需要保留一个产品')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.diy-editor-test {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.toolbar {
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;

  .toolbar-left, .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .page-title {
    font-size: 18px;
    font-weight: 600;
  }
}

.editor-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.edit-panel {
  width: 400px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  overflow-y: auto;

  .setting-section {
    padding: 20px;

    h4 {
      margin: 0 0 16px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
    }
  }

  .product-item {
    margin: 16px 0;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;

    h5 {
      margin: 0 0 12px 0;
      color: #606266;
    }
  }
}

.preview-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.phone-frame {
  width: 375px;
  height: 667px;
  background: #000;
  border-radius: 30px;
  padding: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 20px;
  overflow: hidden;
}

.preview-content {
  height: 100%;
  overflow-y: auto;
}

.preview-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
  text-align: center;
  color: white;

  h1 {
    font-size: 24px;
    margin: 0 0 8px 0;
  }

  p {
    font-size: 16px;
    margin: 0;
  }
}

.preview-products {
  padding: 20px;

  .product-card {
    background: #f8f9fa;
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 8px;

    .product-name {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .product-price {
      .current-price {
        font-size: 18px;
        font-weight: 700;
        color: #ff4757;
        margin-right: 8px;
      }

      .original-price {
        font-size: 14px;
        text-decoration: line-through;
        color: #999;
      }
    }
  }
}
</style>
