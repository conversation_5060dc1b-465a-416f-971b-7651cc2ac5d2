<template>
  <div class="diy-editor-pro">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button @click="goBack" icon="el-icon-arrow-left" size="small">返回</el-button>
        <span class="page-title">🎨 专业DIY编辑器</span>
        <el-tag type="success" size="mini">Pro版本</el-tag>
      </div>
      <div class="toolbar-center">
        <el-button-group>
          <el-button @click="undoChange" :disabled="!canUndo" icon="el-icon-refresh-left" size="small">撤销</el-button>
          <el-button @click="redoChange" :disabled="!canRedo" icon="el-icon-refresh-right" size="small">重做</el-button>
        </el-button-group>
      </div>
      <div class="toolbar-right">
        <el-button @click="resetToDefault" icon="el-icon-refresh" size="small">重置</el-button>
        <el-button @click="previewPage" type="primary" icon="el-icon-view" size="small">预览</el-button>
        <el-button @click="savePage" type="success" icon="el-icon-check" size="small">保存</el-button>
      </div>
    </div>

    <div class="editor-layout">
      <!-- 左侧：编辑面板 -->
      <div class="edit-panel">
        <el-tabs v-model="activeTab" tab-position="top" class="editor-tabs">
          
          <!-- 页面背景设置 -->
          <el-tab-pane label="🎨 页面背景" name="background">
            <div class="setting-section">
              <h4>背景设置</h4>
              <el-form label-width="100px" size="small">
                <el-form-item label="背景类型">
                  <el-radio-group v-model="config.background.type">
                    <el-radio label="color">纯色背景</el-radio>
                    <el-radio label="gradient">渐变背景</el-radio>
                    <el-radio label="image">图片背景</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <!-- 纯色背景 -->
                <el-form-item v-if="config.background.type === 'color'" label="背景颜色">
                  <el-color-picker v-model="config.background.color" show-alpha></el-color-picker>
                </el-form-item>
                
                <!-- 渐变背景 -->
                <template v-if="config.background.type === 'gradient'">
                  <el-form-item label="渐变方向">
                    <el-select v-model="config.background.gradientDirection">
                      <el-option label="从上到下" value="to bottom"></el-option>
                      <el-option label="从左到右" value="to right"></el-option>
                      <el-option label="对角线↘" value="135deg"></el-option>
                      <el-option label="对角线↙" value="45deg"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="起始颜色">
                    <el-color-picker v-model="config.background.gradientStart" show-alpha></el-color-picker>
                  </el-form-item>
                  <el-form-item label="结束颜色">
                    <el-color-picker v-model="config.background.gradientEnd" show-alpha></el-color-picker>
                  </el-form-item>
                </template>
                
                <!-- 图片背景 -->
                <template v-if="config.background.type === 'image'">
                  <el-form-item label="背景图片">
                    <div class="image-upload-section">
                      <el-upload
                        class="bg-uploader"
                        action="#"
                        :show-file-list="false"
                        :before-upload="handleBackgroundUpload"
                        accept="image/*"
                      >
                        <img v-if="config.background.image" :src="config.background.image" class="bg-preview">
                        <i v-else class="el-icon-plus bg-uploader-icon"></i>
                      </el-upload>
                      <div class="upload-tips">
                        <p>📐 建议尺寸：375x812px (iPhone比例)</p>
                        <p>📁 支持格式：JPG、PNG、WebP</p>
                        <p>📦 文件大小：≤2MB</p>
                      </div>
                    </div>
                  </el-form-item>
                  <el-form-item label="图片模式">
                    <el-select v-model="config.background.imageMode">
                      <el-option label="覆盖 (cover)" value="cover"></el-option>
                      <el-option label="包含 (contain)" value="contain"></el-option>
                      <el-option label="拉伸 (stretch)" value="100% 100%"></el-option>
                      <el-option label="重复 (repeat)" value="repeat"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="图片位置">
                    <el-select v-model="config.background.imagePosition">
                      <el-option label="居中" value="center"></el-option>
                      <el-option label="顶部" value="top"></el-option>
                      <el-option label="底部" value="bottom"></el-option>
                      <el-option label="左侧" value="left"></el-option>
                      <el-option label="右侧" value="right"></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-form>
            </div>
          </el-tab-pane>

          <!-- Banner设置 -->
          <el-tab-pane label="🎯 Banner横幅" name="banner">
            <div class="setting-section">
              <div class="section-header">
                <h4>Banner横幅设置</h4>
                <el-switch v-model="config.banner.enabled" active-text="显示" inactive-text="隐藏"></el-switch>
              </div>
              
              <div v-if="config.banner.enabled">
                <!-- Banner背景 -->
                <el-collapse v-model="bannerActiveNames">
                  <el-collapse-item title="🖼️ Banner背景" name="bg">
                    <el-form label-width="100px" size="small">
                      <el-form-item label="背景图片">
                        <div class="image-upload-section">
                          <el-upload
                            class="banner-uploader"
                            action="#"
                            :show-file-list="false"
                            :before-upload="handleBannerUpload"
                            accept="image/*"
                          >
                            <img v-if="config.banner.backgroundImage" :src="config.banner.backgroundImage" class="banner-preview">
                            <i v-else class="el-icon-plus banner-uploader-icon"></i>
                          </el-upload>
                          <div class="upload-tips">
                            <p>📐 建议尺寸：375x210px (16:9比例)</p>
                            <p>📁 支持格式：JPG、PNG、WebP</p>
                            <p>📦 文件大小：≤1MB</p>
                          </div>
                        </div>
                      </el-form-item>
                      <el-form-item label="背景遮罩">
                        <el-slider v-model="config.banner.maskOpacity" :min="0" :max="100" show-input></el-slider>
                        <span class="form-tip">调整遮罩透明度，让文字更清晰</span>
                      </el-form-item>
                    </el-form>
                  </el-collapse-item>
                  
                  <el-collapse-item title="✏️ Banner文字" name="text">
                    <el-form label-width="100px" size="small">
                      <el-form-item label="主标题">
                        <el-input v-model="config.banner.title" placeholder="碰一碰领福利" maxlength="20" show-word-limit></el-input>
                      </el-form-item>
                      <el-form-item label="主标题字体">
                        <el-row :gutter="10">
                          <el-col :span="8">
                            <el-input-number v-model="config.banner.titleFontSize" :min="12" :max="36" controls-position="right"></el-input-number>
                            <span class="unit">px</span>
                          </el-col>
                          <el-col :span="8">
                            <el-select v-model="config.banner.titleFontWeight">
                              <el-option label="细体" value="300"></el-option>
                              <el-option label="正常" value="400"></el-option>
                              <el-option label="中等" value="500"></el-option>
                              <el-option label="粗体" value="700"></el-option>
                              <el-option label="特粗" value="900"></el-option>
                            </el-select>
                          </el-col>
                          <el-col :span="8">
                            <el-color-picker v-model="config.banner.titleColor" show-alpha></el-color-picker>
                          </el-col>
                        </el-row>
                      </el-form-item>
                      
                      <el-form-item label="副标题">
                        <el-input v-model="config.banner.subtitle" placeholder="请点击进行操作吧" maxlength="30" show-word-limit></el-input>
                      </el-form-item>
                      <el-form-item label="副标题字体">
                        <el-row :gutter="10">
                          <el-col :span="8">
                            <el-input-number v-model="config.banner.subtitleFontSize" :min="10" :max="24" controls-position="right"></el-input-number>
                            <span class="unit">px</span>
                          </el-col>
                          <el-col :span="8">
                            <el-select v-model="config.banner.subtitleFontWeight">
                              <el-option label="细体" value="300"></el-option>
                              <el-option label="正常" value="400"></el-option>
                              <el-option label="中等" value="500"></el-option>
                              <el-option label="粗体" value="700"></el-option>
                            </el-select>
                          </el-col>
                          <el-col :span="8">
                            <el-color-picker v-model="config.banner.subtitleColor" show-alpha></el-color-picker>
                          </el-col>
                        </el-row>
                      </el-form-item>
                      
                      <el-form-item label="文字对齐">
                        <el-radio-group v-model="config.banner.textAlign">
                          <el-radio label="left">左对齐</el-radio>
                          <el-radio label="center">居中</el-radio>
                          <el-radio label="right">右对齐</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      
                      <el-form-item label="文字阴影">
                        <el-switch v-model="config.banner.textShadow"></el-switch>
                      </el-form-item>
                    </el-form>
                  </el-collapse-item>
                  
                  <el-collapse-item title="🎨 Banner样式" name="style">
                    <el-form label-width="100px" size="small">
                      <el-form-item label="高度设置">
                        <el-input-number v-model="config.banner.height" :min="150" :max="400" controls-position="right"></el-input-number>
                        <span class="unit">px</span>
                      </el-form-item>
                      <el-form-item label="圆角大小">
                        <el-input-number v-model="config.banner.borderRadius" :min="0" :max="30" controls-position="right"></el-input-number>
                        <span class="unit">px</span>
                      </el-form-item>
                      <el-form-item label="边框设置">
                        <el-row :gutter="10">
                          <el-col :span="8">
                            <el-input-number v-model="config.banner.borderWidth" :min="0" :max="10" controls-position="right"></el-input-number>
                            <span class="unit">px</span>
                          </el-col>
                          <el-col :span="8">
                            <el-select v-model="config.banner.borderStyle">
                              <el-option label="实线" value="solid"></el-option>
                              <el-option label="虚线" value="dashed"></el-option>
                              <el-option label="点线" value="dotted"></el-option>
                            </el-select>
                          </el-col>
                          <el-col :span="8">
                            <el-color-picker v-model="config.banner.borderColor"></el-color-picker>
                          </el-col>
                        </el-row>
                      </el-form-item>
                      <el-form-item label="阴影效果">
                        <el-switch v-model="config.banner.boxShadow"></el-switch>
                      </el-form-item>
                    </el-form>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-tab-pane>

          <!-- 门店信息设置 -->
          <el-tab-pane label="🏪 门店信息" name="store">
            <div class="setting-section">
              <div class="section-header">
                <h4>门店信息设置</h4>
                <el-switch v-model="config.store.enabled" active-text="显示" inactive-text="隐藏"></el-switch>
              </div>
              
              <div v-if="config.store.enabled">
                <el-collapse v-model="storeActiveNames">
                  <el-collapse-item title="🏪 基本信息" name="basic">
                    <el-form label-width="100px" size="small">
                      <el-form-item label="门店Logo">
                        <div class="image-upload-section">
                          <el-upload
                            class="logo-uploader"
                            action="#"
                            :show-file-list="false"
                            :before-upload="handleLogoUpload"
                            accept="image/*"
                          >
                            <img v-if="config.store.logo" :src="config.store.logo" class="logo-preview">
                            <i v-else class="el-icon-plus logo-uploader-icon"></i>
                          </el-upload>
                          <div class="upload-tips">
                            <p>📐 建议尺寸：100x100px (正方形)</p>
                            <p>📁 支持格式：JPG、PNG</p>
                            <p>📦 文件大小：≤500KB</p>
                          </div>
                        </div>
                      </el-form-item>
                      
                      <el-form-item label="门店名称">
                        <el-input v-model="config.store.name" placeholder="请输入门店名称" maxlength="30" show-word-limit></el-input>
                      </el-form-item>
                      
                      <el-form-item label="门店地址">
                        <el-input v-model="config.store.address" placeholder="请输入门店地址" maxlength="50" show-word-limit></el-input>
                      </el-form-item>
                      
                      <el-form-item label="联系电话">
                        <el-input v-model="config.store.phone" placeholder="请输入联系电话" maxlength="20"></el-input>
                      </el-form-item>
                      
                      <el-form-item label="营业时间">
                        <el-input v-model="config.store.businessHours" placeholder="如：09:00-22:00" maxlength="30"></el-input>
                      </el-form-item>
                    </el-form>
                  </el-collapse-item>
                  
                  <el-collapse-item title="🎨 样式设置" name="style">
                    <el-form label-width="100px" size="small">
                      <el-form-item label="名称字体">
                        <el-row :gutter="10">
                          <el-col :span="8">
                            <el-input-number v-model="config.store.nameFontSize" :min="12" :max="24" controls-position="right"></el-input-number>
                            <span class="unit">px</span>
                          </el-col>
                          <el-col :span="8">
                            <el-select v-model="config.store.nameFontWeight">
                              <el-option label="正常" value="400"></el-option>
                              <el-option label="中等" value="500"></el-option>
                              <el-option label="粗体" value="700"></el-option>
                            </el-select>
                          </el-col>
                          <el-col :span="8">
                            <el-color-picker v-model="config.store.nameColor"></el-color-picker>
                          </el-col>
                        </el-row>
                      </el-form-item>
                      
                      <el-form-item label="地址字体">
                        <el-row :gutter="10">
                          <el-col :span="12">
                            <el-input-number v-model="config.store.addressFontSize" :min="10" :max="18" controls-position="right"></el-input-number>
                            <span class="unit">px</span>
                          </el-col>
                          <el-col :span="12">
                            <el-color-picker v-model="config.store.addressColor"></el-color-picker>
                          </el-col>
                        </el-row>
                      </el-form-item>
                      
                      <el-form-item label="背景设置">
                        <el-row :gutter="10">
                          <el-col :span="12">
                            <el-color-picker v-model="config.store.backgroundColor" show-alpha></el-color-picker>
                          </el-col>
                          <el-col :span="12">
                            <el-input-number v-model="config.store.borderRadius" :min="0" :max="20" controls-position="right"></el-input-number>
                            <span class="unit">px</span>
                          </el-col>
                        </el-row>
                      </el-form-item>
                    </el-form>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-tab-pane>

          <!-- 产品信息设置 -->
          <el-tab-pane label="🛍️ 产品展示" name="products">
            <div class="setting-section">
              <div class="section-header">
                <h4>产品展示设置</h4>
                <div class="header-controls">
                  <el-switch v-model="config.products.enabled" active-text="显示" inactive-text="隐藏"></el-switch>
                  <el-button type="primary" size="mini" icon="el-icon-plus" @click="addProduct">添加产品</el-button>
                </div>
              </div>

              <div v-if="config.products.enabled">
                <!-- 全局产品设置 -->
                <el-collapse v-model="productsActiveNames">
                  <el-collapse-item title="⚙️ 全局设置" name="global">
                    <el-form label-width="120px" size="small">
                      <el-form-item label="显示模式">
                        <el-radio-group v-model="config.products.displayMode">
                          <el-radio label="carousel">轮播模式</el-radio>
                          <el-radio label="grid">网格模式</el-radio>
                          <el-radio label="list">列表模式</el-radio>
                        </el-radio-group>
                      </el-form-item>

                      <el-form-item v-if="config.products.displayMode === 'carousel'" label="自动轮播">
                        <el-switch v-model="config.products.autoPlay"></el-switch>
                      </el-form-item>

                      <el-form-item v-if="config.products.autoPlay" label="轮播间隔">
                        <el-input-number v-model="config.products.interval" :min="2" :max="10" controls-position="right"></el-input-number>
                        <span class="unit">秒</span>
                      </el-form-item>

                      <el-form-item v-if="config.products.displayMode === 'grid'" label="每行显示">
                        <el-input-number v-model="config.products.columns" :min="1" :max="3" controls-position="right"></el-input-number>
                        <span class="unit">个</span>
                      </el-form-item>

                      <el-form-item label="背景设置">
                        <el-row :gutter="10">
                          <el-col :span="12">
                            <el-color-picker v-model="config.products.backgroundColor" show-alpha></el-color-picker>
                          </el-col>
                          <el-col :span="12">
                            <el-input-number v-model="config.products.borderRadius" :min="0" :max="20" controls-position="right"></el-input-number>
                            <span class="unit">px</span>
                          </el-col>
                        </el-row>
                      </el-form-item>
                    </el-form>
                  </el-collapse-item>

                  <!-- 单个产品设置 -->
                  <el-collapse-item
                    v-for="(product, index) in config.products.items"
                    :key="'product-' + index"
                    :title="`🛍️ 产品 ${index + 1}: ${product.name}`"
                    :name="'product-' + index"
                  >
                    <div class="product-editor">
                      <div class="product-header">
                        <el-button type="danger" size="mini" icon="el-icon-delete" @click="removeProduct(index)" circle></el-button>
                      </div>

                      <el-form label-width="100px" size="small">
                        <!-- 基本信息 -->
                        <div class="form-group">
                          <h5>📝 基本信息</h5>
                          <el-form-item label="产品图片">
                            <div class="image-upload-section">
                              <el-upload
                                class="product-uploader"
                                action="#"
                                :show-file-list="false"
                                :before-upload="(file) => handleProductImageUpload(file, index)"
                                accept="image/*"
                              >
                                <img v-if="product.image" :src="product.image" class="product-preview">
                                <i v-else class="el-icon-plus product-uploader-icon"></i>
                              </el-upload>
                              <div class="upload-tips">
                                <p>📐 建议尺寸：300x300px (正方形)</p>
                                <p>📁 支持格式：JPG、PNG、WebP</p>
                                <p>📦 文件大小：≤1MB</p>
                              </div>
                            </div>
                          </el-form-item>

                          <el-form-item label="产品名称">
                            <el-input v-model="product.name" placeholder="请输入产品名称" maxlength="30" show-word-limit></el-input>
                          </el-form-item>

                          <el-form-item label="产品描述">
                            <el-input v-model="product.description" type="textarea" :rows="3" placeholder="请输入产品描述" maxlength="100" show-word-limit></el-input>
                          </el-form-item>

                          <el-form-item label="产品配置">
                            <el-input v-model="product.specifications" type="textarea" :rows="2" placeholder="如：容量500ml，保质期12个月" maxlength="80" show-word-limit></el-input>
                          </el-form-item>
                        </div>

                        <!-- 价格设置 -->
                        <div class="form-group">
                          <h5>💰 价格设置</h5>
                          <el-form-item label="原价">
                            <el-input v-model="product.originalPrice" placeholder="99.00">
                              <template slot="prepend">￥</template>
                            </el-input>
                          </el-form-item>

                          <el-form-item label="现价">
                            <el-input v-model="product.currentPrice" placeholder="79.00">
                              <template slot="prepend">￥</template>
                            </el-input>
                          </el-form-item>

                          <el-form-item label="折扣信息">
                            <el-input v-model="product.discount" placeholder="如：8折优惠，限时特价" maxlength="20" show-word-limit></el-input>
                          </el-form-item>

                          <el-form-item label="团购链接">
                            <el-input v-model="product.buyUrl" placeholder="https://..." type="url"></el-input>
                          </el-form-item>
                        </div>

                        <!-- 标签设置 -->
                        <div class="form-group">
                          <h5>🏷️ 产品标签</h5>
                          <el-form-item label="爆款图标">
                            <el-switch v-model="product.isHot"></el-switch>
                            <span class="form-tip">显示"🔥爆款"标签</span>
                          </el-form-item>

                          <el-form-item label="热卖图标">
                            <el-switch v-model="product.isBestSeller"></el-switch>
                            <span class="form-tip">显示"⭐热卖"标签</span>
                          </el-form-item>

                          <el-form-item label="新品图标">
                            <el-switch v-model="product.isNew"></el-switch>
                            <span class="form-tip">显示"🆕新品"标签</span>
                          </el-form-item>

                          <el-form-item label="限时图标">
                            <el-switch v-model="product.isLimited"></el-switch>
                            <span class="form-tip">显示"⏰限时"标签</span>
                          </el-form-item>

                          <el-form-item label="自定义标签">
                            <el-input v-model="product.customTag" placeholder="如：包邮，满减" maxlength="10" show-word-limit></el-input>
                          </el-form-item>
                        </div>

                        <!-- 样式设置 -->
                        <div class="form-group">
                          <h5>🎨 样式设置</h5>
                          <el-form-item label="名称字体">
                            <el-row :gutter="10">
                              <el-col :span="8">
                                <el-input-number v-model="product.nameFontSize" :min="12" :max="20" controls-position="right"></el-input-number>
                                <span class="unit">px</span>
                              </el-col>
                              <el-col :span="8">
                                <el-select v-model="product.nameFontWeight">
                                  <el-option label="正常" value="400"></el-option>
                                  <el-option label="中等" value="500"></el-option>
                                  <el-option label="粗体" value="600"></el-option>
                                  <el-option label="特粗" value="700"></el-option>
                                </el-select>
                              </el-col>
                              <el-col :span="8">
                                <el-color-picker v-model="product.nameColor"></el-color-picker>
                              </el-col>
                            </el-row>
                          </el-form-item>

                          <el-form-item label="价格字体">
                            <el-row :gutter="10">
                              <el-col :span="8">
                                <el-input-number v-model="product.priceFontSize" :min="14" :max="24" controls-position="right"></el-input-number>
                                <span class="unit">px</span>
                              </el-col>
                              <el-col :span="8">
                                <el-select v-model="product.priceFontWeight">
                                  <el-option label="中等" value="500"></el-option>
                                  <el-option label="粗体" value="600"></el-option>
                                  <el-option label="特粗" value="700"></el-option>
                                  <el-option label="超粗" value="800"></el-option>
                                </el-select>
                              </el-col>
                              <el-col :span="8">
                                <el-color-picker v-model="product.priceColor"></el-color-picker>
                              </el-col>
                            </el-row>
                          </el-form-item>

                          <el-form-item label="按钮设置">
                            <el-row :gutter="10">
                              <el-col :span="12">
                                <el-input v-model="product.buttonText" placeholder="立即购买" maxlength="8" show-word-limit></el-input>
                              </el-col>
                              <el-col :span="12">
                                <el-color-picker v-model="product.buttonColor"></el-color-picker>
                              </el-col>
                            </el-row>
                          </el-form-item>
                        </div>
                      </el-form>
                    </div>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-tab-pane>

        </el-tabs>
      </div>

      <!-- 右侧：预览面板 -->
      <div class="preview-panel">
        <div class="phone-frame">
          <div class="phone-screen">
            <div class="preview-content" :style="pageBackgroundStyle">
              
              <!-- Banner区域 -->
              <div v-if="config.banner.enabled" class="preview-banner" :style="bannerStyle">
                <div class="banner-mask" :style="bannerMaskStyle"></div>
                <div class="banner-content" :style="bannerContentStyle">
                  <h1 :style="bannerTitleStyle">{{ config.banner.title }}</h1>
                  <p :style="bannerSubtitleStyle">{{ config.banner.subtitle }}</p>
                </div>
              </div>

              <!-- 门店信息 -->
              <div v-if="config.store.enabled" class="preview-store" :style="storeStyle">
                <img v-if="config.store.logo" :src="config.store.logo" class="store-logo" alt="门店logo">
                <div class="store-info">
                  <div class="store-name" :style="storeNameStyle">{{ config.store.name }}</div>
                  <div class="store-address" :style="storeAddressStyle">{{ config.store.address }}</div>
                  <div class="store-phone" :style="storeAddressStyle">{{ config.store.phone }}</div>
                  <div class="store-hours" :style="storeAddressStyle">{{ config.store.businessHours }}</div>
                </div>
              </div>

              <!-- 产品展示 -->
              <div v-if="config.products.enabled && config.products.items.length > 0" class="preview-products" :style="productsStyle">
                <div v-if="config.products.displayMode === 'carousel'" class="products-carousel">
                  <div
                    v-for="(product, index) in config.products.items"
                    :key="'product-' + index"
                    v-show="index === currentProductIndex"
                    class="product-slide"
                    @click="openProductUrl(product.buyUrl)"
                  >
                    <div class="product-card">
                      <div class="product-image-container">
                        <img v-if="product.image" :src="product.image" class="product-image" alt="产品图片">
                        <div class="product-tags">
                          <span v-if="product.isHot" class="product-tag hot">🔥爆款</span>
                          <span v-if="product.isBestSeller" class="product-tag bestseller">⭐热卖</span>
                          <span v-if="product.isNew" class="product-tag new">🆕新品</span>
                          <span v-if="product.isLimited" class="product-tag limited">⏰限时</span>
                          <span v-if="product.customTag" class="product-tag custom">{{ product.customTag }}</span>
                        </div>
                      </div>
                      <div class="product-info">
                        <div class="product-name" :style="getProductNameStyle(product)">{{ product.name }}</div>
                        <div class="product-description" :style="getProductDescStyle(product)">{{ product.description }}</div>
                        <div class="product-specs" :style="getProductDescStyle(product)">{{ product.specifications }}</div>
                        <div class="product-price">
                          <span class="current-price" :style="getProductPriceStyle(product)">￥{{ product.currentPrice }}</span>
                          <span class="original-price">￥{{ product.originalPrice }}</span>
                          <span v-if="product.discount" class="discount-info">{{ product.discount }}</span>
                        </div>
                        <button class="buy-button" :style="getProductButtonStyle(product)">
                          {{ product.buttonText }}
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- 轮播指示器 -->
                  <div v-if="config.products.items.length > 1" class="carousel-indicators">
                    <span
                      v-for="(product, index) in config.products.items"
                      :key="'indicator-' + index"
                      :class="{ active: index === currentProductIndex }"
                      @click="currentProductIndex = index"
                    ></span>
                  </div>
                </div>

                <!-- 网格模式 -->
                <div v-else-if="config.products.displayMode === 'grid'" class="products-grid" :style="{ gridTemplateColumns: `repeat(${config.products.columns}, 1fr)` }">
                  <div
                    v-for="(product, index) in config.products.items"
                    :key="'grid-product-' + index"
                    class="product-card grid-card"
                    @click="openProductUrl(product.buyUrl)"
                  >
                    <div class="product-image-container">
                      <img v-if="product.image" :src="product.image" class="product-image" alt="产品图片">
                      <div class="product-tags">
                        <span v-if="product.isHot" class="product-tag hot">🔥</span>
                        <span v-if="product.isBestSeller" class="product-tag bestseller">⭐</span>
                        <span v-if="product.isNew" class="product-tag new">🆕</span>
                        <span v-if="product.isLimited" class="product-tag limited">⏰</span>
                      </div>
                    </div>
                    <div class="product-info">
                      <div class="product-name" :style="getProductNameStyle(product)">{{ product.name }}</div>
                      <div class="product-price">
                        <span class="current-price" :style="getProductPriceStyle(product)">￥{{ product.currentPrice }}</span>
                        <span class="original-price">￥{{ product.originalPrice }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 列表模式 -->
                <div v-else class="products-list">
                  <div
                    v-for="(product, index) in config.products.items"
                    :key="'list-product-' + index"
                    class="product-item"
                    @click="openProductUrl(product.buyUrl)"
                  >
                    <img v-if="product.image" :src="product.image" class="product-image-small" alt="产品图片">
                    <div class="product-info">
                      <div class="product-name" :style="getProductNameStyle(product)">{{ product.name }}</div>
                      <div class="product-description" :style="getProductDescStyle(product)">{{ product.description }}</div>
                      <div class="product-price">
                        <span class="current-price" :style="getProductPriceStyle(product)">￥{{ product.currentPrice }}</span>
                        <span class="original-price">￥{{ product.originalPrice }}</span>
                      </div>
                    </div>
                    <div class="product-tags">
                      <span v-if="product.isHot" class="product-tag hot">🔥</span>
                      <span v-if="product.isBestSeller" class="product-tag bestseller">⭐</span>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PromotionDIYPro',
  data() {
    return {
      activeTab: 'background',
      bannerActiveNames: ['bg', 'text', 'style'],
      storeActiveNames: ['basic', 'style'],
      productsActiveNames: ['global'],
      currentProductIndex: 0,
      productTimer: null,
      
      // 历史记录
      history: [],
      historyIndex: -1,
      
      // 配置数据
      config: {
        // 页面背景
        background: {
          type: 'gradient', // color, gradient, image
          color: '#ffffff',
          gradientDirection: '135deg',
          gradientStart: '#667eea',
          gradientEnd: '#764ba2',
          image: '',
          imageMode: 'cover',
          imagePosition: 'center'
        },
        
        // Banner设置
        banner: {
          enabled: true,
          backgroundImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=375&h=210&fit=crop',
          title: '碰一碰领福利',
          subtitle: '请点击进行操作吧',
          titleColor: '#ffffff',
          subtitleColor: '#f0f0f0',
          titleFontSize: 24,
          subtitleFontSize: 16,
          titleFontWeight: '700',
          subtitleFontWeight: '400',
          textAlign: 'center',
          textShadow: true,
          maskOpacity: 30,
          height: 210,
          borderRadius: 12,
          borderWidth: 0,
          borderStyle: 'solid',
          borderColor: '#e4e7ed',
          boxShadow: true
        },
        
        // 门店信息
        store: {
          enabled: true,
          logo: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',
          name: '星巴克咖啡店',
          address: '北京市朝阳区建国门外大街1号',
          phone: '138-0000-0000',
          businessHours: '09:00-22:00',
          nameColor: '#333333',
          addressColor: '#666666',
          nameFontSize: 18,
          addressFontSize: 14,
          nameFontWeight: '600',
          backgroundColor: 'rgba(255,255,255,0.95)',
          borderRadius: 12
        },

        // 产品信息
        products: {
          enabled: true,
          displayMode: 'carousel', // carousel, grid, list
          autoPlay: true,
          interval: 3,
          columns: 2,
          backgroundColor: 'rgba(248,249,250,0.95)',
          borderRadius: 12,
          items: [
            {
              image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300&h=300&fit=crop',
              name: '经典美式咖啡',
              description: '精选优质咖啡豆，香醇浓郁，回味悠长',
              specifications: '容量：350ml，温度：65-75℃',
              originalPrice: '35.00',
              currentPrice: '28.00',
              discount: '限时8折',
              buyUrl: 'https://example.com/coffee',
              isHot: true,
              isBestSeller: false,
              isNew: false,
              isLimited: true,
              customTag: '包邮',
              nameFontSize: 16,
              nameFontWeight: '600',
              nameColor: '#333333',
              descriptionFontSize: 12,
              descriptionColor: '#666666',
              priceFontSize: 18,
              priceFontWeight: '700',
              priceColor: '#ff4757',
              buttonText: '立即购买',
              buttonColor: '#ff4757'
            },
            {
              image: 'https://images.unsplash.com/photo-1544787219-7f47ccb76574?w=300&h=300&fit=crop',
              name: '拿铁咖啡',
              description: '丝滑奶泡与浓郁咖啡的完美结合',
              specifications: '容量：400ml，奶泡厚度：1cm',
              originalPrice: '38.00',
              currentPrice: '30.00',
              discount: '新品特价',
              buyUrl: 'https://example.com/latte',
              isHot: false,
              isBestSeller: true,
              isNew: true,
              isLimited: false,
              customTag: '推荐',
              nameFontSize: 16,
              nameFontWeight: '600',
              nameColor: '#333333',
              descriptionFontSize: 12,
              descriptionColor: '#666666',
              priceFontSize: 18,
              priceFontWeight: '700',
              priceColor: '#ff4757',
              buttonText: '立即购买',
              buttonColor: '#ff4757'
            }
          ]
        }
      }
    }
  },
  computed: {
    storeId() {
      return this.$route.params.storeId || '1'
    },

    canUndo() {
      return this.historyIndex > 0
    },

    canRedo() {
      return this.historyIndex < this.history.length - 1
    },

    // 页面背景样式
    pageBackgroundStyle() {
      const bg = this.config.background
      let style = {}

      if (bg.type === 'color') {
        style.background = bg.color
      } else if (bg.type === 'gradient') {
        style.background = `linear-gradient(${bg.gradientDirection}, ${bg.gradientStart}, ${bg.gradientEnd})`
      } else if (bg.type === 'image' && bg.image) {
        style.backgroundImage = `url(${bg.image})`
        style.backgroundSize = bg.imageMode
        style.backgroundPosition = bg.imagePosition
        style.backgroundRepeat = bg.imageMode === 'repeat' ? 'repeat' : 'no-repeat'
      }

      return style
    },

    // Banner样式
    bannerStyle() {
      const banner = this.config.banner
      return {
        height: `${banner.height}px`,
        borderRadius: `${banner.borderRadius}px`,
        border: banner.borderWidth > 0 ? `${banner.borderWidth}px ${banner.borderStyle} ${banner.borderColor}` : 'none',
        boxShadow: banner.boxShadow ? '0 4px 12px rgba(0,0,0,0.15)' : 'none',
        backgroundImage: banner.backgroundImage ? `url(${banner.backgroundImage})` : 'none',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        position: 'relative',
        overflow: 'hidden'
      }
    },

    bannerMaskStyle() {
      return {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: `rgba(0,0,0,${this.config.banner.maskOpacity / 100})`,
        zIndex: 1
      }
    },

    bannerContentStyle() {
      return {
        position: 'relative',
        zIndex: 2,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: this.config.banner.textAlign === 'center' ? 'center' :
                   this.config.banner.textAlign === 'right' ? 'flex-end' : 'flex-start',
        textAlign: this.config.banner.textAlign,
        padding: '20px'
      }
    },

    bannerTitleStyle() {
      const banner = this.config.banner
      return {
        fontSize: `${banner.titleFontSize}px`,
        fontWeight: banner.titleFontWeight,
        color: banner.titleColor,
        textShadow: banner.textShadow ? '2px 2px 4px rgba(0,0,0,0.5)' : 'none',
        margin: '0 0 8px 0',
        lineHeight: '1.2'
      }
    },

    bannerSubtitleStyle() {
      const banner = this.config.banner
      return {
        fontSize: `${banner.subtitleFontSize}px`,
        fontWeight: banner.subtitleFontWeight,
        color: banner.subtitleColor,
        textShadow: banner.textShadow ? '1px 1px 2px rgba(0,0,0,0.5)' : 'none',
        margin: 0,
        lineHeight: '1.4'
      }
    },

    // 门店样式
    storeStyle() {
      const store = this.config.store
      return {
        backgroundColor: store.backgroundColor,
        borderRadius: `${store.borderRadius}px`,
        padding: '16px',
        margin: '16px',
        display: 'flex',
        alignItems: 'center',
        gap: '12px'
      }
    },

    storeNameStyle() {
      const store = this.config.store
      return {
        fontSize: `${store.nameFontSize}px`,
        fontWeight: store.nameFontWeight,
        color: store.nameColor,
        margin: '0 0 4px 0'
      }
    },

    storeAddressStyle() {
      const store = this.config.store
      return {
        fontSize: `${store.addressFontSize}px`,
        color: store.addressColor,
        margin: '2px 0'
      }
    },

    // 产品样式
    productsStyle() {
      const products = this.config.products
      return {
        backgroundColor: products.backgroundColor,
        borderRadius: `${products.borderRadius}px`,
        margin: '16px',
        padding: '16px'
      }
    }
  },

  mounted() {
    this.loadConfig()
    this.saveToHistory()
    this.startProductCarousel()
  },

  beforeDestroy() {
    this.stopProductCarousel()
  },

  methods: {
    // 产品样式方法
    getProductNameStyle(product) {
      return {
        fontSize: `${product.nameFontSize}px`,
        fontWeight: product.nameFontWeight,
        color: product.nameColor
      }
    },

    getProductDescStyle(product) {
      return {
        fontSize: `${product.descriptionFontSize}px`,
        color: product.descriptionColor
      }
    },

    getProductPriceStyle(product) {
      return {
        fontSize: `${product.priceFontSize}px`,
        fontWeight: product.priceFontWeight,
        color: product.priceColor
      }
    },

    getProductButtonStyle(product) {
      return {
        backgroundColor: product.buttonColor,
        borderColor: product.buttonColor
      }
    },

    // 打开产品链接
    openProductUrl(url) {
      if (url) {
        window.open(url, '_blank')
      } else {
        this.$message.info('请先设置团购链接')
      }
    },

    // 开始产品轮播
    startProductCarousel() {
      this.stopProductCarousel()
      if (this.config.products.autoPlay && this.config.products.items.length > 1) {
        this.productTimer = setInterval(() => {
          this.currentProductIndex = (this.currentProductIndex + 1) % this.config.products.items.length
        }, this.config.products.interval * 1000)
      }
    },

    // 停止产品轮播
    stopProductCarousel() {
      if (this.productTimer) {
        clearInterval(this.productTimer)
        this.productTimer = null
      }
    }
  },

  mounted() {
    this.loadConfig()
    this.saveToHistory()
    this.startProductCarousel()
  },

  beforeDestroy() {
    this.stopProductCarousel()
  },

  methods: {
    // 加载配置
    loadConfig() {
      const configKey = `promotion_diy_pro_${this.storeId}`
      const savedConfig = localStorage.getItem(configKey)

      if (savedConfig) {
        try {
          const parsedConfig = JSON.parse(savedConfig)
          this.config = this.deepMerge(this.config, parsedConfig)
        } catch (error) {
          console.error('加载配置失败:', error)
        }
      }
    },

    // 保存配置
    savePage() {
      const configKey = `promotion_diy_pro_${this.storeId}`
      localStorage.setItem(configKey, JSON.stringify(this.config))
      this.$message.success('页面配置已保存')
    },

    // 预览页面
    previewPage() {
      this.savePage()
      const previewUrl = `/promotion/${this.storeId}`
      window.open(this.$router.resolve({ path: previewUrl }).href, '_blank')
    },

    // 返回
    goBack() {
      this.$router.push('/storer/store')
    },

    // 重置到默认
    resetToDefault() {
      this.$confirm('确定要重置所有设置到默认状态吗？', '重置确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置配置（这里可以设置默认值）
        this.config = {
          // ... 默认配置
        }
        this.saveToHistory()
        this.$message.success('已重置到默认设置')
      })
    },

    // 历史记录管理
    saveToHistory() {
      // 移除当前位置之后的历史记录
      this.history = this.history.slice(0, this.historyIndex + 1)
      // 添加新的历史记录
      this.history.push(JSON.parse(JSON.stringify(this.config)))
      this.historyIndex = this.history.length - 1

      // 限制历史记录数量
      if (this.history.length > 50) {
        this.history.shift()
        this.historyIndex--
      }
    },

    undoChange() {
      if (this.canUndo) {
        this.historyIndex--
        this.config = JSON.parse(JSON.stringify(this.history[this.historyIndex]))
        this.$message.info('已撤销')
      }
    },

    redoChange() {
      if (this.canRedo) {
        this.historyIndex++
        this.config = JSON.parse(JSON.stringify(this.history[this.historyIndex]))
        this.$message.info('已重做')
      }
    },

    // 图片上传处理
    handleBackgroundUpload(file) {
      // 检查文件大小
      if (file.size > 2 * 1024 * 1024) {
        this.$message.error('图片大小不能超过2MB')
        return false
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        this.config.background.image = e.target.result
        this.saveToHistory()
      }
      reader.readAsDataURL(file)
      return false
    },

    handleBannerUpload(file) {
      if (file.size > 1024 * 1024) {
        this.$message.error('图片大小不能超过1MB')
        return false
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        this.config.banner.backgroundImage = e.target.result
        this.saveToHistory()
      }
      reader.readAsDataURL(file)
      return false
    },

    handleLogoUpload(file) {
      if (file.size > 512 * 1024) {
        this.$message.error('图片大小不能超过500KB')
        return false
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        this.config.store.logo = e.target.result
        this.saveToHistory()
      }
      reader.readAsDataURL(file)
      return false
    },

    // 添加产品
    addProduct() {
      const newProduct = {
        image: '',
        name: '新产品',
        description: '请输入产品描述',
        specifications: '请输入产品配置信息',
        originalPrice: '99.00',
        currentPrice: '79.00',
        discount: '',
        buyUrl: '',
        isHot: false,
        isBestSeller: false,
        isNew: true,
        isLimited: false,
        customTag: '',
        nameFontSize: 16,
        nameFontWeight: '600',
        nameColor: '#333333',
        descriptionFontSize: 12,
        descriptionColor: '#666666',
        priceFontSize: 18,
        priceFontWeight: '700',
        priceColor: '#ff4757',
        buttonText: '立即购买',
        buttonColor: '#ff4757'
      }

      this.config.products.items.push(newProduct)
      // 自动展开新添加的产品
      const newIndex = this.config.products.items.length - 1
      this.productsActiveNames.push('product-' + newIndex)
      this.saveToHistory()
      this.$message.success('产品添加成功')
    },

    // 删除产品
    removeProduct(index) {
      if (this.config.products.items.length <= 1) {
        this.$message.warning('至少需要保留一个产品')
        return
      }

      this.$confirm('确定要删除这个产品吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.config.products.items.splice(index, 1)
        // 移除对应的折叠面板
        const panelName = 'product-' + index
        const panelIndex = this.productsActiveNames.indexOf(panelName)
        if (panelIndex > -1) {
          this.productsActiveNames.splice(panelIndex, 1)
        }
        this.saveToHistory()
        this.$message.success('产品删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 处理产品图片上传
    handleProductImageUpload(file, index) {
      if (file.size > 1024 * 1024) {
        this.$message.error('图片大小不能超过1MB')
        return false
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        this.config.products.items[index].image = e.target.result
        this.saveToHistory()
      }
      reader.readAsDataURL(file)
      return false
    },

    // 深度合并对象
    deepMerge(target, source) {
      const result = { ...target }
      for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(result[key] || {}, source[key])
        } else {
          result[key] = source[key]
        }
      }
      return result
    }
  },

  watch: {
    config: {
      handler() {
        // 配置变化时自动保存到历史记录（防抖）
        clearTimeout(this.saveTimer)
        this.saveTimer = setTimeout(() => {
          this.saveToHistory()
        }, 1000)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.diy-editor-pro {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

// 工具栏样式
.toolbar {
  height: 70px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
  z-index: 100;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .toolbar-left, .toolbar-center, .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    z-index: 1;
  }

  .page-title {
    font-size: 20px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  }

  .el-button {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  .el-tag {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    backdrop-filter: blur(10px);
  }
}

// 编辑器布局
.editor-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

// 左侧编辑面板
.edit-panel {
  width: 420px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  overflow-y: auto;
  box-shadow: 4px 0 20px rgba(0,0,0,0.1);

  .editor-tabs {
    height: 100%;

    ::v-deep .el-tabs__header {
      margin: 0;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 0 16px;
    }

    ::v-deep .el-tabs__nav-wrap {
      padding: 8px 0;
    }

    ::v-deep .el-tabs__item {
      font-weight: 600;
      color: #495057;
      border-radius: 8px 8px 0 0;
      margin-right: 4px;
      transition: all 0.3s ease;

      &.is-active {
        background: white;
        color: #667eea;
        box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
      }

      &:hover {
        color: #667eea;
      }
    }

    ::v-deep .el-tabs__content {
      height: calc(100% - 60px);
      overflow-y: auto;
      padding: 0;
    }
  }
}

// 设置区域
.setting-section {
  padding: 20px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0fe 100%);
    border-radius: 12px;
    border-left: 4px solid #667eea;

    h4 {
      margin: 0;
      color: #2c3e50;
      font-size: 18px;
      font-weight: 700;
    }
  }

  h4 {
    margin: 0 0 16px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
  }

  .el-form {
    .el-form-item {
      margin-bottom: 20px;

      .el-form-item__label {
        font-weight: 600;
        color: #495057;
      }
    }
  }

  .unit {
    margin-left: 8px;
    color: #6c757d;
    font-size: 12px;
  }

  .form-tip {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
  }
}

// 折叠面板样式
.el-collapse {
  border: none;

  ::v-deep .el-collapse-item {
    margin-bottom: 12px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    .el-collapse-item__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      font-weight: 600;
      padding: 16px 20px;
      border: none;

      &:hover {
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
      }
    }

    .el-collapse-item__content {
      background: white;
      padding: 20px;
    }
  }
}

// 图片上传样式
.image-upload-section {
  .bg-uploader, .banner-uploader, .logo-uploader {
    ::v-deep .el-upload {
      border: 2px dashed #d9d9d9;
      border-radius: 12px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
      background: #fafafa;

      &:hover {
        border-color: #667eea;
        background: #f0f4ff;
      }
    }
  }

  .bg-uploader ::v-deep .el-upload {
    width: 200px;
    height: 120px;
  }

  .banner-uploader ::v-deep .el-upload {
    width: 180px;
    height: 100px;
  }

  .logo-uploader ::v-deep .el-upload {
    width: 80px;
    height: 80px;
  }

  .bg-preview, .banner-preview, .logo-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
  }

  .bg-uploader-icon, .banner-uploader-icon, .logo-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .upload-tips {
    margin-top: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;

    p {
      margin: 4px 0;
      font-size: 12px;
      color: #495057;
      line-height: 1.4;

      &:first-child {
        font-weight: 600;
        color: #667eea;
      }
    }
  }
}

// 右侧预览面板
.preview-panel {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  }
}

// iPhone框架样式
.phone-frame {
  width: 390px;
  height: 844px;
  background: #000;
  border-radius: 50px;
  padding: 12px;
  box-shadow:
    0 20px 40px rgba(0,0,0,0.3),
    0 0 0 1px rgba(255,255,255,0.1),
    inset 0 0 0 1px rgba(255,255,255,0.1);
  position: relative;
  z-index: 1;

  // 动态岛
  &::before {
    content: '';
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 35px;
    background: #000;
    border-radius: 20px;
    z-index: 10;
  }

  // 侧边按钮
  &::after {
    content: '';
    position: absolute;
    top: 120px;
    right: -2px;
    width: 4px;
    height: 80px;
    background: #333;
    border-radius: 2px 0 0 2px;
  }

  // 音量按钮
  .volume-buttons {
    position: absolute;
    top: 150px;
    left: -2px;
    width: 4px;
    height: 100px;
    display: flex;
    flex-direction: column;
    gap: 20px;

    &::before, &::after {
      content: '';
      width: 4px;
      height: 40px;
      background: #333;
      border-radius: 0 2px 2px 0;
    }
  }
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 38px;
  overflow: hidden;
  position: relative;
}

// 预览内容样式
.preview-content {
  height: 100%;
  overflow-y: auto;
  position: relative;
}

.preview-banner {
  margin: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-store {
  .store-logo {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  }

  .store-info {
    flex: 1;
  }
}

// 产品展示样式
.preview-products {
  .products-carousel {
    position: relative;

    .product-slide {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }
    }

    .carousel-indicators {
      display: flex;
      justify-content: center;
      gap: 8px;
      margin-top: 16px;

      span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #ddd;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background: #667eea;
          transform: scale(1.2);
        }
      }
    }
  }

  .products-grid {
    display: grid;
    gap: 12px;

    .grid-card {
      .product-info {
        padding: 8px;

        .product-name {
          font-size: 14px !important;
          margin-bottom: 4px;
        }
      }
    }
  }

  .products-list {
    .product-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(102, 126, 234, 0.05);
      }

      .product-image-small {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        object-fit: cover;
      }

      .product-info {
        flex: 1;

        .product-name {
          margin-bottom: 4px;
        }

        .product-description {
          margin-bottom: 8px;
          opacity: 0.8;
        }
      }
    }
  }
}

.product-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  }

  .product-image-container {
    position: relative;

    .product-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }

    .product-tags {
      position: absolute;
      top: 8px;
      left: 8px;
      display: flex;
      flex-direction: column;
      gap: 4px;

      .product-tag {
        padding: 2px 6px;
        border-radius: 12px;
        font-size: 10px;
        font-weight: 600;
        color: white;

        &.hot {
          background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        }

        &.bestseller {
          background: linear-gradient(135deg, #feca57, #ff9ff3);
        }

        &.new {
          background: linear-gradient(135deg, #48dbfb, #0abde3);
        }

        &.limited {
          background: linear-gradient(135deg, #ff9ff3, #f368e0);
        }

        &.custom {
          background: linear-gradient(135deg, #667eea, #764ba2);
        }
      }
    }
  }

  .product-info {
    padding: 16px;

    .product-name {
      margin-bottom: 8px;
      font-weight: 600;
      line-height: 1.2;
    }

    .product-description {
      margin-bottom: 8px;
      opacity: 0.8;
      line-height: 1.4;
    }

    .product-specs {
      margin-bottom: 12px;
      font-size: 11px;
      opacity: 0.7;
      line-height: 1.3;
    }

    .product-price {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;

      .current-price {
        font-weight: 700;
      }

      .original-price {
        font-size: 12px;
        text-decoration: line-through;
        opacity: 0.6;
      }

      .discount-info {
        font-size: 10px;
        background: #ff4757;
        color: white;
        padding: 2px 6px;
        border-radius: 8px;
        font-weight: 600;
      }
    }

    .buy-button {
      width: 100%;
      padding: 12px;
      border: none;
      border-radius: 8px;
      color: white;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      }
    }
  }
}

// 表单组样式
.form-group {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;

  h5 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
  }
}

.product-editor {
  .product-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;
  }
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-uploader {
  ::v-deep .el-upload {
    width: 120px;
    height: 120px;
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:hover {
      border-color: #667eea;
    }
  }
}

.product-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 响应式设计
@media (max-width: 1200px) {
  .edit-panel {
    width: 380px;
  }

  .phone-frame {
    width: 350px;
    height: 758px;
  }
}

@media (max-width: 768px) {
  .editor-layout {
    flex-direction: column;
  }

  .edit-panel {
    width: 100%;
    height: 50vh;
  }

  .preview-panel {
    height: 50vh;
    padding: 20px;
  }

  .phone-frame {
    width: 280px;
    height: 606px;
  }
}
</style>
