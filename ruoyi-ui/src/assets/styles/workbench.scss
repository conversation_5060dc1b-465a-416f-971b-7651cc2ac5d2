// 工作台样式变量
:root {
  // 主色调
  --primary-color: #1890ff;
  --primary-light: #40a9ff;
  --primary-dark: #096dd9;
  
  // 成功色
  --success-color: #52c41a;
  --success-light: #73d13d;
  --success-dark: #389e0d;
  
  // 警告色
  --warning-color: #faad14;
  --warning-light: #ffc53d;
  --warning-dark: #d48806;
  
  // 错误色
  --error-color: #ff4d4f;
  --error-light: #ff7875;
  --error-dark: #cf1322;
  
  // 中性色
  --text-primary: #1a1a1a;
  --text-secondary: #666;
  --text-disabled: #999;
  --border-color: #f0f0f0;
  --background-color: #f0f2f5;
  
  // 渐变背景
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-purple: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-orange: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

// 工作台容器样式
.workbench-container {
  height: 100vh;
  background: var(--background-color);
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

// 顶部区域样式
.top-section {
  background: #fff;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  z-index: 10;
  border-bottom: 1px solid var(--border-color);
}

// 侧边栏样式
.sidebar {
  width: 240px;
  background: #001529;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

// 内容区域样式
.content-area {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  background: var(--background-color);
}

// 统计卡片样式
.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid var(--border-color);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
}

// 快速操作卡片样式
.quick-action-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .sidebar {
    width: 200px;
  }
  
  .content-area {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .workbench-container {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: auto;
    order: 2;
  }
  
  .content-area {
    order: 1;
  }
  
  .top-section {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

// 加载状态样式
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
} 