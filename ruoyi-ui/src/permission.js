import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'

NProgress.configure({ showSpinner: false })

const whiteList = ['/login', '/register']

router.beforeEach(async (to, from, next) => {
  console.log('路由跳转:', from.path, '->', to.path)
  NProgress.start()
  
  // 开发环境跳过权限验证，直接允许访问
  if (process.env.NODE_ENV === 'development') {
    console.log('开发环境，跳过权限检查')
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    next()
    NProgress.done()
    return
  }
  
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/index' })
      NProgress.done()
    } else {
      // 检查是否已经获取过用户信息
      const hasUserInfo = store.getters.name && store.getters.roles.length > 0
      if (hasUserInfo) {
        next()
      } else {
        try {
          // 获取用户信息
          await store.dispatch('GetInfo')
          // 用户信息获取成功，继续路由
          next()
        } catch (error) {
          console.error('获取用户信息失败:', error)
          // 获取用户信息失败，清除token并跳转到登录页
          await store.dispatch('LogOut')
          Message.error('获取用户信息失败，请重新登录')
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      // 其他没有访问权限的页面将重定向到登录页面
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
