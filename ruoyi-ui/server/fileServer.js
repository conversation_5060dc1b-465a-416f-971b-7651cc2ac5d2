const express = require('express')
const multer = require('multer')
const path = require('path')
const fs = require('fs')
const cors = require('cors')

const app = express()
const PORT = 28888

// 启用 CORS
app.use(cors())
app.use(express.json())

// 上传目录配置
const UPLOAD_BASE_DIR = path.join(__dirname, '../public/uploads')

// 确保上传目录存在
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
    console.log(`创建目录: ${dirPath}`)
  }
}

// 初始化上传目录
function initializeUploadDirs() {
  const dirs = ['videos', 'music', 'images']
  dirs.forEach(type => {
    const typeDir = path.join(UPLOAD_BASE_DIR, type)
    ensureDirectoryExists(typeDir)
    
    // 创建默认文件夹
    const defaultFolder = path.join(typeDir, '总')
    ensureDirectoryExists(defaultFolder)
  })
}

// 配置 multer 存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const { type, folder = '总' } = req.params
    const uploadPath = path.join(UPLOAD_BASE_DIR, type, folder)
    
    // 确保目录存在
    ensureDirectoryExists(uploadPath)
    
    cb(null, uploadPath)
  },
  filename: function (req, file, cb) {
    // 保持原文件名，如果重复则添加时间戳
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8')
    const ext = path.extname(originalName)
    const name = path.basename(originalName, ext)
    const timestamp = Date.now()
    
    const finalName = `${name}_${timestamp}${ext}`
    cb(null, finalName)
  }
})

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 500 * 1024 * 1024 // 500MB
  }
})

// 获取文件列表
app.get('/api/files/:type/:folder?', (req, res) => {
  try {
    const { type, folder = '总' } = req.params
    const dirPath = path.join(UPLOAD_BASE_DIR, type, folder)
    
    if (!fs.existsSync(dirPath)) {
      return res.json([])
    }
    
    const files = fs.readdirSync(dirPath)
    const fileList = files.map(filename => {
      const filePath = path.join(dirPath, filename)
      const stats = fs.statSync(filePath)
      
      return {
        id: filename,
        name: filename,
        size: stats.size,
        sizeFormatted: formatFileSize(stats.size),
        uploadTime: stats.mtime.toLocaleString(),
        type: getFileType(filename),
        url: `/uploads/${type}/${folder}/${filename}`,
        folder: folder
      }
    })
    
    res.json(fileList)
  } catch (error) {
    console.error('获取文件列表失败:', error)
    res.status(500).json({ error: '获取文件列表失败' })
  }
})

// 上传文件
app.post('/api/upload/:type/:folder?', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '没有文件上传' })
    }
    
    const { type, folder = '总' } = req.params
    const fileInfo = {
      id: req.file.filename,
      name: req.file.filename,
      originalName: Buffer.from(req.file.originalname, 'latin1').toString('utf8'),
      size: req.file.size,
      sizeFormatted: formatFileSize(req.file.size),
      uploadTime: new Date().toLocaleString(),
      type: getFileType(req.file.filename),
      url: `/uploads/${type}/${folder}/${req.file.filename}`,
      folder: folder,
      path: req.file.path
    }
    
    console.log(`文件上传成功: ${fileInfo.originalName} -> ${fileInfo.path}`)
    res.json({ success: true, file: fileInfo })
    
  } catch (error) {
    console.error('文件上传失败:', error)
    res.status(500).json({ error: '文件上传失败' })
  }
})

// 删除文件
app.delete('/api/files/:type/:folder/:filename', (req, res) => {
  try {
    const { type, folder, filename } = req.params
    const filePath = path.join(UPLOAD_BASE_DIR, type, folder, filename)
    
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath)
      console.log(`文件删除成功: ${filePath}`)
      res.json({ success: true })
    } else {
      res.status(404).json({ error: '文件不存在' })
    }
  } catch (error) {
    console.error('删除文件失败:', error)
    res.status(500).json({ error: '删除文件失败' })
  }
})

// 重命名文件
app.put('/api/files/:type/:folder/:filename/rename', (req, res) => {
  try {
    const { type, folder, filename } = req.params
    const { newName } = req.body
    
    const oldPath = path.join(UPLOAD_BASE_DIR, type, folder, filename)
    const newPath = path.join(UPLOAD_BASE_DIR, type, folder, newName)
    
    if (fs.existsSync(oldPath)) {
      fs.renameSync(oldPath, newPath)
      console.log(`文件重命名成功: ${filename} -> ${newName}`)
      res.json({ success: true })
    } else {
      res.status(404).json({ error: '文件不存在' })
    }
  } catch (error) {
    console.error('重命名文件失败:', error)
    res.status(500).json({ error: '重命名文件失败' })
  }
})

// 创建文件夹
app.post('/api/folders/:type', (req, res) => {
  try {
    const { type } = req.params
    const { folderName } = req.body
    
    const folderPath = path.join(UPLOAD_BASE_DIR, type, folderName)
    
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true })
      console.log(`文件夹创建成功: ${folderPath}`)
      res.json({ success: true })
    } else {
      res.status(400).json({ error: '文件夹已存在' })
    }
  } catch (error) {
    console.error('创建文件夹失败:', error)
    res.status(500).json({ error: '创建文件夹失败' })
  }
})

// 获取文件夹列表
app.get('/api/folders/:type', (req, res) => {
  try {
    const { type } = req.params
    const typeDir = path.join(UPLOAD_BASE_DIR, type)
    
    if (!fs.existsSync(typeDir)) {
      return res.json(['总'])
    }
    
    const items = fs.readdirSync(typeDir)
    const folders = items.filter(item => {
      const itemPath = path.join(typeDir, item)
      return fs.statSync(itemPath).isDirectory()
    })
    
    res.json(folders.length > 0 ? folders : ['总'])
  } catch (error) {
    console.error('获取文件夹列表失败:', error)
    res.status(500).json({ error: '获取文件夹列表失败' })
  }
})

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../public/uploads')))

// 工具函数
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function getFileType(filename) {
  const ext = path.extname(filename).toLowerCase()
  const videoExts = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.3gp', '.mkv', '.m4v']
  const audioExts = ['.mp3', '.wav', '.flac', '.aac', '.m4a', '.ogg', '.wma']
  const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  
  if (videoExts.includes(ext)) return 'video'
  if (audioExts.includes(ext)) return 'audio'
  if (imageExts.includes(ext)) return 'image'
  return 'unknown'
}

// 启动服务器
initializeUploadDirs()

app.listen(PORT, () => {
  console.log(`文件服务器运行在 http://localhost:${PORT}`)
  console.log(`上传目录: ${UPLOAD_BASE_DIR}`)
})

module.exports = app
