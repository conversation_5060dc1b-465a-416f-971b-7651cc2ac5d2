@echo off
chcp 65001 >nul
echo ==========================================
echo 临时启动前端 (无需环境变量)
echo ==========================================
echo.

echo 当前目录: %CD%
echo.

if not exist "package.json" (
    echo ❌ 错误: 未找到 package.json
    echo 请确保在 ruoyi-ui 目录中运行
    pause
    exit /b 1
)

echo ✅ 找到 package.json
echo.

echo 正在查找 Node.js 安装...
echo.

REM 定义可能的 Node.js 路径
set "NODEJS_PATHS="C:\Program Files\nodejs" "C:\Program Files (x86)\nodejs" "C:\nodejs" "%LOCALAPPDATA%\Programs\nodejs""

set "FOUND_NODEJS="
set "FOUND_NPM="

REM 查找 Node.js
for %%p in (%NODEJS_PATHS%) do (
    if exist "%%~p\node.exe" (
        echo ✅ 找到 node.exe: %%~p
        set "FOUND_NODEJS=%%~p\node.exe"
    )
    if exist "%%~p\npm.cmd" (
        echo ✅ 找到 npm.cmd: %%~p
        set "FOUND_NPM=%%~p\npm.cmd"
    )
)

if "%FOUND_NPM%"=="" (
    echo.
    echo ❌ 未找到 npm.cmd
    echo.
    echo 请选择解决方案:
    echo [1] 安装 Node.js
    echo [2] 手动指定 Node.js 路径
    echo [3] 使用在线版本 (跳过前端)
    echo.
    set /p solution=请选择 (1-3): 
    
    if "!solution!"=="1" (
        call 安装nodejs.bat
        exit /b
    )
    if "!solution!"=="2" (
        goto manual_path
    )
    if "!solution!"=="3" (
        goto online_only
    )
    
    echo 无效选择
    pause
    exit /b 1
)

echo.
echo 使用找到的 npm: %FOUND_NPM%
echo.

echo 正在启动前端开发服务器...
echo.

REM 设置临时环境变量
set "PATH=%PATH%;%FOUND_NPM:~0,-8%"

REM 启动前端
"%FOUND_NPM%" run dev

if %errorlevel% neq 0 (
    echo.
    echo ❌ 启动失败
    echo.
    echo 可能的原因:
    echo 1. 依赖未安装，请先运行: "%FOUND_NPM%" install
    echo 2. 端口被占用
    echo 3. 配置文件错误
    echo.
    
    echo 是否尝试安装依赖? (y/n)
    set /p install_deps=
    if /i "!install_deps!"=="y" (
        echo 正在安装依赖...
        "%FOUND_NPM%" install
        echo.
        echo 重新启动前端...
        "%FOUND_NPM%" run dev
    )
)

pause
exit /b

:manual_path
echo.
echo 请输入 Node.js 安装路径 (例如: C:\Program Files\nodejs):
set /p manual_nodejs_path=

if exist "%manual_nodejs_path%\npm.cmd" (
    echo ✅ 找到 npm: %manual_nodejs_path%\npm.cmd
    "%manual_nodejs_path%\npm.cmd" run dev
) else (
    echo ❌ 路径无效或未找到 npm.cmd
)

pause
exit /b

:online_only
echo.
echo ==========================================
echo 使用在线版本方案
echo ==========================================
echo.

echo 由于 Node.js 环境问题，建议使用以下方案:
echo.
echo 1. 后台服务: 使用 IDEA 或 Eclipse 启动 Spring Boot
echo 2. 前端: 使用已构建的版本或在线版本
echo 3. Fly-Cut: 使用在线版本 https://fly-cut.vercel.app/
echo.

echo 正在打开相关链接...
start https://fly-cut.vercel.app/

echo.
echo 如果需要本地前端，请先解决 Node.js 环境问题:
echo 1. 运行: 安装nodejs.bat
echo 2. 或手动从 https://nodejs.org/ 下载安装
echo.

pause
exit /b
