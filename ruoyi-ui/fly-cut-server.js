const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 3001;

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.mjs': 'application/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon',
  '.svg': 'image/svg+xml',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.mp4': 'video/mp4'
};

// 设置CORS
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});

// 静态文件服务，设置正确的MIME类型
app.use('/fly-cut', (req, res, next) => {
  const ext = path.extname(req.path).toLowerCase();
  const contentType = mimeTypes[ext] || 'application/octet-stream';

  // 设置正确的Content-Type
  res.setHeader('Content-Type', contentType);

  // 对于JavaScript模块，添加额外的头部
  if (ext === '.js' || ext === '.mjs') {
    res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
  }

  next();
}, express.static(path.join(__dirname, 'public', 'fly-cut')));

// 处理fly-cut-editor.html
app.get('/fly-cut-editor.html', (req, res) => {
  res.setHeader('Content-Type', 'text/html; charset=utf-8');
  res.sendFile(path.join(__dirname, 'public', 'fly-cut-editor.html'));
});

// 根路径重定向
app.get('/', (req, res) => {
  res.redirect('/fly-cut-editor.html');
});

app.listen(PORT, () => {
  console.log(`🚀 Fly-Cut服务器启动成功！`);
  console.log(`📱 访问地址: http://localhost:${PORT}`);
  console.log(`🎬 Fly-Cut编辑器: http://localhost:${PORT}/fly-cut-editor.html`);
  console.log(`📊 直接访问fly-cut: http://localhost:${PORT}/fly-cut/`);
  console.log(`\n💡 这个服务器专门用于解决MIME类型问题`);
});
