@echo off
chcp 65001 >nul
echo.
echo =============================================
echo 🚀 Vue路由修复重启脚本
echo =============================================
echo.
echo 📋 修复内容概述：
echo ✅ 统一路由路径：将所有菜单路径统一为 /storer/*
echo ✅ 修复路由配置：添加了完整的路由定义
echo ✅ 组件导入：修复了Vue组件文件导入路径
echo ✅ 默认重定向：修复了首页重定向逻辑
echo.

:MENU
echo =============================================
echo 🔧 选择操作：
echo =============================================
echo [1] 🔄 重启Vue开发服务器
echo [2] 🧪 测试路由功能
echo [3] 📊 查看修复详情
echo [4] 🌐 打开测试页面
echo [5] 🔍 检查服务状态
echo [6] ❌ 退出
echo.
set /p choice="请输入选项 (1-6): "

if "%choice%"=="1" goto RESTART_SERVER
if "%choice%"=="2" goto TEST_ROUTES
if "%choice%"=="3" goto SHOW_DETAILS
if "%choice%"=="4" goto OPEN_TEST_PAGE
if "%choice%"=="5" goto CHECK_STATUS
if "%choice%"=="6" goto EXIT
echo 无效选项，请重新选择！
goto MENU

:RESTART_SERVER
echo.
echo =============================================
echo 🔄 重启Vue开发服务器
echo =============================================
echo.

cd /d "%~dp0"

echo 📂 当前目录：%CD%
echo.

echo 🛑 尝试停止现有服务器...
taskkill /f /im node.exe 2>nul
timeout /t 2 >nul

echo.
echo 🧹 清理缓存...
if exist node_modules\.cache (
    rmdir /s /q node_modules\.cache
    echo ✅ 已清理 node_modules 缓存
)

echo.
echo 📦 检查依赖...
if not exist node_modules (
    echo 🔄 正在安装依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败！
        pause
        goto MENU
    )
    echo ✅ 依赖安装完成
)

echo.
echo 🚀 启动Vue开发服务器...
echo 💡 提示：服务器启动后，访问 http://localhost:8081
echo 📋 修复内容：
echo    - 菜单路径统一为 /storer/*
echo    - 添加了所有缺失的路由配置
echo    - 修复了组件导入路径
echo.

start /b npm run dev

echo ⏳ 等待服务器启动...
timeout /t 10 >nul

echo.
echo ✅ 开发服务器已启动！
echo 🌐 访问地址: http://localhost:8081
echo 📝 如需停止服务器，请按 Ctrl+C
echo.
pause
goto MENU

:TEST_ROUTES
echo.
echo =============================================
echo 🧪 测试路由功能
echo =============================================
echo.
echo 📋 将要测试的路由：
echo    /storer/index     - 工作台
echo    /storer/store     - 门店列表
echo    /storer/bangding  - 绑定多平台账号
echo    /storer/shipin    - AI剪辑文案
echo    /storer/dou       - 抖音/快手文案
echo    /storer/hong      - 小红书文案
echo    /storer/up        - 素材上传
echo    /storer/dijin     - AI递进式剪辑
echo    /storer/hun       - AI混剪
echo.

echo 🔍 检查服务器状态...
curl -s http://localhost:8081 >nul 2>&1
if errorlevel 1 (
    echo ❌ 服务器未运行！请先启动开发服务器。
    echo 💡 选择选项1来启动服务器
    pause
    goto MENU
)

echo ✅ 服务器正在运行
echo.
echo 🌐 在浏览器中打开主页...
start http://localhost:8081

echo.
echo 📝 请在浏览器中：
echo 1. 检查页面是否正常加载
echo 2. 点击左侧菜单项测试导航
echo 3. 确认不再出现404错误
echo.
pause
goto MENU

:SHOW_DETAILS
echo.
echo =============================================
echo 📊 路由修复详情
echo =============================================
echo.
echo 🔧 修复的问题：
echo ✅ 路径不匹配：菜单使用 /storer/* 但路由配置为 /store/*
echo ✅ 缺失路由：部分菜单项没有对应的路由定义
echo ✅ 组件导入：修复了 .vue 文件扩展名问题
echo ✅ 重定向：修复了默认首页重定向路径
echo.
echo 📁 修改的文件：
echo    src/router/index.js - 主路由配置文件
echo.
echo 🆕 添加的路由：
echo    { path: '/storer/index', component: '() =^> import("@/views/dashboard/index.vue")' }
echo    { path: '/storer/store', component: '() =^> import("@/views/store/store.vue")' }
echo    { path: '/storer/bangding', component: '() =^> import("@/views/store/bangding.vue")' }
echo    { path: '/storer/shipin', component: '() =^> import("@/views/store/shipin.vue")' }
echo    { path: '/storer/dou', component: '() =^> import("@/views/store/dou.vue")' }
echo    { path: '/storer/hong', component: '() =^> import("@/views/store/hong.vue")' }
echo    { path: '/storer/up', component: '() =^> import("@/views/store/up.vue")' }
echo    { path: '/storer/dijin', component: '() =^> import("@/views/store/dijin.vue")' }
echo    { path: '/storer/hun', component: '() =^> import("@/views/store/hun.vue")' }
echo.
echo 💡 现在所有菜单项都有对应的路由配置了！
echo.
pause
goto MENU

:OPEN_TEST_PAGE
echo.
echo =============================================
echo 🌐 打开测试页面
echo =============================================
echo.
echo 🚀 在浏览器中打开路由测试页面...
start route-fix-complete.html

echo.
echo 📝 测试页面功能：
echo ✅ 查看修复内容总结
echo ✅ 测试所有路由链接
echo ✅ 获取重启说明
echo ✅ 实时监控控制台
echo.
pause
goto MENU

:CHECK_STATUS
echo.
echo =============================================
echo 🔍 检查系统状态
echo =============================================
echo.

echo 📂 当前目录：%CD%
echo.

echo 🔍 检查Vue开发服务器状态...
curl -s http://localhost:8081 >nul 2>&1
if errorlevel 1 (
    echo ❌ Vue开发服务器未运行
    echo 💡 建议：选择选项1启动服务器
) else (
    echo ✅ Vue开发服务器正在运行 (http://localhost:8081)
)
echo.

echo 📁 检查关键文件...
if exist src\router\index.js (
    echo ✅ 路由配置文件存在
) else (
    echo ❌ 路由配置文件缺失
)

if exist src\layout\SimpleLayout.vue (
    echo ✅ 布局组件文件存在
) else (
    echo ❌ 布局组件文件缺失
)

if exist package.json (
    echo ✅ package.json 存在
) else (
    echo ❌ package.json 缺失
)

if exist node_modules (
    echo ✅ 依赖已安装
) else (
    echo ❌ 依赖未安装，建议运行 npm install
)

echo.
echo 🔍 检查进程...
tasklist | findstr /i node.exe >nul 2>&1
if errorlevel 1 (
    echo ❌ 没有发现Node.js进程
) else (
    echo ✅ Node.js进程正在运行
    tasklist | findstr /i node.exe
)

echo.
echo 📊 状态检查完成！
echo.
pause
goto MENU

:EXIT
echo.
echo =============================================
echo 👋 感谢使用路由修复工具！
echo =============================================
echo.
echo 📝 总结：
echo ✅ 已修复菜单导航404问题
echo ✅ 统一了路由路径规范
echo ✅ 添加了完整的路由配置
echo.
echo 💡 如果还有问题：
echo 1. 确保已重启Vue开发服务器
echo 2. 清除浏览器缓存
echo 3. 检查控制台错误信息
echo.
echo 🚀 祝你开发愉快！
echo.
timeout /t 3 >nul
exit

:ERROR
echo.
echo ❌ 发生错误！错误代码：%errorlevel%
echo 💡 请检查：
echo    1. 是否在正确的目录中运行脚本
echo    2. 是否已安装Node.js和npm
echo    3. 网络连接是否正常
echo.
pause
goto MENU
