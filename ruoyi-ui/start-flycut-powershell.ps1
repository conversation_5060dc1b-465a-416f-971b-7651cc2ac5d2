# Fly-Cut 本地服务器 (PowerShell版本)
param(
    [int]$Port = 3001
)

Write-Host "===========================================" -ForegroundColor Green
Write-Host "启动 Fly-Cut 本地服务器" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green
Write-Host ""

# 检查fly-cut文件是否存在
$flycutPath = Join-Path $PSScriptRoot "public\fly-cut"
$indexPath = Join-Path $flycutPath "index.html"

if (-not (Test-Path $indexPath)) {
    Write-Host "❌ 错误: fly-cut 文件不存在" -ForegroundColor Red
    Write-Host "路径: $indexPath" -ForegroundColor Yellow
    Write-Host "请确保已正确复制fly-cut文件到 public/fly-cut/ 目录" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

Write-Host "✅ fly-cut 文件检查通过" -ForegroundColor Green
Write-Host ""

# 创建HTTP监听器
$listener = New-Object System.Net.HttpListener
$listener.Prefixes.Add("http://localhost:$Port/")

try {
    $listener.Start()
    Write-Host "🚀 Fly-Cut服务器启动成功！" -ForegroundColor Green
    Write-Host "📱 访问地址: http://localhost:$Port/" -ForegroundColor Cyan
    Write-Host "🎬 编辑器地址: http://localhost:$Port/?templateId=test&templateName=测试模板" -ForegroundColor Cyan
    Write-Host "💚 健康检查: http://localhost:$Port/health" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "✅ 服务器已就绪，可以开始使用fly-cut编辑器了！" -ForegroundColor Green
    Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
    Write-Host "===========================================" -ForegroundColor Green
    
    while ($listener.IsListening) {
        $context = $listener.GetContext()
        $request = $context.Request
        $response = $context.Response
        
        # 设置CORS头
        $response.Headers.Add("Access-Control-Allow-Origin", "*")
        $response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        $response.Headers.Add("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept")
        
        # 处理健康检查
        if ($request.Url.AbsolutePath -eq "/health") {
            $healthResponse = '{"status":"ok","service":"fly-cut-server","port":' + $Port + '}'
            $buffer = [System.Text.Encoding]::UTF8.GetBytes($healthResponse)
            $response.ContentType = "application/json; charset=utf-8"
            $response.ContentLength64 = $buffer.Length
            $response.OutputStream.Write($buffer, 0, $buffer.Length)
            $response.Close()
            continue
        }
        
        # 处理文件请求
        $filePath = $request.Url.AbsolutePath
        if ($filePath -eq "/") {
            $filePath = "/index.html"
        }
        
        $fullPath = Join-Path $flycutPath $filePath.TrimStart('/')
        
        if (Test-Path $fullPath) {
            $content = [System.IO.File]::ReadAllBytes($fullPath)
            $extension = [System.IO.Path]::GetExtension($fullPath).ToLower()
            
            # 设置正确的Content-Type
            switch ($extension) {
                ".html" { $response.ContentType = "text/html; charset=utf-8" }
                ".js" { $response.ContentType = "application/javascript; charset=utf-8" }
                ".css" { $response.ContentType = "text/css; charset=utf-8" }
                ".json" { $response.ContentType = "application/json; charset=utf-8" }
                ".png" { $response.ContentType = "image/png" }
                ".jpg" { $response.ContentType = "image/jpeg" }
                ".ico" { $response.ContentType = "image/x-icon" }
                ".woff" { $response.ContentType = "font/woff" }
                ".woff2" { $response.ContentType = "font/woff2" }
                ".ttf" { $response.ContentType = "font/ttf" }
                ".mp4" { $response.ContentType = "video/mp4" }
                default { $response.ContentType = "application/octet-stream" }
            }
            
            $response.ContentLength64 = $content.Length
            $response.OutputStream.Write($content, 0, $content.Length)
        } else {
            $response.StatusCode = 404
            $notFoundContent = [System.Text.Encoding]::UTF8.GetBytes("File not found: $filePath")
            $response.ContentLength64 = $notFoundContent.Length
            $response.OutputStream.Write($notFoundContent, 0, $notFoundContent.Length)
        }
        
        $response.Close()
    }
} catch {
    Write-Host "❌ 启动服务器时出错: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Message -like "*拒绝访问*" -or $_.Exception.Message -like "*Access is denied*") {
        Write-Host "可能是端口被占用，请尝试使用其他端口" -ForegroundColor Yellow
    }
} finally {
    if ($listener.IsListening) {
        $listener.Stop()
    }
    Write-Host "🛑 服务器已停止" -ForegroundColor Yellow
}
