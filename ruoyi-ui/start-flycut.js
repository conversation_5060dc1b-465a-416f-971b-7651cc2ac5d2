// 简化的fly-cut服务器启动脚本
const express = require('express');
const path = require('path');

const app = express();
const PORT = 3001;

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html; charset=utf-8',
  '.js': 'application/javascript; charset=utf-8',
  '.mjs': 'application/javascript; charset=utf-8',
  '.css': 'text/css; charset=utf-8',
  '.json': 'application/json; charset=utf-8',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon',
  '.svg': 'image/svg+xml',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2',
  '.ttf': 'font/ttf',
  '.mp4': 'video/mp4'
};

// 设置CORS
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});

// 静态文件服务，设置正确的MIME类型
app.use('/fly-cut', (req, res, next) => {
  const ext = path.extname(req.path).toLowerCase();
  const contentType = mimeTypes[ext] || 'application/octet-stream';
  
  // 设置正确的Content-Type
  res.setHeader('Content-Type', contentType);
  
  // 禁用缓存，确保开发时能看到最新文件
  res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  
  next();
}, express.static(path.join(__dirname, 'public', 'fly-cut')));

// 根路径重定向到fly-cut
app.get('/', (req, res) => {
  res.redirect('/fly-cut/');
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'fly-cut-server', port: PORT });
});

app.listen(PORT, () => {
  console.log('🚀 Fly-Cut本地服务器启动成功！');
  console.log(`📱 访问地址: http://localhost:${PORT}/fly-cut/`);
  console.log(`🎬 编辑器地址: http://localhost:${PORT}/fly-cut/?templateId=test&templateName=测试模板`);
  console.log(`💚 健康检查: http://localhost:${PORT}/health`);
  console.log('');
  console.log('✅ 服务器已就绪，可以开始使用fly-cut编辑器了！');
});
