#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fly-Cut 本地服务器 (Python版本)
解决MIME类型问题，为fly-cut提供正确的静态文件服务
"""

import http.server
import socketserver
import os
import mimetypes
from urllib.parse import urlparse

class FlyCutHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # 设置服务根目录为 public/fly-cut
        super().__init__(*args, directory=os.path.join(os.getcwd(), 'public', 'fly-cut'), **kwargs)
    
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept')
        
        # 禁用缓存，确保开发时能看到最新文件
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        
        super().end_headers()
    
    def guess_type(self, path):
        """重写MIME类型检测，确保JavaScript文件正确识别"""
        mimetype, encoding = mimetypes.guess_type(path)
        
        # 特殊处理JavaScript文件
        if path.endswith('.js') or path.endswith('.mjs'):
            mimetype = 'application/javascript'
        elif path.endswith('.css'):
            mimetype = 'text/css'
        elif path.endswith('.html'):
            mimetype = 'text/html'
        elif path.endswith('.json'):
            mimetype = 'application/json'
        
        # 添加charset
        if mimetype and mimetype.startswith(('text/', 'application/javascript', 'application/json')):
            mimetype += '; charset=utf-8'
            
        return mimetype, encoding
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.end_headers()

def main():
    PORT = 3001
    
    # 检查fly-cut目录是否存在
    flycut_dir = os.path.join(os.getcwd(), 'public', 'fly-cut')
    if not os.path.exists(flycut_dir):
        print(f"❌ 错误: fly-cut目录不存在: {flycut_dir}")
        print("请确保已经正确复制了fly-cut文件到 public/fly-cut/ 目录")
        return
    
    # 检查关键文件
    index_file = os.path.join(flycut_dir, 'index.html')
    if not os.path.exists(index_file):
        print(f"❌ 错误: index.html文件不存在: {index_file}")
        return
    
    try:
        with socketserver.TCPServer(("", PORT), FlyCutHandler) as httpd:
            print("=" * 50)
            print("🚀 Fly-Cut本地服务器启动成功！")
            print("=" * 50)
            print(f"📱 访问地址: http://localhost:{PORT}/")
            print(f"🎬 编辑器地址: http://localhost:{PORT}/?templateId=test&templateName=测试模板")
            print(f"💚 服务目录: {flycut_dir}")
            print("")
            print("✅ 服务器已就绪，可以开始使用fly-cut编辑器了！")
            print("按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 10048:  # Windows: 端口被占用
            print(f"❌ 错误: 端口 {PORT} 已被占用")
            print("请关闭占用该端口的程序，或修改PORT变量使用其他端口")
        else:
            print(f"❌ 启动服务器时出错: {e}")

if __name__ == "__main__":
    main()
