@echo off
chcp 65001 >nul
echo.
echo =============================================
echo 🎉 内部路由配置完成！
echo =============================================
echo.
echo 📋 已完成的修改：
echo ✅ 创建了话题创建页面 (huati.vue)
echo ✅ 创建了算力明细页面 (mingxi.vue)  
echo ✅ 创建了待发布视频库页面 (sk.vue)
echo ✅ 创建了图片库营销活动页面 (tk.vue)
echo ✅ 创建了奖品设置页面 (jiang.vue)
echo ✅ 创建了大转盘配置页面 (zhuanp.vue)
echo ✅ 创建了待领取信息页面 (daijiang.vue)
echo ✅ 更新了菜单配置，将外部链接改为内部路由
echo ✅ 添加了相应的路由配置
echo.
echo 🔄 现在这些菜单项将跳转到本地页面而不是外部网站：
echo    • 话题创建 → /storer/huati
echo    • 算力明细 → /storer/mingxi  
echo    • 待发布视频库 → /storer/sk
echo    • 图片库营销活动 → /storer/tk
echo    • 奖品设置 → /storer/jiang
echo    • 大转盘配置 → /storer/zhuanp
echo    • 待领取信息 → /storer/daijiang
echo.

:MENU
echo =============================================
echo 🔧 选择操作：
echo =============================================
echo [1] 🔄 重启Vue开发服务器
echo [2] 🧪 测试新页面路由
echo [3] 🌐 打开测试页面
echo [4] 📊 查看修改详情
echo [5] ❌ 退出
echo.
set /p choice="请输入选项 (1-5): "

if "%choice%"=="1" goto RESTART_SERVER
if "%choice%"=="2" goto TEST_ROUTES
if "%choice%"=="3" goto OPEN_TEST_PAGE
if "%choice%"=="4" goto SHOW_DETAILS
if "%choice%"=="5" goto EXIT
echo 无效选项，请重新选择！
goto MENU

:RESTART_SERVER
echo.
echo =============================================
echo 🔄 重启Vue开发服务器
echo =============================================
echo.

cd /d "%~dp0"

echo 📂 当前目录：%CD%
echo.

echo 🛑 停止现有服务器...
taskkill /f /im node.exe 2>nul
timeout /t 3 >nul

echo.
echo 🧹 清理缓存...
if exist node_modules\.cache (
    rmdir /s /q node_modules\.cache
    echo ✅ 已清理缓存
)

echo.
echo 🚀 启动Vue开发服务器...
echo 💡 提示：新增页面路由已配置完成
echo.

start /b npm run dev

echo ⏳ 等待服务器启动...
timeout /t 10 >nul

echo.
echo ✅ 开发服务器已启动！
echo 🌐 访问地址: http://localhost:8081
echo.
pause
goto MENU

:TEST_ROUTES
echo.
echo =============================================
echo 🧪 测试新页面路由
echo =============================================
echo.
echo 📋 将要测试的新路由：
echo    /storer/huati  - 话题创建
echo    /storer/mingxi - 算力明细
echo    /storer/sk     - 待发布视频库  
echo    /storer/tk     - 图片库营销活动
echo    /storer/jiang  - 奖品设置
echo    /storer/zhuanp - 大转盘配置
echo    /storer/daijiang - 待领取信息
echo.

echo 🔍 检查服务器状态...
curl -s http://localhost:8081 >nul 2>&1
if errorlevel 1 (
    echo ❌ 服务器未运行！请先启动开发服务器。
    echo 💡 选择选项1来启动服务器
    pause
    goto MENU
)

echo ✅ 服务器正在运行
echo.
echo 🌐 在浏览器中打开新页面...
start http://localhost:8081/storer/huati
timeout /t 2 >nul
start http://localhost:8081/storer/mingxi  
timeout /t 2 >nul
start http://localhost:8081/storer/sk
timeout /t 2 >nul
start http://localhost:8081/storer/tk
timeout /t 2 >nul
start http://localhost:8081/storer/jiang
timeout /t 2 >nul
start http://localhost:8081/storer/zhuanp
timeout /t 2 >nul
start http://localhost:8081/storer/daijiang

echo.
echo 📝 请在浏览器中：
echo 1. 检查所有新页面是否正常加载
echo 2. 点击左侧菜单项测试内部导航
echo 3. 确认不再跳转到外部网站
echo.
pause
goto MENU

:OPEN_TEST_PAGE
echo.
echo =============================================
echo 🌐 打开内部路由测试页面
echo =============================================
echo.
echo 🚀 创建测试页面...

echo ^<!DOCTYPE html^> > internal-route-test.html
echo ^<html lang="zh-CN"^> >> internal-route-test.html
echo ^<head^> >> internal-route-test.html
echo     ^<meta charset="UTF-8"^> >> internal-route-test.html
echo     ^<title^>内部路由测试^</title^> >> internal-route-test.html
echo     ^<style^> >> internal-route-test.html
echo         body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; } >> internal-route-test.html
echo         .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); } >> internal-route-test.html
echo         h1 { color: #333; text-align: center; } >> internal-route-test.html
echo         .route-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 15px; margin: 20px 0; } >> internal-route-test.html
echo         .route-item { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-decoration: none; transition: transform 0.3s; } >> internal-route-test.html
echo         .route-item:hover { transform: translateY(-3px); text-decoration: none; color: white; } >> internal-route-test.html
echo         .route-title { font-size: 18px; font-weight: bold; margin-bottom: 8px; } >> internal-route-test.html
echo         .route-desc { font-size: 14px; opacity: 0.9; } >> internal-route-test.html
echo         .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0; } >> internal-route-test.html
echo     ^</style^> >> internal-route-test.html
echo ^</head^> >> internal-route-test.html
echo ^<body^> >> internal-route-test.html
echo     ^<div class="container"^> >> internal-route-test.html
echo         ^<h1^>🎉 内部路由配置成功！^</h1^> >> internal-route-test.html
echo         ^<div class="success"^> >> internal-route-test.html
echo             ^<strong^>✅ 配置完成！^</strong^> 所有外部链接已改为内部路由，点击菜单项现在会跳转到本地页面。 >> internal-route-test.html
echo         ^</div^> >> internal-route-test.html
echo         ^<h2^>测试新路由：^</h2^> >> internal-route-test.html
echo         ^<div class="route-list"^> >> internal-route-test.html
echo             ^<a href="http://localhost:8081/storer/huati" class="route-item" target="_blank"^> >> internal-route-test.html
echo                 ^<div class="route-title"^>#️⃣ 话题创建^</div^> >> internal-route-test.html
echo                 ^<div class="route-desc"^>创建和管理话题内容^</div^> >> internal-route-test.html
echo             ^</a^> >> internal-route-test.html
echo             ^<a href="http://localhost:8081/storer/mingxi" class="route-item" target="_blank"^> >> internal-route-test.html
echo                 ^<div class="route-title"^>⚡ 算力明细^</div^> >> internal-route-test.html
echo                 ^<div class="route-desc"^>查看算力使用详情和统计^</div^> >> internal-route-test.html
echo             ^</a^> >> internal-route-test.html
echo             ^<a href="http://localhost:8081/storer/sk" class="route-item" target="_blank"^> >> internal-route-test.html
echo                 ^<div class="route-title"^>📹 待发布视频库^</div^> >> internal-route-test.html
echo                 ^<div class="route-desc"^>管理待发布的视频内容^</div^> >> internal-route-test.html
echo             ^</a^> >> internal-route-test.html
echo             ^<a href="http://localhost:8081/storer/tk" class="route-item" target="_blank"^> >> internal-route-test.html
echo                 ^<div class="route-title"^>🖼️ 图片库营销活动^</div^> >> internal-route-test.html
echo                 ^<div class="route-desc"^>管理图片素材和营销活动^</div^> >> internal-route-test.html
echo             ^</a^> >> internal-route-test.html
echo         ^</div^> >> internal-route-test.html
echo     ^</div^> >> internal-route-test.html
echo ^</body^> >> internal-route-test.html
echo ^</html^> >> internal-route-test.html

echo ✅ 测试页面已创建
echo 🌐 在浏览器中打开测试页面...
start internal-route-test.html

echo.
pause
goto MENU

:SHOW_DETAILS
echo.
echo =============================================
echo 📊 修改详情
echo =============================================
echo.
echo 🆕 创建的Vue组件：
echo    src/views/store/huati.vue  - 话题创建页面
echo       • 话题创建表单和管理
echo       • 标签管理功能
echo       • 话题列表展示
echo.
echo    src/views/store/mingxi.vue - 算力明细页面  
echo       • 算力统计概览
echo       • 使用记录列表
echo       • 详情查看和重试功能
echo.
echo    src/views/store/sk.vue     - 待发布视频库页面
echo       • 视频上传和管理
echo       • 多平台发布配置
echo       • 视频预览功能
echo.
echo    src/views/store/tk.vue     - 图片库营销活动页面
echo       • 图片库管理
echo       • 营销活动创建
echo       • 多格式图片支持
echo.
echo 🔧 修改的配置文件：
echo    src/layout/SimpleLayout.vue
echo       • 将外部链接改为 router-link
echo       • 移除了 external-link 类和 target="_blank"
echo       • 更新了路径为 /storer/* 格式
echo.
echo    src/router/index.js
echo       • 添加了4个新路由配置
echo       • 正确配置了组件导入路径
echo       • 设置了页面标题和图标
echo.
echo 💡 现在点击这些菜单项会：
echo    ✅ 在当前窗口内跳转（不是新标签页）
echo    ✅ 使用Vue路由导航（无页面刷新）
echo    ✅ 显示对应的Vue组件页面
echo    ✅ 保持应用的单页面特性
echo.
pause
goto MENU

:EXIT
echo.
echo =============================================
echo 👋 内部路由配置完成！
echo =============================================
echo.
echo 📝 总结：
echo ✅ 已将外部链接改为内部路由
echo ✅ 创建了4个功能完整的Vue页面
echo ✅ 更新了菜单和路由配置
echo ✅ 现在所有功能都在本地应用内
echo.
echo 💡 下次启动应用后：
echo 1. 点击菜单项会在当前页面内跳转
echo 2. 每个页面都有完整的功能界面
echo 3. 不再跳转到外部网站
echo.
echo 🚀 祝你开发愉快！
echo.
timeout /t 3 >nul
exit
