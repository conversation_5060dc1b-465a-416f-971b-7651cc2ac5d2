{"name": "ruoyi-flowable-plus", "version": "0.8.3", "description": "后台管理", "author": "KonBAI", "license": "MIT", "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "dev:full": "start-dev-with-flycut.bat", "build:prod": "vue-cli-service build", "build:flycut": "build-fly-cut.bat", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/KonBAI-Q/ruoyi-flowable-plus.git"}, "dependencies": {"@babel/parser": "7.7.4", "@babel/runtime": "^7.28.2", "@riophae/vue-treeselect": "0.4.0", "ali-oss": "^6.23.0", "axios": "0.24.0", "bpmn-js-token-simulation": "0.10.0", "clipboard": "2.0.8", "core-js": "3.25.3", "echarts": "5.4.0", "element-ui": "2.15.12", "express": "^5.1.0", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "10.5.0", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "lodash-es": "^4.17.21", "moment": "^2.30.1", "nprogress": "0.2.0", "qrcode-generator": "^2.0.2", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "spark-md5": "^3.0.2", "v-hotkey": "^0.9.0", "video.js": "^7.21.7", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-draggable-resizable": "^2.3.0", "vue-meta": "2.4.0", "vue-print-nb": "^1.7.5", "vue-router": "3.4.9", "vue-video-player": "^5.0.2", "vuedraggable": "2.24.3", "vuex": "3.6.0", "wavesurfer.js": "^7.10.1", "xml-js": "1.6.11"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "bpmn-js": "7.5.0", "bpmn-js-properties-panel": "0.37.2", "camunda-bpmn-moddle": "4.4.1", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "less": "file:../less-3.13.1.tgz", "less-loader": "^5.0.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "^2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}