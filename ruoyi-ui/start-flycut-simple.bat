@echo off
chcp 65001 >nul
echo ==========================================
echo 启动 Fly-Cut 本地服务器
echo ==========================================
echo.

echo 检查 fly-cut 文件...
if not exist "public\fly-cut\index.html" (
    echo ❌ 错误: fly-cut 文件不存在
    echo 请确保 public\fly-cut\ 目录中有完整的 fly-cut 文件
    pause
    exit /b 1
)

echo ✅ fly-cut 文件检查通过
echo.

echo 尝试启动服务器...
echo 🚀 正在启动 Fly-Cut 服务器...
echo 📱 访问地址: http://localhost:3001/
echo.

REM 尝试使用 Python 启动服务器
python -c "import http.server, socketserver, os; os.chdir('public/fly-cut'); httpd = socketserver.TCPServer(('', 3001), http.server.SimpleHTTPRequestHandler); print('服务器启动成功！访问 http://localhost:3001/'); httpd.serve_forever()" 2>nul

if %errorlevel% neq 0 (
    echo Python 不可用，尝试使用 Node.js...
    
    REM 尝试使用 Node.js
    node -e "const http=require('http'),fs=require('fs'),path=require('path');const server=http.createServer((req,res)=>{const filePath=path.join(__dirname,'public','fly-cut',req.url==='/'?'index.html':req.url);fs.readFile(filePath,(err,data)=>{if(err){res.writeHead(404);res.end('Not Found');return;}const ext=path.extname(filePath);const contentType=ext==='.js'?'application/javascript':ext==='.css'?'text/css':ext==='.html'?'text/html':'text/plain';res.writeHead(200,{'Content-Type':contentType,'Access-Control-Allow-Origin':'*'});res.end(data);});});server.listen(3001,()=>console.log('服务器启动成功！访问 http://localhost:3001/'));" 2>nul
    
    if %errorlevel% neq 0 (
        echo.
        echo ❌ 无法启动服务器
        echo 请确保安装了 Python 或 Node.js
        echo.
        echo 解决方案：
        echo 1. 安装 Python: https://www.python.org/downloads/
        echo 2. 或安装 Node.js: https://nodejs.org/
        echo 3. 或使用在线版本: https://fly-cut.vercel.app/
        echo.
        pause
        exit /b 1
    )
)

pause
