<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广页面系统测试</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .feature-section {
            margin-bottom: 40px;
        }
        
        .feature-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            border-left: 4px solid #667eea;
            padding-left: 15px;
        }
        
        .url-list {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .url-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .url-label {
            font-weight: bold;
            color: #667eea;
            min-width: 120px;
        }
        
        .url-link {
            color: #007bff;
            text-decoration: none;
            flex: 1;
            word-break: break-all;
        }
        
        .url-link:hover {
            text-decoration: underline;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .test-btn:hover {
            opacity: 0.9;
        }
        
        .workflow-steps {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .step-desc {
            color: #666;
            font-size: 0.9em;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .feature-card h4 {
            color: #667eea;
            margin: 0 0 10px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            color: #666;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            background: #28a745;
            color: white;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🚀 推广页面系统</h1>
            <p>门店分享推广页面管理系统 - 功能演示</p>
        </div>
        
        <div class="content">
            <!-- 系统概述 -->
            <div class="feature-section">
                <div class="feature-title">📋 系统概述</div>
                <p>这是一个为多商户设计的分享推广页面系统，每个门店都有独特的推广页面（通过ID区分），支持实时配置所有页面元素。</p>
            </div>

            <!-- 快速访问链接 -->
            <div class="feature-section">
                <div class="feature-title">🔗 快速访问链接</div>
                <div class="url-list">
                    <div class="url-item">
                        <span class="url-label">门店管理：</span>
                        <a href="http://localhost:8081/#/store/store" class="url-link" target="_blank">
                            http://localhost:8081/#/store/store
                        </a>
                        <button class="test-btn" onclick="window.open('http://localhost:8081/#/store/store', '_blank')">访问</button>
                    </div>
                    <div class="url-item">
                        <span class="url-label">推广页面1：</span>
                        <a href="http://localhost:8081/#/promotion/1" class="url-link" target="_blank">
                            http://localhost:8081/#/promotion/1
                        </a>
                        <button class="test-btn" onclick="window.open('http://localhost:8081/#/promotion/1', '_blank')">访问</button>
                    </div>
                    <div class="url-item">
                        <span class="url-label">推广配置页：</span>
                        <a href="http://localhost:8081/#/promotion/config?storeId=1&storeName=测试店铺" class="url-link" target="_blank">
                            http://localhost:8081/#/promotion/config?storeId=1&storeName=测试店铺
                        </a>
                        <button class="test-btn" onclick="window.open('http://localhost:8081/#/promotion/config?storeId=1&storeName=测试店铺', '_blank')">访问</button>
                    </div>
                </div>
            </div>

            <!-- 功能特性 -->
            <div class="feature-section">
                <div class="feature-title">✨ 系统功能特性</div>
                <div class="features-grid">
                    <div class="feature-card">
                        <h4>📱 移动端优化</h4>
                        <ul class="feature-list">
                            <li>响应式手机页面设计</li>
                            <li>符合移动端用户习惯</li>
                            <li>完美适配各种屏幕尺寸</li>
                            <li>触摸友好的交互体验</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>🎨 实时配置系统</h4>
                        <ul class="feature-list">
                            <li>实时预览配置效果</li>
                            <li>图片、文字、背景配置</li>
                            <li>平台图标自定义配置</li>
                            <li>一键保存配置更改</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>🏪 多门店管理</h4>
                        <ul class="feature-list">
                            <li>独立门店推广页面</li>
                            <li>ID递增唯一标识</li>
                            <li>门店信息统一管理</li>
                            <li>批量操作支持</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>🔧 完整的工作流</h4>
                        <ul class="feature-list">
                            <li>新增门店一键创建</li>
                            <li>推广页面自动生成</li>
                            <li>配置页面即时跳转</li>
                            <li>数据持久化存储</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 使用流程 -->
            <div class="feature-section">
                <div class="feature-title">📝 完整使用流程</div>
                <div class="workflow-steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <div class="step-title">访问门店管理页面</div>
                            <div class="step-desc">在门店管理页面可以查看所有门店信息，管理门店配置</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <div class="step-title">点击"新增门店"按钮</div>
                            <div class="step-desc">填写门店基本信息，包括名称、地址、联系方式等</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <div class="step-title">自动生成推广页面</div>
                            <div class="step-desc">系统自动为新门店创建独特的推广页面（ID递增）</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <div class="step-title">配置推广页面内容</div>
                            <div class="step-desc">点击"配置推广页面"按钮，实时编辑页面所有元素</div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <div class="step-title">分享推广页面</div>
                            <div class="step-desc">获取独特的推广页面链接，用于营销推广</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技术实现 -->
            <div class="feature-section">
                <div class="feature-title">⚙️ 技术实现</div>
                <div class="features-grid">
                    <div class="feature-card">
                        <h4>前端技术栈</h4>
                        <ul class="feature-list">
                            <li>Vue.js 2.x 框架</li>
                            <li>Element UI 组件库</li>
                            <li>Vue Router 路由管理</li>
                            <li>SCSS 样式预处理</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h4>组件架构</h4>
                        <ul class="feature-list">
                            <li>PromotionPage.vue - 推广页面展示</li>
                            <li>PromotionPageConfig.vue - 配置界面</li>
                            <li>AddStoreDialog.vue - 门店创建</li>
                            <li>store.vue - 门店管理</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 状态显示 -->
            <div class="feature-section">
                <div class="feature-title">📊 系统状态</div>
                <p>所有组件已成功创建并集成<span class="status-badge">✅ 运行正常</span></p>
                <p>Vue应用编译成功，无错误<span class="status-badge">✅ 编译成功</span></p>
                <p>路由配置完成，页面可正常访问<span class="status-badge">✅ 路由正常</span></p>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后的提示
        window.onload = function() {
            console.log('🎉 推广页面系统测试页面加载完成！');
            console.log('📱 请通过上方链接访问各个功能页面');
            console.log('🔧 系统已准备就绪，可以开始测试！');
        };
    </script>
</body>
</html>
