@echo off
echo ==========================================
echo Permanent Node.js Environment Fix
echo ==========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Administrator privileges required
    echo.
    echo Please right-click this script and select "Run as administrator"
    pause
    exit /b 1
)

echo [OK] Administrator privileges confirmed
echo.

echo 1. Setting up Node.js v18.19.0 as default...
echo.

REM Use nvm to set Node.js version
C:\chajian\nvm\nvm.exe use 18.19.0

echo 2. Adding Node.js paths to system environment variables...
echo.

REM Get current system PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SYSTEM_PATH=%%b"

REM Define Node.js paths
set "NODEJS_PATH=C:\chajian\nvm\v18.19.0"
set "NVM_PATH=C:\chajian\nvm"

echo Adding paths:
echo - %NODEJS_PATH%
echo - %NVM_PATH%
echo.

REM Check and add Node.js path
echo %SYSTEM_PATH% | findstr /i "%NODEJS_PATH%" >nul
if %errorlevel% neq 0 (
    echo Adding Node.js path to system PATH...
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "%SYSTEM_PATH%;%NODEJS_PATH%" /f
    
    REM Update SYSTEM_PATH for next check
    set "SYSTEM_PATH=%SYSTEM_PATH%;%NODEJS_PATH%"
) else (
    echo Node.js path already in system PATH
)

REM Check and add NVM path
echo %SYSTEM_PATH% | findstr /i "%NVM_PATH%" >nul
if %errorlevel% neq 0 (
    echo Adding NVM path to system PATH...
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "%SYSTEM_PATH%;%NVM_PATH%" /f
) else (
    echo NVM path already in system PATH
)

echo.
echo 3. Setting NVM environment variables...
echo.

REM Set NVM_HOME
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v NVM_HOME /t REG_SZ /d "C:\chajian\nvm" /f

REM Set NVM_SYMLINK
reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v NVM_SYMLINK /t REG_SZ /d "C:\chajian\nodejs" /f

echo.
echo 4. Creating symbolic link for nodejs...
echo.

REM Create nodejs directory if not exists
if not exist "C:\chajian\nodejs" (
    mklink /D "C:\chajian\nodejs" "C:\chajian\nvm\v18.19.0"
) else (
    echo Symbolic link already exists
)

echo.
echo 5. Refreshing environment variables...
echo.

REM Notify system that environment variables have changed
powershell -Command "[Environment]::SetEnvironmentVariable('Path', [Environment]::GetEnvironmentVariable('Path', 'Machine'), 'Machine')"

echo.
echo 6. Testing Node.js installation...
echo.

REM Test Node.js
C:\chajian\nvm\v18.19.0\node.exe --version
if %errorlevel% equ 0 (
    echo [OK] Node.js is working
) else (
    echo [ERROR] Node.js test failed
)

REM Test npm
C:\chajian\nvm\v18.19.0\npm.cmd --version
if %errorlevel% equ 0 (
    echo [OK] npm is working
) else (
    echo [ERROR] npm test failed
)

echo.
echo ==========================================
echo Permanent fix completed!
echo.
echo IMPORTANT NEXT STEPS:
echo 1. Close ALL command prompt windows
echo 2. Open a NEW command prompt
echo 3. Test: node --version
echo 4. Test: npm --version
echo 5. If still not working, restart computer
echo ==========================================
pause
