@echo off
echo ==========================================
echo Node.js Environment Variable Fix Tool
echo ==========================================
echo.

echo Please run this script as Administrator!
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Administrator privileges required
    echo.
    echo Please right-click this script and select "Run as administrator"
    pause
    exit /b 1
)

echo [OK] Administrator privileges confirmed
echo.

echo 1. Finding Node.js installation...
echo.

set "NODEJS_PATH="
set "FOUND_PATHS="

REM Check common paths
for %%p in ("C:\Program Files\nodejs" "C:\Program Files (x86)\nodejs" "C:\nodejs" "%LOCALAPPDATA%\Programs\nodejs") do (
    if exist "%%~p\node.exe" (
        echo [FOUND] Node.js: %%~p
        set "NODEJS_PATH=%%~p"
        set "FOUND_PATHS=!FOUND_PATHS!%%~p;"
    )
)

if "%NODEJS_PATH%"=="" (
    echo [ERROR] Node.js installation not found
    echo.
    echo Please install Node.js first:
    echo 1. Visit https://nodejs.org/
    echo 2. Download LTS version
    echo 3. Run installer
    echo 4. Run this script again
    pause
    exit /b 1
)

echo.
echo 2. Current system PATH:
echo %PATH%
echo.

echo 3. Adding Node.js to system PATH...
echo.

REM Get current system PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "SYSTEM_PATH=%%b"

REM Check if already in PATH
echo %SYSTEM_PATH% | findstr /i "%NODEJS_PATH%" >nul
if %errorlevel% equ 0 (
    echo [OK] Node.js path already in system PATH
) else (
    echo Adding %NODEJS_PATH% to system PATH...
    
    REM Add to system PATH
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "%SYSTEM_PATH%;%NODEJS_PATH%" /f
    
    if %errorlevel% equ 0 (
        echo [OK] Successfully added to system PATH
    ) else (
        echo [ERROR] Failed to add to PATH
    )
)

echo.
echo 4. Refreshing environment variables...
echo.

REM Notify system that environment variables have changed
powershell -Command "[Environment]::SetEnvironmentVariable('Path', [Environment]::GetEnvironmentVariable('Path', 'Machine'), 'Machine')"

echo 5. Testing Node.js commands...
echo.

REM Test using full path
"%NODEJS_PATH%\node.exe" --version
if %errorlevel% equ 0 (
    echo [OK] node.exe can run
) else (
    echo [ERROR] node.exe failed to run
)

"%NODEJS_PATH%\npm.cmd" --version
if %errorlevel% equ 0 (
    echo [OK] npm.cmd can run
) else (
    echo [ERROR] npm.cmd failed to run
)

echo.
echo ==========================================
echo Fix completed!
echo.
echo IMPORTANT:
echo 1. Close all command prompt windows
echo 2. Open a new command prompt
echo 3. Test: npm --version
echo 4. If still not working, restart computer
echo ==========================================
pause
