@echo off
chcp 65001 >nul
echo ================================================
echo 🔧 修复系统更新认证问题工具
echo ================================================
echo.

echo 💡 问题诊断与修复向导
echo.

echo [1/4] 检查Git配置...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git未安装，请先安装Git
    echo 下载地址: https://git-scm.com/download/win
    goto :end
)
echo ✅ Git已安装

echo.
echo [2/4] 检查Git仓库状态...
git status >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 当前目录不是Git仓库或Git配置有问题
    echo.
    echo 💡 解决方案：
    echo   1. 确认当前在项目根目录
    echo   2. 重新初始化仓库: git init
    echo   3. 添加远程仓库: git remote add origin [仓库地址]
    goto :end
)
echo ✅ Git仓库状态正常

echo.
echo [3/4] 检查网络连接...
ping -n 1 github.com >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  网络连接可能有问题
    echo.
    echo 💡 解决方案：
    echo   1. 检查网络连接
    echo   2. 如在公司网络，可能需要配置代理
    echo   3. 使用手机热点测试网络
) else (
    echo ✅ 网络连接正常
)

echo.
echo [4/4] 检查Git认证...
git ls-remote origin >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git认证失败
    echo.
    echo 💡 解决方案：
    echo   1. 检查SSH密钥配置: ssh -T **************
    echo   2. 或配置用户名密码认证
    echo   3. 使用个人访问令牌(GitHub)
    echo.
    echo 🔧 快速修复选项：
    echo   [1] 配置用户名和邮箱
    echo   [2] 生成SSH密钥
    echo   [3] 跳过认证检查
    echo.
    set /p choice="请选择修复选项 (1-3): "
    
    if "!choice!"=="1" (
        echo.
        set /p username="请输入Git用户名: "
        set /p email="请输入Git邮箱: "
        git config --global user.name "!username!"
        git config --global user.email "!email!"
        echo ✅ 用户信息配置完成
    )
    
    if "!choice!"=="2" (
        echo.
        echo 正在生成SSH密钥...
        ssh-keygen -t rsa -b 4096 -f %USERPROFILE%\.ssh\id_rsa -N ""
        echo.
        echo ✅ SSH密钥已生成，请将以下公钥添加到Git服务器：
        echo.
        type %USERPROFILE%\.ssh\id_rsa.pub
        echo.
        echo 📋 复制上面的公钥到GitHub/GitLab的SSH Keys设置中
    )
    
    if "!choice!"=="3" (
        echo.
        echo ⚠️  跳过认证检查，将尝试强制更新...
        goto :force_update
    )
) else (
    echo ✅ Git认证正常
)

echo.
echo ================================================
echo ✅ 诊断完成！现在可以尝试运行 update.bat
echo ================================================
goto :end

:force_update
echo.
echo 🔄 尝试强制更新（跳过认证检查）...
echo.
echo 1. 备份本地修改...
git stash
echo.
echo 2. 重置到最新状态...
git reset --hard HEAD
echo.
echo 3. 强制拉取最新代码...
git fetch origin main
git reset --hard origin/main
echo.
echo 4. 构建前端...
if exist "ruoyi-ui" (
    cd ruoyi-ui
    call npm install
    call npm run build:prod
    cd ..
)
echo.
echo ✅ 强制更新完成！

:end
echo.
echo 💡 更多帮助：
echo   - 如果问题持续，请联系技术支持
echo   - 或手动执行每个步骤进行排查
echo.
pause
