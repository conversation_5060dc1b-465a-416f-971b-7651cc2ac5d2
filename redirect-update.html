<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="refresh" content="0; url=http://localhost:8078/index.html">
    <title>重定向到更新页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .redirect-info {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        .redirect-info h1 {
            margin-bottom: 20px;
            font-size: 2rem;
        }
        .redirect-info p {
            font-size: 1.2rem;
            margin: 10px 0;
        }
        .redirect-info a {
            color: #4facfe;
            text-decoration: none;
            font-weight: bold;
        }
        .redirect-info a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="redirect-info">
        <h1>🚀 正在跳转到系统更新页面</h1>
        <p>如果页面没有自动跳转，请点击下方链接：</p>
        <p><a href="http://localhost:8078/index.html">点击这里访问更新页面</a></p>
        <p>或直接访问：<strong>http://localhost:8078/index.html</strong></p>
    </div>
    
    <script>
        // 备用跳转方式
        setTimeout(function() {
            window.location.href = 'http://localhost:8078/index.html';
        }, 3000);
    </script>
</body>
</html>
