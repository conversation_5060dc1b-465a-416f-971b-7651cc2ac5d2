@echo off
chcp 65001 > nul
echo ========================================
echo        后端Spring Boot启动问题修复
echo ========================================
echo.
echo 🔍 检测到错误：ClassNotFoundException: com.ruoyi.system.domain.SysConfig
echo.
echo 🛠️ 正在执行修复步骤...
echo.

REM 检查是否在正确的目录
if not exist "ruoyi-admin" (
    echo ❌ 错误：未找到ruoyi-admin目录
    echo 请确保在项目根目录下运行此脚本
    pause
    exit /b 1
)

echo 📂 当前目录：%CD%
echo.

echo 🧹 步骤1：清理Maven缓存和编译文件...
if exist "ruoyi-admin\target" rmdir /s /q "ruoyi-admin\target"
if exist "ruoyi-system\target" rmdir /s /q "ruoyi-system\target"
if exist "ruoyi-common\target" rmdir /s /q "ruoyi-common\target"
if exist "ruoyi-framework\target" rmdir /s /q "ruoyi-framework\target"
if exist "ruoyi-flowable\target" rmdir /s /q "ruoyi-flowable\target"
if exist "ruoyi-generator\target" rmdir /s /q "ruoyi-generator\target"
if exist "ruoyi-job\target" rmdir /s /q "ruoyi-job\target"
if exist "ruoyi-oss\target" rmdir /s /q "ruoyi-oss\target"
if exist "ruoyi-demo\target" rmdir /s /q "ruoyi-demo\target"
if exist "ruoyi-oa\target" rmdir /s /q "ruoyi-oa\target"
if exist "ruoyi-sms\target" rmdir /s /q "ruoyi-sms\target"
echo ✅ 清理完成
echo.

echo 🔄 步骤2：重新编译项目...
echo.
echo 正在执行：mvn clean compile -Dmaven.test.skip=true
echo.
mvn clean compile -Dmaven.test.skip=true

if errorlevel 1 (
    echo.
    echo ❌ Maven编译失败！
    echo.
    echo 🔧 尝试替代方案：
    echo 1. 检查Maven是否正确安装
    echo 2. 检查网络连接（下载依赖）
    echo 3. 清理本地Maven仓库缓存
    echo.
    echo 💡 手动执行命令：
    echo    mvn clean install -Dmaven.test.skip=true
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功！
echo.

echo 🗄️ 步骤3：检查数据库连接...
echo.
echo 💡 请确保：
echo    1. MySQL数据库已启动
echo    2. 数据库连接配置正确
echo    3. 数据库中已导入SQL脚本
echo.

echo 🚀 步骤4：启动Spring Boot应用...
echo.
echo 正在启动后端服务器...
echo.

cd ruoyi-admin
mvn spring-boot:run -Dmaven.test.skip=true

echo.
echo 🎉 如果看到此消息，说明后端服务器启动成功！
echo 🌐 后端API地址：http://localhost:8078
echo 📊 管理界面：http://localhost:8078/swagger-ui/index.html
echo.
pause
