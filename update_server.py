#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import http.server
import socketserver
import os
import urllib.request
import json
from urllib.parse import urlparse

class UpdateHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/index' or self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            try:
                with open('ruoyi-admin/src/main/resources/static/index.html', 'r', encoding='utf-8') as f:
                    content = f.read()
                self.wfile.write(content.encode('utf-8'))
            except Exception as e:
                error_html = f"""
                <!DOCTYPE html>
                <html>
                <head><title>更新页面</title><meta charset="utf-8"></head>
                <body>
                <h1>🚀 系统更新页面</h1>
                <p>正在准备更新页面...</p>
                <p>请确保后端服务(8078端口)已启动</p>
                <p><a href="http://localhost:8078/index.html">直接访问后端更新页面</a></p>
                <p>错误信息: {str(e)}</p>
                </body>
                </html>
                """
                self.wfile.write(error_html.encode('utf-8'))
        elif self.path.startswith('/system/update'):
            # 代理到后端API
            try:
                backend_url = 'http://localhost:8078' + self.path
                req = urllib.request.Request(backend_url)
                response = urllib.request.urlopen(req)
                response_data = response.read()
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(response_data)
            except Exception as e:
                self.send_response(500)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                error_response = json.dumps({
                    'code': 500, 
                    'msg': '❌ 自动更新失败\n错误信息：\n\n认证失败，无法访问系统资源\n\n🛠️ 解决方案：\n请手动双击项目根目录的 update.bat 文件\n或在命令行中执行：git pull origin main\n然后重新构建前端：cd ruoyi-ui && npm run build:prod\n💡 如果问题持续，请联系技术支持\n\n详细错误: ' + str(e),
                    'data': None
                })
                self.wfile.write(error_response.encode('utf-8'))
        else:
            super().do_GET()

    def do_POST(self):
        if self.path.startswith('/system/update'):
            try:
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                backend_url = 'http://localhost:8078' + self.path
                req = urllib.request.Request(backend_url, data=post_data)
                req.add_header('Content-Type', 'application/json')
                
                response = urllib.request.urlopen(req)
                response_data = response.read()
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(response_data)
            except Exception as e:
                self.send_response(500)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                error_response = json.dumps({'code': 500, 'msg': 'API Error: ' + str(e)})
                self.wfile.write(error_response.encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()

if __name__ == "__main__":
    PORT = 8080
    try:
        with socketserver.TCPServer(("", PORT), UpdateHandler) as httpd:
            print(f"✅ 更新服务已启动: http://localhost:{PORT}/index")
            print(f"🔄 后端API代理: http://localhost:8078")
            print("🛑 按Ctrl+C停止服务")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
