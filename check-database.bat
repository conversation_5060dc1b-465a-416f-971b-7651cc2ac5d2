@echo off
chcp 65001 > nul
echo ========================================
echo           数据库连接检查工具
echo ========================================
echo.

echo 🗄️ 检查数据库配置...
echo.

REM 检查配置文件
if exist "ruoyi-admin\src\main\resources\application-dev.yml" (
    echo ✅ 找到开发环境配置文件
    echo.
    echo 📋 数据库配置信息：
    echo ----------------------------------------
    findstr /C:"url:" ruoyi-admin\src\main\resources\application-dev.yml
    findstr /C:"username:" ruoyi-admin\src\main\resources\application-dev.yml
    findstr /C:"password:" ruoyi-admin\src\main\resources\application-dev.yml
    echo ----------------------------------------
    echo.
) else (
    echo ❌ 未找到开发环境配置文件
    echo 📂 路径：ruoyi-admin\src\main\resources\application-dev.yml
)

echo 🔍 检查MySQL服务状态...
echo.

REM 检查MySQL服务
sc query mysql > nul 2>&1
if errorlevel 1 (
    echo ❌ MySQL服务未安装或未启动
    echo.
    echo 💡 请执行以下操作：
    echo    1. 安装MySQL数据库
    echo    2. 启动MySQL服务
    echo    3. 创建数据库和用户
) else (
    echo ✅ MySQL服务正在运行
)

echo.
echo 🔌 检查数据库连接端口...
netstat -an | findstr ":3306" > nul
if errorlevel 1 (
    echo ❌ 端口3306无连接 - MySQL可能未启动
) else (
    echo ✅ 端口3306有连接 - MySQL正在监听
)

echo.
echo 📊 数据库诊断建议：
echo ========================================
echo.
echo 如果数据库连接失败，请检查：
echo.
echo 1. 📥 MySQL安装和启动：
echo    • 确保MySQL服务已安装并启动
echo    • 检查服务状态：services.msc
echo.
echo 2. 🔧 数据库配置：
echo    • 数据库地址：localhost:3306
echo    • 数据库名称：ry-flowable
echo    • 用户名和密码是否正确
echo.
echo 3. 📋 SQL脚本导入：
echo    • 检查script/sql目录下的SQL文件
echo    • 确保基础数据已导入
echo.
echo 4. 🔐 权限设置：
echo    • 确保数据库用户有足够权限
echo    • 检查防火墙设置
echo.
echo 🚀 快速修复命令：
echo    net start mysql          (启动MySQL服务)
echo    mysql -u root -p         (连接数据库)
echo.

pause
