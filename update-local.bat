@echo off
chcp 65001 >nul
echo ================================================
echo 🚀 工程项目进度及成本控制管理系统 - 自动更新工具
echo ================================================
echo.

echo 📋 检查更新状态...
git status --porcelain
if %errorlevel% neq 0 (
    echo ❌ Git状态检查失败，请确认当前目录是Git仓库
    goto error
)

echo.
echo 🔄 正在获取最新代码...
git pull origin main
if %errorlevel% neq 0 (
    echo ❌ 代码更新失败，可能存在冲突或网络问题
    goto error
)

echo.
echo 🏗️ 正在编译后端...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo ❌ 后端编译失败
    goto error
)

echo.
echo 🎨 正在构建前端...
cd ruoyi-ui
call npm run build:prod
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    cd ..
    goto error
)
cd ..

echo.
echo ✅ 更新完成！
echo 💡 提示：需要重启服务器以应用更改
echo ================================================
pause
exit /b 0

:error
echo.
echo ================================================
echo ❌ 更新过程中出现错误
echo 💡 请检查以下几点：
echo   1. 网络连接是否正常
echo   2. Git仓库状态是否干净
echo   3. Maven和Node.js环境是否配置正确
echo ================================================
pause
exit /b 1
